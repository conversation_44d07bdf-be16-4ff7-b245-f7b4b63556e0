import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import SweetAlertUtils from '../utils/sweetAlert';

const LoginPage: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const { login, isAuthenticated, isLoading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (isAuthenticated) {
      navigate('/');
    }
  }, [isAuthenticated, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email || !password) {
      SweetAlertUtils.warning('กรุณากรอกข้อมูล', 'กรุณากรอกอีเมลและรหัสผ่าน');
      return;
    }

    const success = await login({ email, password });
    if (success) {
      navigate('/');
    }
  };

  const handleDemoLogin = async (role: 'admin' | 'member') => {
    const demoCredentials = {
      admin: { email: '<EMAIL>', password: 'admin123' },
      member: { email: '<EMAIL>', password: 'member123' }
    };

    const credentials = demoCredentials[role];
    setEmail(credentials.email);
    setPassword(credentials.password);

    const success = await login(credentials);
    if (success) {
      navigate('/');
    }
  };

  return (
    <div className="login-page" style={{ minHeight: '100vh' }}>
      <div className="login-box">
        <div className="card card-outline card-primary">
          <div className="card-header text-center">
            <a href="/" className="h1">
              <b>สหกรณ์</b>ร้านค้า
            </a>
          </div>
          <div className="card-body">
            <p className="login-box-msg">เข้าสู่ระบบเพื่อเริ่มต้นใช้งาน</p>

            <form onSubmit={handleSubmit}>
              <div className="input-group mb-3">
                <input
                  type="email"
                  className="form-control"
                  placeholder="อีเมล"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={isLoading}
                />
                <div className="input-group-append">
                  <div className="input-group-text">
                    <span className="fas fa-envelope"></span>
                  </div>
                </div>
              </div>

              <div className="input-group mb-3">
                <input
                  type={showPassword ? 'text' : 'password'}
                  className="form-control"
                  placeholder="รหัสผ่าน"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  disabled={isLoading}
                />
                <div className="input-group-append">
                  <div 
                    className="input-group-text"
                    style={{ cursor: 'pointer' }}
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    <span className={`fas ${showPassword ? 'fa-eye-slash' : 'fa-eye'}`}></span>
                  </div>
                </div>
              </div>

              <div className="row">
                <div className="col-8">
                  <div className="icheck-primary">
                    <input 
                      type="checkbox" 
                      id="remember"
                      checked={showPassword}
                      onChange={(e) => setShowPassword(e.target.checked)}
                    />
                    <label htmlFor="remember">
                      แสดงรหัสผ่าน
                    </label>
                  </div>
                </div>
                <div className="col-4">
                  <button 
                    type="submit" 
                    className="btn btn-primary btn-block"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <i className="fas fa-spinner fa-spin mr-2"></i>
                        กำลังเข้าสู่ระบบ...
                      </>
                    ) : (
                      'เข้าสู่ระบบ'
                    )}
                  </button>
                </div>
              </div>
            </form>

            <div className="social-auth-links text-center mt-2 mb-3">
              <p>- หรือ -</p>
              <button 
                className="btn btn-block btn-success"
                onClick={() => handleDemoLogin('admin')}
                disabled={isLoading}
              >
                <i className="fas fa-user-shield mr-2"></i>
                เข้าสู่ระบบในฐานะผู้ดูแล (Demo)
              </button>
              <button 
                className="btn btn-block btn-info"
                onClick={() => handleDemoLogin('member')}
                disabled={isLoading}
              >
                <i className="fas fa-user mr-2"></i>
                เข้าสู่ระบบในฐานะสมาชิก (Demo)
              </button>
            </div>

            <p className="mb-1">
              <a href="/forgot-password">ลืมรหัสผ่าน?</a>
            </p>
            <p className="mb-0">
              <a href="/register" className="text-center">สมัครสมาชิกใหม่</a>
            </p>
          </div>
        </div>
      </div>

      <style>{`
        .login-page {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .login-box {
          width: 360px;
          margin: auto;
        }

        .card {
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
          border: none;
          border-radius: 10px;
        }

        .card-header {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border-radius: 10px 10px 0 0 !important;
        }

        .card-header a {
          color: white !important;
          text-decoration: none;
        }

        .btn-primary {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border: none;
        }

        .btn-primary:hover {
          background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }

        .input-group-text {
          background-color: #f8f9fa;
          border-color: #ced4da;
        }

        .form-control:focus {
          border-color: #667eea;
          box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
      `}</style>
    </div>
  );
};

export default LoginPage;
