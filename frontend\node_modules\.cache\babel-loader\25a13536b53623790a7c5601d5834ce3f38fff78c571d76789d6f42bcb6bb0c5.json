{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\coop\\\\frontend\\\\src\\\\pages\\\\DashboardPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { apiService } from '../services/api';\nimport SweetAlertUtils from '../utils/sweetAlert';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DashboardPage = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [stats, setStats] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [recentSales, setRecentSales] = useState([]);\n  const [currentTime, setCurrentTime] = useState(new Date());\n  useEffect(() => {\n    fetchDashboardStats();\n\n    // Update time every second\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n    return () => clearInterval(timer);\n  }, []);\n  const fetchDashboardStats = async () => {\n    try {\n      setLoading(true);\n      const response = await apiService.getDashboardStats();\n      if (response.success) {\n        setStats(response.data);\n      } else {\n        SweetAlertUtils.error('เกิดข้อผิดพลาด', 'ไม่สามารถโหลดข้อมูลแดชบอร์ดได้');\n      }\n    } catch (error) {\n      console.error('Error fetching dashboard stats:', error);\n      // Set mock data for demo\n      setStats({\n        totalMembers: 150,\n        totalProducts: 85,\n        totalSales: 125000,\n        totalProfit: 25000,\n        todaySales: 5500,\n        todayProfit: 1100,\n        lowStockProducts: 8,\n        recentSales: []\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('th-TH', {\n      style: 'currency',\n      currency: 'THB'\n    }).format(amount);\n  };\n  const formatNumber = num => {\n    return new Intl.NumberFormat('th-TH').format(num);\n  };\n  const formatDateTime = date => {\n    return new Intl.DateTimeFormat('th-TH', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit',\n      second: '2-digit'\n    }).format(date);\n  };\n  const getGreeting = () => {\n    const hour = currentTime.getHours();\n    if (hour < 12) return 'สวัสดีตอนเช้า';\n    if (hour < 17) return 'สวัสดีตอนบ่าย';\n    return 'สวัสดีตอนเย็น';\n  };\n  const refreshData = () => {\n    fetchDashboardStats();\n    SweetAlertUtils.toast('success', 'รีเฟรชข้อมูลแล้ว');\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"content-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container-fluid\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row mb-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-sm-6\",\n            children: /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"m-0\",\n              children: \"\\u0E41\\u0E14\\u0E0A\\u0E1A\\u0E2D\\u0E23\\u0E4C\\u0E14\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"content\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container-fluid\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner-border text-primary\",\n              role: \"status\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sr-only\",\n                children: \"\\u0E01\\u0E33\\u0E25\\u0E31\\u0E07\\u0E42\\u0E2B\\u0E25\\u0E14...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"content-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container-fluid\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row mb-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-sm-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"m-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-tachometer-alt mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this), \"\\u0E41\\u0E14\\u0E0A\\u0E1A\\u0E2D\\u0E23\\u0E4C\\u0E14\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted mb-0\",\n              children: [getGreeting(), \", \", user === null || user === void 0 ? void 0 : user.name, \" | \", formatDateTime(currentTime)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-sm-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"float-sm-right\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn btn-primary btn-sm mr-2\",\n                onClick: refreshData,\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-sync-alt mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 19\n                }, this), \"\\u0E23\\u0E35\\u0E40\\u0E1F\\u0E23\\u0E0A\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"ol\", {\n                className: \"breadcrumb mb-0\",\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"breadcrumb-item\",\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: \"/\",\n                    children: \"\\u0E2B\\u0E19\\u0E49\\u0E32\\u0E41\\u0E23\\u0E01\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 134,\n                    columnNumber: 51\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"breadcrumb-item active\",\n                  children: \"\\u0E41\\u0E14\\u0E0A\\u0E1A\\u0E2D\\u0E23\\u0E4C\\u0E14\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container-fluid\",\n        children: [stats && stats.lowStockProducts > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"alert alert-warning alert-dismissible\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"close\",\n                \"data-dismiss\": \"alert\",\n                \"aria-hidden\": \"true\",\n                children: \"\\xD7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"icon fas fa-exclamation-triangle\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 23\n                }, this), \" \\u0E41\\u0E08\\u0E49\\u0E07\\u0E40\\u0E15\\u0E37\\u0E2D\\u0E19!\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 19\n              }, this), \"\\u0E21\\u0E35\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\\u0E17\\u0E35\\u0E48\\u0E40\\u0E2B\\u0E25\\u0E37\\u0E2D\\u0E19\\u0E49\\u0E2D\\u0E22 \", stats.lowStockProducts, \" \\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23 \\u0E01\\u0E23\\u0E38\\u0E13\\u0E32\\u0E40\\u0E15\\u0E34\\u0E21\\u0E2A\\u0E15\\u0E4A\\u0E2D\\u0E01\", /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"/products?filter=low-stock\",\n                className: \"btn btn-sm btn-warning ml-2\",\n                children: \"\\u0E14\\u0E39\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-lg-3 col-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"small-box bg-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"inner\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: stats ? formatNumber(stats.totalMembers) : '0'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"\\u0E2A\\u0E21\\u0E32\\u0E0A\\u0E34\\u0E01\\u0E17\\u0E31\\u0E49\\u0E07\\u0E2B\\u0E21\\u0E14\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"icon\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-users\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"/members\",\n                className: \"small-box-footer\",\n                children: [\"\\u0E14\\u0E39\\u0E23\\u0E32\\u0E22\\u0E25\\u0E30\\u0E40\\u0E2D\\u0E35\\u0E22\\u0E14 \", /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-arrow-circle-right\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 32\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-lg-3 col-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"small-box bg-success\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"inner\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: stats ? formatNumber(stats.totalProducts) : '0'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\\u0E17\\u0E31\\u0E49\\u0E07\\u0E2B\\u0E21\\u0E14\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"icon\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-box\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"/products\",\n                className: \"small-box-footer\",\n                children: [\"\\u0E14\\u0E39\\u0E23\\u0E32\\u0E22\\u0E25\\u0E30\\u0E40\\u0E2D\\u0E35\\u0E22\\u0E14 \", /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-arrow-circle-right\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 32\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-lg-3 col-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"small-box bg-warning\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"inner\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: stats ? formatCurrency(stats.todaySales) : '฿0'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"\\u0E22\\u0E2D\\u0E14\\u0E02\\u0E32\\u0E22\\u0E27\\u0E31\\u0E19\\u0E19\\u0E35\\u0E49\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"icon\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-shopping-cart\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"/sales\",\n                className: \"small-box-footer\",\n                children: [\"\\u0E14\\u0E39\\u0E23\\u0E32\\u0E22\\u0E25\\u0E30\\u0E40\\u0E2D\\u0E35\\u0E22\\u0E14 \", /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-arrow-circle-right\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 32\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-lg-3 col-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"small-box bg-danger\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"inner\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: stats ? formatCurrency(stats.todayProfit) : '฿0'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"\\u0E01\\u0E33\\u0E44\\u0E23\\u0E27\\u0E31\\u0E19\\u0E19\\u0E35\\u0E49\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"icon\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-chart-line\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"/reports/sales\",\n                className: \"small-box-footer\",\n                children: [\"\\u0E14\\u0E39\\u0E23\\u0E32\\u0E22\\u0E25\\u0E30\\u0E40\\u0E2D\\u0E35\\u0E22\\u0E14 \", /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-arrow-circle-right\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 32\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-header\",\n                children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"card-title\",\n                  children: \"\\u0E2A\\u0E23\\u0E38\\u0E1B\\u0E22\\u0E2D\\u0E14\\u0E02\\u0E32\\u0E22\\u0E41\\u0E25\\u0E30\\u0E01\\u0E33\\u0E44\\u0E23\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-body\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"col-md-6\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"info-box\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"info-box-icon bg-primary\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-money-bill-wave\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 237,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 236,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"info-box-content\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"info-box-text\",\n                          children: \"\\u0E22\\u0E2D\\u0E14\\u0E02\\u0E32\\u0E22\\u0E23\\u0E27\\u0E21\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 240,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"info-box-number\",\n                          children: stats ? formatCurrency(stats.totalSales) : '฿0'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 241,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 239,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 235,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 234,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"col-md-6\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"info-box\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"info-box-icon bg-success\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-chart-pie\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 250,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 249,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"info-box-content\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"info-box-text\",\n                          children: \"\\u0E01\\u0E33\\u0E44\\u0E23\\u0E23\\u0E27\\u0E21\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 253,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"info-box-number\",\n                          children: stats ? formatCurrency(stats.totalProfit) : '฿0'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 254,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 252,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 248,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 247,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-header\",\n                children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"card-title\",\n                  children: \"\\u0E01\\u0E32\\u0E23\\u0E41\\u0E08\\u0E49\\u0E07\\u0E40\\u0E15\\u0E37\\u0E2D\\u0E19\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-body\",\n                children: stats && stats.lowStockProducts > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"alert alert-warning\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"icon fas fa-exclamation-triangle\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 273,\n                      columnNumber: 27\n                    }, this), \" \\u0E41\\u0E08\\u0E49\\u0E07\\u0E40\\u0E15\\u0E37\\u0E2D\\u0E19!\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 273,\n                    columnNumber: 23\n                  }, this), \"\\u0E21\\u0E35\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\\u0E17\\u0E35\\u0E48\\u0E40\\u0E2B\\u0E25\\u0E37\\u0E2D\\u0E19\\u0E49\\u0E2D\\u0E22 \", stats.lowStockProducts, \" \\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 275,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: \"/products?filter=low-stock\",\n                    className: \"btn btn-sm btn-warning mt-2\",\n                    children: \"\\u0E14\\u0E39\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 276,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"alert alert-success\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"icon fas fa-check\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 282,\n                      columnNumber: 27\n                    }, this), \" \\u0E14\\u0E35\\u0E40\\u0E22\\u0E35\\u0E48\\u0E22\\u0E21!\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 23\n                  }, this), \"\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\\u0E17\\u0E38\\u0E01\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\\u0E21\\u0E35\\u0E2A\\u0E15\\u0E4A\\u0E2D\\u0E01\\u0E40\\u0E1E\\u0E35\\u0E22\\u0E07\\u0E1E\\u0E2D\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-header\",\n                children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"card-title\",\n                  children: \"\\u0E01\\u0E32\\u0E23\\u0E14\\u0E33\\u0E40\\u0E19\\u0E34\\u0E19\\u0E01\\u0E32\\u0E23\\u0E14\\u0E48\\u0E27\\u0E19\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-body\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"col-md-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: \"/sales/new\",\n                      className: \"btn btn-primary btn-block\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-plus mr-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 302,\n                        columnNumber: 25\n                      }, this), \"\\u0E02\\u0E32\\u0E22\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 301,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 300,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"col-md-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: \"/members/new\",\n                      className: \"btn btn-success btn-block\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-user-plus mr-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 308,\n                        columnNumber: 25\n                      }, this), \"\\u0E40\\u0E1E\\u0E34\\u0E48\\u0E21\\u0E2A\\u0E21\\u0E32\\u0E0A\\u0E34\\u0E01\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 307,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 306,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"col-md-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: \"/products/new\",\n                      className: \"btn btn-info btn-block\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-box mr-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 314,\n                        columnNumber: 25\n                      }, this), \"\\u0E40\\u0E1E\\u0E34\\u0E48\\u0E21\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 313,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"col-md-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: \"/reports\",\n                      className: \"btn btn-warning btn-block\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-chart-bar mr-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 320,\n                        columnNumber: 25\n                      }, this), \"\\u0E14\\u0E39\\u0E23\\u0E32\\u0E22\\u0E07\\u0E32\\u0E19\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 319,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 318,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(DashboardPage, \"k0LWi4qVxwVwKQqBxeeJb5+xgVc=\", false, function () {\n  return [useAuth];\n});\n_c = DashboardPage;\nexport default DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "apiService", "Sweet<PERSON>lertUtils", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DashboardPage", "_s", "user", "stats", "setStats", "loading", "setLoading", "recentSales", "setRecentSales", "currentTime", "setCurrentTime", "Date", "fetchDashboardStats", "timer", "setInterval", "clearInterval", "response", "getDashboardStats", "success", "data", "error", "console", "totalMembers", "totalProducts", "totalSales", "totalProfit", "todaySales", "todayProfit", "lowStockProducts", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "formatNumber", "num", "formatDateTime", "date", "DateTimeFormat", "year", "month", "day", "hour", "minute", "second", "getGreeting", "getHours", "refreshData", "toast", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "role", "name", "onClick", "href", "type", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/coop/frontend/src/pages/DashboardPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { DashboardStats, Sale } from '../types';\nimport { apiService } from '../services/api';\nimport SweetAlertUtils from '../utils/sweetAlert';\nimport { useAuth } from '../context/AuthContext';\n\nconst DashboardPage: React.FC = () => {\n  const { user } = useAuth();\n  const [stats, setStats] = useState<DashboardStats | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [recentSales, setRecentSales] = useState<Sale[]>([]);\n  const [currentTime, setCurrentTime] = useState(new Date());\n\n  useEffect(() => {\n    fetchDashboardStats();\n\n    // Update time every second\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, []);\n\n  const fetchDashboardStats = async () => {\n    try {\n      setLoading(true);\n      const response = await apiService.getDashboardStats();\n      if (response.success) {\n        setStats(response.data as DashboardStats);\n      } else {\n        SweetAlertUtils.error('เกิดข้อผิดพลาด', 'ไม่สามารถโหลดข้อมูลแดชบอร์ดได้');\n      }\n    } catch (error) {\n      console.error('Error fetching dashboard stats:', error);\n      // Set mock data for demo\n      setStats({\n        totalMembers: 150,\n        totalProducts: 85,\n        totalSales: 125000,\n        totalProfit: 25000,\n        todaySales: 5500,\n        todayProfit: 1100,\n        lowStockProducts: 8,\n        recentSales: []\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('th-TH', {\n      style: 'currency',\n      currency: 'THB'\n    }).format(amount);\n  };\n\n  const formatNumber = (num: number) => {\n    return new Intl.NumberFormat('th-TH').format(num);\n  };\n\n  const formatDateTime = (date: Date) => {\n    return new Intl.DateTimeFormat('th-TH', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit',\n      second: '2-digit'\n    }).format(date);\n  };\n\n  const getGreeting = () => {\n    const hour = currentTime.getHours();\n    if (hour < 12) return 'สวัสดีตอนเช้า';\n    if (hour < 17) return 'สวัสดีตอนบ่าย';\n    return 'สวัสดีตอนเย็น';\n  };\n\n  const refreshData = () => {\n    fetchDashboardStats();\n    SweetAlertUtils.toast('success', 'รีเฟรชข้อมูลแล้ว');\n  };\n\n  if (loading) {\n    return (\n      <div className=\"content-header\">\n        <div className=\"container-fluid\">\n          <div className=\"row mb-2\">\n            <div className=\"col-sm-6\">\n              <h1 className=\"m-0\">แดชบอร์ด</h1>\n            </div>\n          </div>\n        </div>\n        <section className=\"content\">\n          <div className=\"container-fluid\">\n            <div className=\"d-flex justify-content-center\">\n              <div className=\"spinner-border text-primary\" role=\"status\">\n                <span className=\"sr-only\">กำลังโหลด...</span>\n              </div>\n            </div>\n          </div>\n        </section>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      {/* Content Header */}\n      <div className=\"content-header\">\n        <div className=\"container-fluid\">\n          <div className=\"row mb-2\">\n            <div className=\"col-sm-6\">\n              <h1 className=\"m-0\">\n                <i className=\"fas fa-tachometer-alt mr-2\"></i>\n                แดชบอร์ด\n              </h1>\n              <p className=\"text-muted mb-0\">\n                {getGreeting()}, {user?.name} | {formatDateTime(currentTime)}\n              </p>\n            </div>\n            <div className=\"col-sm-6\">\n              <div className=\"float-sm-right\">\n                <button\n                  className=\"btn btn-primary btn-sm mr-2\"\n                  onClick={refreshData}\n                >\n                  <i className=\"fas fa-sync-alt mr-1\"></i>\n                  รีเฟรช\n                </button>\n                <ol className=\"breadcrumb mb-0\">\n                  <li className=\"breadcrumb-item\"><a href=\"/\">หน้าแรก</a></li>\n                  <li className=\"breadcrumb-item active\">แดชบอร์ด</li>\n                </ol>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <section className=\"content\">\n        <div className=\"container-fluid\">\n          {/* Alert Section */}\n          {stats && stats.lowStockProducts > 0 && (\n            <div className=\"row\">\n              <div className=\"col-12\">\n                <div className=\"alert alert-warning alert-dismissible\">\n                  <button type=\"button\" className=\"close\" data-dismiss=\"alert\" aria-hidden=\"true\">×</button>\n                  <h5><i className=\"icon fas fa-exclamation-triangle\"></i> แจ้งเตือน!</h5>\n                  มีสินค้าที่เหลือน้อย {stats.lowStockProducts} รายการ กรุณาเติมสต๊อก\n                  <a href=\"/products?filter=low-stock\" className=\"btn btn-sm btn-warning ml-2\">\n                    ดูรายการสินค้า\n                  </a>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Small boxes (Stat box) */}\n          <div className=\"row\">\n            <div className=\"col-lg-3 col-6\">\n              <div className=\"small-box bg-info\">\n                <div className=\"inner\">\n                  <h3>{stats ? formatNumber(stats.totalMembers) : '0'}</h3>\n                  <p>สมาชิกทั้งหมด</p>\n                </div>\n                <div className=\"icon\">\n                  <i className=\"fas fa-users\"></i>\n                </div>\n                <a href=\"/members\" className=\"small-box-footer\">\n                  ดูรายละเอียด <i className=\"fas fa-arrow-circle-right\"></i>\n                </a>\n              </div>\n            </div>\n\n            <div className=\"col-lg-3 col-6\">\n              <div className=\"small-box bg-success\">\n                <div className=\"inner\">\n                  <h3>{stats ? formatNumber(stats.totalProducts) : '0'}</h3>\n                  <p>สินค้าทั้งหมด</p>\n                </div>\n                <div className=\"icon\">\n                  <i className=\"fas fa-box\"></i>\n                </div>\n                <a href=\"/products\" className=\"small-box-footer\">\n                  ดูรายละเอียด <i className=\"fas fa-arrow-circle-right\"></i>\n                </a>\n              </div>\n            </div>\n\n            <div className=\"col-lg-3 col-6\">\n              <div className=\"small-box bg-warning\">\n                <div className=\"inner\">\n                  <h3>{stats ? formatCurrency(stats.todaySales) : '฿0'}</h3>\n                  <p>ยอดขายวันนี้</p>\n                </div>\n                <div className=\"icon\">\n                  <i className=\"fas fa-shopping-cart\"></i>\n                </div>\n                <a href=\"/sales\" className=\"small-box-footer\">\n                  ดูรายละเอียด <i className=\"fas fa-arrow-circle-right\"></i>\n                </a>\n              </div>\n            </div>\n\n            <div className=\"col-lg-3 col-6\">\n              <div className=\"small-box bg-danger\">\n                <div className=\"inner\">\n                  <h3>{stats ? formatCurrency(stats.todayProfit) : '฿0'}</h3>\n                  <p>กำไรวันนี้</p>\n                </div>\n                <div className=\"icon\">\n                  <i className=\"fas fa-chart-line\"></i>\n                </div>\n                <a href=\"/reports/sales\" className=\"small-box-footer\">\n                  ดูรายละเอียด <i className=\"fas fa-arrow-circle-right\"></i>\n                </a>\n              </div>\n            </div>\n          </div>\n\n          {/* Second row */}\n          <div className=\"row\">\n            <div className=\"col-md-6\">\n              <div className=\"card\">\n                <div className=\"card-header\">\n                  <h3 className=\"card-title\">สรุปยอดขายและกำไร</h3>\n                </div>\n                <div className=\"card-body\">\n                  <div className=\"row\">\n                    <div className=\"col-md-6\">\n                      <div className=\"info-box\">\n                        <span className=\"info-box-icon bg-primary\">\n                          <i className=\"fas fa-money-bill-wave\"></i>\n                        </span>\n                        <div className=\"info-box-content\">\n                          <span className=\"info-box-text\">ยอดขายรวม</span>\n                          <span className=\"info-box-number\">\n                            {stats ? formatCurrency(stats.totalSales) : '฿0'}\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"col-md-6\">\n                      <div className=\"info-box\">\n                        <span className=\"info-box-icon bg-success\">\n                          <i className=\"fas fa-chart-pie\"></i>\n                        </span>\n                        <div className=\"info-box-content\">\n                          <span className=\"info-box-text\">กำไรรวม</span>\n                          <span className=\"info-box-number\">\n                            {stats ? formatCurrency(stats.totalProfit) : '฿0'}\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"col-md-6\">\n              <div className=\"card\">\n                <div className=\"card-header\">\n                  <h3 className=\"card-title\">การแจ้งเตือน</h3>\n                </div>\n                <div className=\"card-body\">\n                  {stats && stats.lowStockProducts > 0 ? (\n                    <div className=\"alert alert-warning\">\n                      <h5><i className=\"icon fas fa-exclamation-triangle\"></i> แจ้งเตือน!</h5>\n                      มีสินค้าที่เหลือน้อย {stats.lowStockProducts} รายการ\n                      <br />\n                      <a href=\"/products?filter=low-stock\" className=\"btn btn-sm btn-warning mt-2\">\n                        ดูรายการสินค้า\n                      </a>\n                    </div>\n                  ) : (\n                    <div className=\"alert alert-success\">\n                      <h5><i className=\"icon fas fa-check\"></i> ดีเยี่ยม!</h5>\n                      สินค้าทุกรายการมีสต๊อกเพียงพอ\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Quick Actions */}\n          <div className=\"row\">\n            <div className=\"col-12\">\n              <div className=\"card\">\n                <div className=\"card-header\">\n                  <h3 className=\"card-title\">การดำเนินการด่วน</h3>\n                </div>\n                <div className=\"card-body\">\n                  <div className=\"row\">\n                    <div className=\"col-md-3\">\n                      <a href=\"/sales/new\" className=\"btn btn-primary btn-block\">\n                        <i className=\"fas fa-plus mr-2\"></i>\n                        ขายสินค้า\n                      </a>\n                    </div>\n                    <div className=\"col-md-3\">\n                      <a href=\"/members/new\" className=\"btn btn-success btn-block\">\n                        <i className=\"fas fa-user-plus mr-2\"></i>\n                        เพิ่มสมาชิก\n                      </a>\n                    </div>\n                    <div className=\"col-md-3\">\n                      <a href=\"/products/new\" className=\"btn btn-info btn-block\">\n                        <i className=\"fas fa-box mr-2\"></i>\n                        เพิ่มสินค้า\n                      </a>\n                    </div>\n                    <div className=\"col-md-3\">\n                      <a href=\"/reports\" className=\"btn btn-warning btn-block\">\n                        <i className=\"fas fa-chart-bar mr-2\"></i>\n                        ดูรายงาน\n                      </a>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n    </>\n  );\n};\n\nexport default DashboardPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAElD,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,OAAOC,eAAe,MAAM,qBAAqB;AACjD,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjD,MAAMC,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAM;IAAEC;EAAK,CAAC,GAAGP,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACQ,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAwB,IAAI,CAAC;EAC/D,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgB,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAS,EAAE,CAAC;EAC1D,MAAM,CAACkB,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAC,IAAIoB,IAAI,CAAC,CAAC,CAAC;EAE1DnB,SAAS,CAAC,MAAM;IACdoB,mBAAmB,CAAC,CAAC;;IAErB;IACA,MAAMC,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9BJ,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMI,aAAa,CAACF,KAAK,CAAC;EACnC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFN,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMU,QAAQ,GAAG,MAAMvB,UAAU,CAACwB,iBAAiB,CAAC,CAAC;MACrD,IAAID,QAAQ,CAACE,OAAO,EAAE;QACpBd,QAAQ,CAACY,QAAQ,CAACG,IAAsB,CAAC;MAC3C,CAAC,MAAM;QACLzB,eAAe,CAAC0B,KAAK,CAAC,gBAAgB,EAAE,gCAAgC,CAAC;MAC3E;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD;MACAhB,QAAQ,CAAC;QACPkB,YAAY,EAAE,GAAG;QACjBC,aAAa,EAAE,EAAE;QACjBC,UAAU,EAAE,MAAM;QAClBC,WAAW,EAAE,KAAK;QAClBC,UAAU,EAAE,IAAI;QAChBC,WAAW,EAAE,IAAI;QACjBC,gBAAgB,EAAE,CAAC;QACnBrB,WAAW,EAAE;MACf,CAAC,CAAC;IACJ,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuB,cAAc,GAAIC,MAAc,IAAK;IACzC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,MAAMM,YAAY,GAAIC,GAAW,IAAK;IACpC,OAAO,IAAIN,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAACG,MAAM,CAACE,GAAG,CAAC;EACnD,CAAC;EAED,MAAMC,cAAc,GAAIC,IAAU,IAAK;IACrC,OAAO,IAAIR,IAAI,CAACS,cAAc,CAAC,OAAO,EAAE;MACtCC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE,SAAS;MACjBC,MAAM,EAAE;IACV,CAAC,CAAC,CAACX,MAAM,CAACI,IAAI,CAAC;EACjB,CAAC;EAED,MAAMQ,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMH,IAAI,GAAGnC,WAAW,CAACuC,QAAQ,CAAC,CAAC;IACnC,IAAIJ,IAAI,GAAG,EAAE,EAAE,OAAO,eAAe;IACrC,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,eAAe;IACrC,OAAO,eAAe;EACxB,CAAC;EAED,MAAMK,WAAW,GAAGA,CAAA,KAAM;IACxBrC,mBAAmB,CAAC,CAAC;IACrBlB,eAAe,CAACwD,KAAK,CAAC,SAAS,EAAE,kBAAkB,CAAC;EACtD,CAAC;EAED,IAAI7C,OAAO,EAAE;IACX,oBACER,OAAA;MAAKsD,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BvD,OAAA;QAAKsD,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BvD,OAAA;UAAKsD,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBvD,OAAA;YAAKsD,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvBvD,OAAA;cAAIsD,SAAS,EAAC,KAAK;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN3D,OAAA;QAASsD,SAAS,EAAC,SAAS;QAAAC,QAAA,eAC1BvD,OAAA;UAAKsD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BvD,OAAA;YAAKsD,SAAS,EAAC,+BAA+B;YAAAC,QAAA,eAC5CvD,OAAA;cAAKsD,SAAS,EAAC,6BAA6B;cAACM,IAAI,EAAC,QAAQ;cAAAL,QAAA,eACxDvD,OAAA;gBAAMsD,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAEV;EAEA,oBACE3D,OAAA,CAAAE,SAAA;IAAAqD,QAAA,gBAEEvD,OAAA;MAAKsD,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7BvD,OAAA;QAAKsD,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BvD,OAAA;UAAKsD,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBvD,OAAA;YAAKsD,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBvD,OAAA;cAAIsD,SAAS,EAAC,KAAK;cAAAC,QAAA,gBACjBvD,OAAA;gBAAGsD,SAAS,EAAC;cAA4B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,oDAEhD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL3D,OAAA;cAAGsD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,GAC3BL,WAAW,CAAC,CAAC,EAAC,IAAE,EAAC7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwD,IAAI,EAAC,KAAG,EAACpB,cAAc,CAAC7B,WAAW,CAAC;YAAA;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN3D,OAAA;YAAKsD,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvBvD,OAAA;cAAKsD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BvD,OAAA;gBACEsD,SAAS,EAAC,6BAA6B;gBACvCQ,OAAO,EAAEV,WAAY;gBAAAG,QAAA,gBAErBvD,OAAA;kBAAGsD,SAAS,EAAC;gBAAsB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,wCAE1C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT3D,OAAA;gBAAIsD,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC7BvD,OAAA;kBAAIsD,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,eAACvD,OAAA;oBAAG+D,IAAI,EAAC,GAAG;oBAAAR,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5D3D,OAAA;kBAAIsD,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3D,OAAA;MAASsD,SAAS,EAAC,SAAS;MAAAC,QAAA,eAC1BvD,OAAA;QAAKsD,SAAS,EAAC,iBAAiB;QAAAC,QAAA,GAE7BjD,KAAK,IAAIA,KAAK,CAACyB,gBAAgB,GAAG,CAAC,iBAClC/B,OAAA;UAAKsD,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClBvD,OAAA;YAAKsD,SAAS,EAAC,QAAQ;YAAAC,QAAA,eACrBvD,OAAA;cAAKsD,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDvD,OAAA;gBAAQgE,IAAI,EAAC,QAAQ;gBAACV,SAAS,EAAC,OAAO;gBAAC,gBAAa,OAAO;gBAAC,eAAY,MAAM;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1F3D,OAAA;gBAAAuD,QAAA,gBAAIvD,OAAA;kBAAGsD,SAAS,EAAC;gBAAkC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,4DAAW;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,6HACnD,EAACrD,KAAK,CAACyB,gBAAgB,EAAC,4HAC7C,eAAA/B,OAAA;gBAAG+D,IAAI,EAAC,4BAA4B;gBAACT,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAC;cAE7E;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGD3D,OAAA;UAAKsD,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBvD,OAAA;YAAKsD,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7BvD,OAAA;cAAKsD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCvD,OAAA;gBAAKsD,SAAS,EAAC,OAAO;gBAAAC,QAAA,gBACpBvD,OAAA;kBAAAuD,QAAA,EAAKjD,KAAK,GAAGiC,YAAY,CAACjC,KAAK,CAACmB,YAAY,CAAC,GAAG;gBAAG;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACzD3D,OAAA;kBAAAuD,QAAA,EAAG;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eACN3D,OAAA;gBAAKsD,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnBvD,OAAA;kBAAGsD,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACN3D,OAAA;gBAAG+D,IAAI,EAAC,UAAU;gBAACT,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,GAAC,2EACjC,eAAAvD,OAAA;kBAAGsD,SAAS,EAAC;gBAA2B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3D,OAAA;YAAKsD,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7BvD,OAAA;cAAKsD,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnCvD,OAAA;gBAAKsD,SAAS,EAAC,OAAO;gBAAAC,QAAA,gBACpBvD,OAAA;kBAAAuD,QAAA,EAAKjD,KAAK,GAAGiC,YAAY,CAACjC,KAAK,CAACoB,aAAa,CAAC,GAAG;gBAAG;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1D3D,OAAA;kBAAAuD,QAAA,EAAG;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eACN3D,OAAA;gBAAKsD,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnBvD,OAAA;kBAAGsD,SAAS,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eACN3D,OAAA;gBAAG+D,IAAI,EAAC,WAAW;gBAACT,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,GAAC,2EAClC,eAAAvD,OAAA;kBAAGsD,SAAS,EAAC;gBAA2B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3D,OAAA;YAAKsD,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7BvD,OAAA;cAAKsD,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnCvD,OAAA;gBAAKsD,SAAS,EAAC,OAAO;gBAAAC,QAAA,gBACpBvD,OAAA;kBAAAuD,QAAA,EAAKjD,KAAK,GAAG0B,cAAc,CAAC1B,KAAK,CAACuB,UAAU,CAAC,GAAG;gBAAI;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1D3D,OAAA;kBAAAuD,QAAA,EAAG;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACN3D,OAAA;gBAAKsD,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnBvD,OAAA;kBAAGsD,SAAS,EAAC;gBAAsB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eACN3D,OAAA;gBAAG+D,IAAI,EAAC,QAAQ;gBAACT,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,GAAC,2EAC/B,eAAAvD,OAAA;kBAAGsD,SAAS,EAAC;gBAA2B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3D,OAAA;YAAKsD,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7BvD,OAAA;cAAKsD,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAClCvD,OAAA;gBAAKsD,SAAS,EAAC,OAAO;gBAAAC,QAAA,gBACpBvD,OAAA;kBAAAuD,QAAA,EAAKjD,KAAK,GAAG0B,cAAc,CAAC1B,KAAK,CAACwB,WAAW,CAAC,GAAG;gBAAI;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3D3D,OAAA;kBAAAuD,QAAA,EAAG;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eACN3D,OAAA;gBAAKsD,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnBvD,OAAA;kBAAGsD,SAAS,EAAC;gBAAmB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eACN3D,OAAA;gBAAG+D,IAAI,EAAC,gBAAgB;gBAACT,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,GAAC,2EACvC,eAAAvD,OAAA;kBAAGsD,SAAS,EAAC;gBAA2B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN3D,OAAA;UAAKsD,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBvD,OAAA;YAAKsD,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvBvD,OAAA;cAAKsD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBvD,OAAA;gBAAKsD,SAAS,EAAC,aAAa;gBAAAC,QAAA,eAC1BvD,OAAA;kBAAIsD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,eACN3D,OAAA;gBAAKsD,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACxBvD,OAAA;kBAAKsD,SAAS,EAAC,KAAK;kBAAAC,QAAA,gBAClBvD,OAAA;oBAAKsD,SAAS,EAAC,UAAU;oBAAAC,QAAA,eACvBvD,OAAA;sBAAKsD,SAAS,EAAC,UAAU;sBAAAC,QAAA,gBACvBvD,OAAA;wBAAMsD,SAAS,EAAC,0BAA0B;wBAAAC,QAAA,eACxCvD,OAAA;0BAAGsD,SAAS,EAAC;wBAAwB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC,eACP3D,OAAA;wBAAKsD,SAAS,EAAC,kBAAkB;wBAAAC,QAAA,gBAC/BvD,OAAA;0BAAMsD,SAAS,EAAC,eAAe;0BAAAC,QAAA,EAAC;wBAAS;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAChD3D,OAAA;0BAAMsD,SAAS,EAAC,iBAAiB;0BAAAC,QAAA,EAC9BjD,KAAK,GAAG0B,cAAc,CAAC1B,KAAK,CAACqB,UAAU,CAAC,GAAG;wBAAI;0BAAA6B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5C,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN3D,OAAA;oBAAKsD,SAAS,EAAC,UAAU;oBAAAC,QAAA,eACvBvD,OAAA;sBAAKsD,SAAS,EAAC,UAAU;sBAAAC,QAAA,gBACvBvD,OAAA;wBAAMsD,SAAS,EAAC,0BAA0B;wBAAAC,QAAA,eACxCvD,OAAA;0BAAGsD,SAAS,EAAC;wBAAkB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChC,CAAC,eACP3D,OAAA;wBAAKsD,SAAS,EAAC,kBAAkB;wBAAAC,QAAA,gBAC/BvD,OAAA;0BAAMsD,SAAS,EAAC,eAAe;0BAAAC,QAAA,EAAC;wBAAO;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAC9C3D,OAAA;0BAAMsD,SAAS,EAAC,iBAAiB;0BAAAC,QAAA,EAC9BjD,KAAK,GAAG0B,cAAc,CAAC1B,KAAK,CAACsB,WAAW,CAAC,GAAG;wBAAI;0BAAA4B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC7C,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3D,OAAA;YAAKsD,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvBvD,OAAA;cAAKsD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBvD,OAAA;gBAAKsD,SAAS,EAAC,aAAa;gBAAAC,QAAA,eAC1BvD,OAAA;kBAAIsD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC,eACN3D,OAAA;gBAAKsD,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACvBjD,KAAK,IAAIA,KAAK,CAACyB,gBAAgB,GAAG,CAAC,gBAClC/B,OAAA;kBAAKsD,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBAClCvD,OAAA;oBAAAuD,QAAA,gBAAIvD,OAAA;sBAAGsD,SAAS,EAAC;oBAAkC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,4DAAW;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,6HACnD,EAACrD,KAAK,CAACyB,gBAAgB,EAAC,uCAC7C,eAAA/B,OAAA;oBAAAwD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN3D,OAAA;oBAAG+D,IAAI,EAAC,4BAA4B;oBAACT,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,EAAC;kBAE7E;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,gBAEN3D,OAAA;kBAAKsD,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBAClCvD,OAAA;oBAAAuD,QAAA,gBAAIvD,OAAA;sBAAGsD,SAAS,EAAC;oBAAmB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,sDAAU;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,kLAE1D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN3D,OAAA;UAAKsD,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClBvD,OAAA;YAAKsD,SAAS,EAAC,QAAQ;YAAAC,QAAA,eACrBvD,OAAA;cAAKsD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBvD,OAAA;gBAAKsD,SAAS,EAAC,aAAa;gBAAAC,QAAA,eAC1BvD,OAAA;kBAAIsD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACN3D,OAAA;gBAAKsD,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACxBvD,OAAA;kBAAKsD,SAAS,EAAC,KAAK;kBAAAC,QAAA,gBAClBvD,OAAA;oBAAKsD,SAAS,EAAC,UAAU;oBAAAC,QAAA,eACvBvD,OAAA;sBAAG+D,IAAI,EAAC,YAAY;sBAACT,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,gBACxDvD,OAAA;wBAAGsD,SAAS,EAAC;sBAAkB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,0DAEtC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACN3D,OAAA;oBAAKsD,SAAS,EAAC,UAAU;oBAAAC,QAAA,eACvBvD,OAAA;sBAAG+D,IAAI,EAAC,cAAc;sBAACT,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,gBAC1DvD,OAAA;wBAAGsD,SAAS,EAAC;sBAAuB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,sEAE3C;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACN3D,OAAA;oBAAKsD,SAAS,EAAC,UAAU;oBAAAC,QAAA,eACvBvD,OAAA;sBAAG+D,IAAI,EAAC,eAAe;sBAACT,SAAS,EAAC,wBAAwB;sBAAAC,QAAA,gBACxDvD,OAAA;wBAAGsD,SAAS,EAAC;sBAAiB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,sEAErC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACN3D,OAAA;oBAAKsD,SAAS,EAAC,UAAU;oBAAAC,QAAA,eACvBvD,OAAA;sBAAG+D,IAAI,EAAC,UAAU;sBAACT,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,gBACtDvD,OAAA;wBAAGsD,SAAS,EAAC;sBAAuB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,oDAE3C;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA,eACV,CAAC;AAEP,CAAC;AAACvD,EAAA,CAtUID,aAAuB;EAAA,QACVL,OAAO;AAAA;AAAAmE,EAAA,GADpB9D,aAAuB;AAwU7B,eAAeA,aAAa;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}