"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.logout = exports.changePassword = exports.updateProfile = exports.getMe = exports.login = exports.register = void 0;
const User_1 = require("../models/User");
const jwt_1 = require("../utils/jwt");
const response_1 = require("../utils/response");
const errorHandler_1 = require("../middleware/errorHandler");
exports.register = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { email, password, name, role } = req.body;
    const existingUser = await User_1.User.findOne({ email });
    if (existingUser) {
        return (0, response_1.sendError)(res, 'อีเมลนี้มีอยู่ในระบบแล้ว', 400);
    }
    const user = await User_1.User.create({
        email,
        password,
        name,
        role: role || 'member'
    });
    const token = (0, jwt_1.generateToken)({
        userId: user._id,
        email: user.email,
        role: user.role
    });
    (0, response_1.sendCreated)(res, 'สร้างบัญชีผู้ใช้สำเร็จ', {
        user: {
            id: user._id,
            email: user.email,
            name: user.name,
            role: user.role,
            isActive: user.isActive
        },
        token
    });
});
exports.login = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { email, password } = req.body;
    const user = await User_1.User.findOne({ email }).select('+password');
    if (!user) {
        return (0, response_1.sendError)(res, 'อีเมลหรือรหัสผ่านไม่ถูกต้อง', 401);
    }
    if (!user.isActive) {
        return (0, response_1.sendError)(res, 'บัญชีผู้ใช้ถูกระงับ', 401);
    }
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
        return (0, response_1.sendError)(res, 'อีเมลหรือรหัสผ่านไม่ถูกต้อง', 401);
    }
    const token = (0, jwt_1.generateToken)({
        userId: user._id,
        email: user.email,
        role: user.role
    });
    (0, response_1.sendSuccess)(res, 'เข้าสู่ระบบสำเร็จ', {
        user: {
            id: user._id,
            email: user.email,
            name: user.name,
            role: user.role,
            isActive: user.isActive
        },
        token
    });
});
exports.getMe = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const user = req.user;
    (0, response_1.sendSuccess)(res, 'ดึงข้อมูลผู้ใช้สำเร็จ', {
        user: {
            id: user._id,
            email: user.email,
            name: user.name,
            role: user.role,
            isActive: user.isActive,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt
        }
    });
});
exports.updateProfile = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { name } = req.body;
    const userId = req.user._id;
    const user = await User_1.User.findByIdAndUpdate(userId, { name }, { new: true, runValidators: true });
    if (!user) {
        return (0, response_1.sendError)(res, 'ไม่พบผู้ใช้งาน', 404);
    }
    (0, response_1.sendSuccess)(res, 'อัปเดตข้อมูลส่วนตัวสำเร็จ', {
        user: {
            id: user._id,
            email: user.email,
            name: user.name,
            role: user.role,
            isActive: user.isActive
        }
    });
});
exports.changePassword = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user._id;
    const user = await User_1.User.findById(userId).select('+password');
    if (!user) {
        return (0, response_1.sendError)(res, 'ไม่พบผู้ใช้งาน', 404);
    }
    const isCurrentPasswordValid = await user.comparePassword(currentPassword);
    if (!isCurrentPasswordValid) {
        return (0, response_1.sendError)(res, 'รหัสผ่านปัจจุบันไม่ถูกต้อง', 400);
    }
    user.password = newPassword;
    await user.save();
    (0, response_1.sendSuccess)(res, 'เปลี่ยนรหัสผ่านสำเร็จ');
});
exports.logout = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    (0, response_1.sendSuccess)(res, 'ออกจากระบบสำเร็จ');
});
//# sourceMappingURL=authController.js.map