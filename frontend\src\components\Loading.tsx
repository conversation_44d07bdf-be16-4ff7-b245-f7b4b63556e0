import React from 'react';

interface LoadingProps {
  message?: string;
  fullScreen?: boolean;
}

const Loading: React.FC<LoadingProps> = ({ 
  message = 'กำลังโหลด...', 
  fullScreen = false 
}) => {
  const containerClass = fullScreen 
    ? 'position-fixed w-100 h-100 d-flex align-items-center justify-content-center bg-white'
    : 'd-flex align-items-center justify-content-center p-4';

  return (
    <div className={containerClass} style={{ zIndex: fullScreen ? 9999 : 'auto' }}>
      <div className="text-center">
        <div className="spinner-border text-primary mb-3" role="status" style={{ width: '3rem', height: '3rem' }}>
          <span className="sr-only">Loading...</span>
        </div>
        <div className="h5 text-muted">{message}</div>
      </div>
    </div>
  );
};

export default Loading;
