import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { User, LoginRequest } from '../types';
import { AuthUtils } from '../utils/auth';
import { apiService } from '../services/api';
import SweetAlertUtils from '../utils/sweetAlert';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

type AuthAction =
  | { type: 'LOGIN_START' }
  | { type: 'LOGIN_SUCCESS'; payload: User }
  | { type: 'LOGIN_FAILURE'; payload: string }
  | { type: 'LOGOUT' }
  | { type: 'SET_USER'; payload: User }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'CLEAR_ERROR' };

interface AuthContextType extends AuthState {
  login: (credentials: LoginRequest) => Promise<boolean>;
  logout: () => void;
  updateProfile: (data: Partial<User>) => Promise<boolean>;
  clearError: () => void;
}

const initialState: AuthState = {
  user: AuthUtils.getUser(),
  isAuthenticated: AuthUtils.isAuthenticated(),
  isLoading: false,
  error: null,
};

const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'LOGIN_START':
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        user: action.payload,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };
    case 'LOGIN_FAILURE':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: action.payload,
      };
    case 'LOGOUT':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      };
    case 'SET_USER':
      return {
        ...state,
        user: action.payload,
        isAuthenticated: true,
      };
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };
    default:
      return state;
  }
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check if user is still authenticated on app load
  useEffect(() => {
    const checkAuth = async () => {
      if (AuthUtils.isAuthenticated()) {
        try {
          dispatch({ type: 'SET_LOADING', payload: true });
          const response = await apiService.getProfile();
          if (response.success && response.data && (response.data as any).user) {
            dispatch({ type: 'SET_USER', payload: (response.data as any).user });
          } else {
            // Invalid token, logout
            logout();
          }
        } catch (error) {
          console.error('Auth check failed:', error);
          logout();
        } finally {
          dispatch({ type: 'SET_LOADING', payload: false });
        }
      }
    };

    checkAuth();
  }, []);

  const login = async (credentials: LoginRequest): Promise<boolean> => {
    try {
      dispatch({ type: 'LOGIN_START' });
      
      const response = await apiService.login(credentials.email, credentials.password);
      
      if (response.success && response.data) {
        const { user, token } = response.data as any;

        // Store in localStorage
        AuthUtils.login(token, user);
        
        // Update state
        dispatch({ type: 'LOGIN_SUCCESS', payload: user });
        
        SweetAlertUtils.toast('success', 'เข้าสู่ระบบสำเร็จ');
        return true;
      } else {
        dispatch({ type: 'LOGIN_FAILURE', payload: response.message || 'เข้าสู่ระบบไม่สำเร็จ' });
        SweetAlertUtils.error('เข้าสู่ระบบไม่สำเร็จ', response.message);
        return false;
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'เกิดข้อผิดพลาดในการเข้าสู่ระบบ';
      dispatch({ type: 'LOGIN_FAILURE', payload: errorMessage });
      SweetAlertUtils.error('เข้าสู่ระบบไม่สำเร็จ', errorMessage);
      return false;
    }
  };

  const logout = () => {
    AuthUtils.logout();
    dispatch({ type: 'LOGOUT' });
    SweetAlertUtils.toast('info', 'ออกจากระบบแล้ว');
  };

  const updateProfile = async (data: Partial<User>): Promise<boolean> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      const response = await apiService.updateProfile(data);
      
      if (response.success && response.data) {
        const updatedUser = (response.data as any).user;
        AuthUtils.setUser(updatedUser);
        dispatch({ type: 'SET_USER', payload: updatedUser });
        
        SweetAlertUtils.toast('success', 'อัปเดตข้อมูลสำเร็จ');
        return true;
      } else {
        SweetAlertUtils.error('อัปเดตข้อมูลไม่สำเร็จ', response.message);
        return false;
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'เกิดข้อผิดพลาดในการอัปเดตข้อมูล';
      SweetAlertUtils.error('อัปเดตข้อมูลไม่สำเร็จ', errorMessage);
      return false;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  const value: AuthContextType = {
    ...state,
    login,
    logout,
    updateProfile,
    clearError,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
