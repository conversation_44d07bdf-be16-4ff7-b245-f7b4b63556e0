const mongoose = require('mongoose');

const productSchema = new mongoose.Schema({
  productCode: {
    type: String,
    required: [true, 'รหัสสินค้าจำเป็นต้องระบุ'],
    unique: true,
    trim: true,
    uppercase: true,
    match: [/^P\d{6}$/, 'รหัสสินค้าต้องเป็นรูปแบบ P000001']
  },
  name: {
    type: String,
    required: [true, 'ชื่อสินค้าจำเป็นต้องระบุ'],
    trim: true,
    maxlength: [200, 'ชื่อสินค้าต้องไม่เกิน 200 ตัวอักษร']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [1000, 'รายละเอียดต้องไม่เกิน 1000 ตัวอักษร']
  },
  category: {
    type: String,
    required: [true, 'หมวดหมู่จำเป็นต้องระบุ'],
    trim: true,
    enum: [
      'เครื่องเขียน',
      'อุปกรณ์การเรียน',
      'เครื่องแต่งกาย',
      'อาหารและเครื่องดื่ม',
      'ขนมและของว่าง',
      'อุปกรณ์กีฬา',
      'หนังสือและสื่อการเรียน',
      'อิเล็กทรอนิกส์',
      'ของใช้ส่วนตัว',
      'อื่นๆ'
    ],
    default: 'อื่นๆ'
  },
  brand: {
    type: String,
    trim: true,
    maxlength: [100, 'ยี่ห้อต้องไม่เกิน 100 ตัวอักษร']
  },
  costPrice: {
    type: Number,
    required: [true, 'ราคาทุนจำเป็นต้องระบุ'],
    min: [0, 'ราคาทุนต้องไม่ติดลบ']
  },
  sellingPrice: {
    type: Number,
    required: [true, 'ราคาขายจำเป็นต้องระบุ'],
    min: [0, 'ราคาขายต้องไม่ติดลบ']
  },
  stock: {
    type: Number,
    required: [true, 'จำนวนสต๊อกจำเป็นต้องระบุ'],
    min: [0, 'จำนวนสต๊อกต้องไม่ติดลบ'],
    default: 0
  },
  minStock: {
    type: Number,
    required: [true, 'สต๊อกขั้นต่ำจำเป็นต้องระบุ'],
    min: [0, 'สต๊อกขั้นต่ำต้องไม่ติดลบ'],
    default: 5
  },
  maxStock: {
    type: Number,
    min: [0, 'สต๊อกสูงสุดต้องไม่ติดลบ'],
    default: 1000
  },
  unit: {
    type: String,
    required: [true, 'หน่วยจำเป็นต้องระบุ'],
    trim: true,
    enum: [
      'ชิ้น', 'อัน', 'ด้าม', 'เล่ม', 'แผ่น', 'ใบ', 'คู่', 'ชุด',
      'กล่อง', 'ห่อ', 'ถุง', 'ขวด', 'กระป๋อง', 'แพ็ค',
      'กิโลกรัม', 'กรัม', 'ลิตร', 'มิลลิลิตร',
      'เมตร', 'เซนติเมตร', 'อื่นๆ'
    ],
    default: 'ชิ้น'
  },
  barcode: {
    type: String,
    trim: true,
    unique: true,
    sparse: true,
    match: [/^\d{8,13}$/, 'บาร์โค้ดต้องเป็นตัวเลข 8-13 หลัก']
  },
  supplier: {
    name: String,
    contact: String,
    phone: String,
    email: String,
    address: String
  },
  images: [{
    url: String,
    alt: String,
    isPrimary: {
      type: Boolean,
      default: false
    }
  }],
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  isFeatured: {
    type: Boolean,
    default: false
  },
  weight: {
    type: Number,
    min: [0, 'น้ำหนักต้องไม่ติดลบ']
  },
  dimensions: {
    length: Number,
    width: Number,
    height: Number,
    unit: {
      type: String,
      enum: ['cm', 'mm', 'inch'],
      default: 'cm'
    }
  },
  expiryDate: {
    type: Date
  },
  manufacturingDate: {
    type: Date
  },
  totalSold: {
    type: Number,
    default: 0,
    min: [0, 'ยอดขายต้องไม่ติดลบ']
  },
  totalRevenue: {
    type: Number,
    default: 0,
    min: [0, 'รายได้ต้องไม่ติดลบ']
  },
  lastSoldDate: {
    type: Date
  },
  notes: {
    type: String,
    trim: true,
    maxlength: [1000, 'หมายเหตุต้องไม่เกิน 1000 ตัวอักษร']
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
productSchema.index({ productCode: 1 });
productSchema.index({ name: 1 });
productSchema.index({ category: 1 });
productSchema.index({ barcode: 1 });
productSchema.index({ isActive: 1 });
productSchema.index({ stock: 1 });
productSchema.index({ sellingPrice: 1 });
productSchema.index({ createdAt: -1 });
productSchema.index({ totalSold: -1 });

// Compound indexes
productSchema.index({ category: 1, isActive: 1 });
productSchema.index({ stock: 1, minStock: 1 });

// Virtual for profit per unit
productSchema.virtual('profitPerUnit').get(function() {
  return this.sellingPrice - this.costPrice;
});

// Virtual for profit margin percentage
productSchema.virtual('profitMargin').get(function() {
  if (this.costPrice === 0) return 0;
  return ((this.sellingPrice - this.costPrice) / this.costPrice) * 100;
});

// Virtual for stock status
productSchema.virtual('stockStatus').get(function() {
  if (this.stock === 0) return 'out_of_stock';
  if (this.stock <= this.minStock) return 'low_stock';
  if (this.stock >= this.maxStock) return 'overstock';
  return 'in_stock';
});

// Virtual for total profit
productSchema.virtual('totalProfit').get(function() {
  return this.totalSold * this.profitPerUnit;
});

// Pre-save middleware to generate product code
productSchema.pre('save', async function(next) {
  if (!this.isNew || this.productCode) return next();
  
  try {
    // Find the last product code
    const lastProduct = await this.constructor.findOne(
      { productCode: { $regex: /^P\d{6}$/ } },
      { productCode: 1 },
      { sort: { productCode: -1 } }
    );
    
    let nextNumber = 1;
    if (lastProduct && lastProduct.productCode) {
      const lastNumber = parseInt(lastProduct.productCode.substring(1));
      nextNumber = lastNumber + 1;
    }
    
    this.productCode = `P${nextNumber.toString().padStart(6, '0')}`;
    next();
  } catch (error) {
    next(error);
  }
});

// Pre-save middleware to validate selling price
productSchema.pre('save', function(next) {
  if (this.sellingPrice < this.costPrice) {
    return next(new Error('ราคาขายต้องมากกว่าหรือเท่ากับราคาทุน'));
  }
  next();
});

// Instance method to update stock
productSchema.methods.updateStock = function(quantity, operation = 'add') {
  if (operation === 'add') {
    this.stock += quantity;
  } else if (operation === 'subtract') {
    this.stock = Math.max(0, this.stock - quantity);
  } else {
    this.stock = quantity;
  }
  return this.save();
};

// Instance method to record sale
productSchema.methods.recordSale = function(quantity, salePrice = null) {
  const price = salePrice || this.sellingPrice;
  this.stock = Math.max(0, this.stock - quantity);
  this.totalSold += quantity;
  this.totalRevenue += quantity * price;
  this.lastSoldDate = new Date();
  return this.save();
};

// Static method to find low stock products
productSchema.statics.findLowStock = function() {
  return this.find({
    isActive: true,
    $expr: { $lte: ['$stock', '$minStock'] }
  });
};

// Static method to find out of stock products
productSchema.statics.findOutOfStock = function() {
  return this.find({
    isActive: true,
    stock: 0
  });
};

// Static method to find by category
productSchema.statics.findByCategory = function(category) {
  return this.find({ category, isActive: true });
};

// Static method to get statistics
productSchema.statics.getStatistics = async function() {
  const stats = await this.aggregate([
    {
      $match: { isActive: true }
    },
    {
      $group: {
        _id: null,
        totalProducts: { $sum: 1 },
        totalStock: { $sum: '$stock' },
        totalValue: { $sum: { $multiply: ['$stock', '$costPrice'] } },
        totalRevenue: { $sum: '$totalRevenue' },
        avgPrice: { $avg: '$sellingPrice' },
        lowStockCount: {
          $sum: {
            $cond: [{ $lte: ['$stock', '$minStock'] }, 1, 0]
          }
        },
        outOfStockCount: {
          $sum: {
            $cond: [{ $eq: ['$stock', 0] }, 1, 0]
          }
        }
      }
    }
  ]);
  
  return stats[0] || {
    totalProducts: 0,
    totalStock: 0,
    totalValue: 0,
    totalRevenue: 0,
    avgPrice: 0,
    lowStockCount: 0,
    outOfStockCount: 0
  };
};

module.exports = mongoose.model('Product', productSchema);
