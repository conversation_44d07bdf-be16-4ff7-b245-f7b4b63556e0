import { Request, Response, NextFunction } from 'express';
import { body, validationResult, query } from 'express-validator';

// Generic validation error handler
export const handleValidationErrors = (req: Request, res: Response, next: NextFunction): Response | void => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'ข้อมูลไม่ถูกต้อง',
      errors: errors.array()
    });
  }
  next();
};

// User validation rules
export const validateUserRegistration = [
  body('email')
    .isEmail()
    .withMessage('รูปแบบอีเมลไม่ถูกต้อง')
    .normalizeEmail(),
  body('password')
    .isLength({ min: 6 })
    .withMessage('รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร'),
  body('name')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('ชื่อต้องมี 1-100 ตัวอักษร'),
  body('role')
    .optional()
    .isIn(['admin', 'member'])
    .withMessage('บทบาทไม่ถูกต้อง'),
  handleValidationErrors
];

export const validateUserLogin = [
  body('email')
    .isEmail()
    .withMessage('รูปแบบอีเมลไม่ถูกต้อง')
    .normalizeEmail(),
  body('password')
    .notEmpty()
    .withMessage('รหัสผ่านจำเป็นต้องระบุ'),
  handleValidationErrors
];

// Member validation rules
export const validateMember = [
  body('name')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('ชื่อสมาชิกต้องมี 1-100 ตัวอักษร'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('รูปแบบอีเมลไม่ถูกต้อง')
    .normalizeEmail(),
  body('phone')
    .optional()
    .matches(/^[0-9]{10}$/)
    .withMessage('เบอร์โทรศัพท์ต้องเป็นตัวเลข 10 หลัก'),
  body('address')
    .optional()
    .isLength({ max: 500 })
    .withMessage('ที่อยู่ต้องไม่เกิน 500 ตัวอักษร'),
  body('studentId')
    .optional()
    .isLength({ max: 20 })
    .withMessage('รหัสนักเรียนต้องไม่เกิน 20 ตัวอักษร'),
  body('class')
    .optional()
    .isLength({ max: 50 })
    .withMessage('ชั้นเรียนต้องไม่เกิน 50 ตัวอักษร'),
  body('shares')
    .optional()
    .isInt({ min: 0 })
    .withMessage('จำนวนหุ้นต้องเป็นจำนวนเต็มไม่น้อยกว่า 0'),
  handleValidationErrors
];

// Product validation rules
export const validateProduct = [
  body('name')
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('ชื่อสินค้าต้องมี 1-200 ตัวอักษร'),
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('รายละเอียดต้องไม่เกิน 1000 ตัวอักษร'),
  body('category')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('หมวดหมู่ต้องมี 1-100 ตัวอักษร'),
  body('costPrice')
    .isFloat({ min: 0 })
    .withMessage('ราคาทุนต้องเป็นตัวเลขไม่น้อยกว่า 0'),
  body('sellingPrice')
    .isFloat({ min: 0 })
    .withMessage('ราคาขายต้องเป็นตัวเลขไม่น้อยกว่า 0'),
  body('stock')
    .isInt({ min: 0 })
    .withMessage('จำนวนสต๊อกต้องเป็นจำนวนเต็มไม่น้อยกว่า 0'),
  body('minStock')
    .optional()
    .isInt({ min: 0 })
    .withMessage('สต๊อกขั้นต่ำต้องเป็นจำนวนเต็มไม่น้อยกว่า 0'),
  body('unit')
    .trim()
    .isLength({ min: 1, max: 20 })
    .withMessage('หน่วยนับต้องมี 1-20 ตัวอักษร'),
  handleValidationErrors
];

// Sale validation rules
export const validateSale = [
  body('memberId')
    .optional()
    .trim()
    .notEmpty()
    .withMessage('รหัสสมาชิกไม่ถูกต้อง'),
  body('items')
    .isArray({ min: 1 })
    .withMessage('ต้องมีรายการสินค้าอย่างน้อย 1 รายการ'),
  body('items.*.productId')
    .notEmpty()
    .withMessage('รหัสสินค้าจำเป็นต้องระบุ'),
  body('items.*.quantity')
    .isInt({ min: 1 })
    .withMessage('จำนวนต้องเป็นจำนวนเต็มมากกว่า 0'),
  body('paymentMethod')
    .optional()
    .isIn(['cash', 'transfer'])
    .withMessage('วิธีการชำระเงินไม่ถูกต้อง'),
  handleValidationErrors
];

// Pagination validation
export const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('หน้าต้องเป็นจำนวนเต็มมากกว่า 0'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('จำนวนรายการต่อหน้าต้องอยู่ระหว่าง 1-100'),
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('ลำดับการเรียงต้องเป็น asc หรือ desc'),
  handleValidationErrors
];

// Date range validation
export const validateDateRange = [
  query('startDate')
    .optional()
    .isISO8601()
    .withMessage('วันที่เริ่มต้นไม่ถูกต้อง'),
  query('endDate')
    .optional()
    .isISO8601()
    .withMessage('วันที่สิ้นสุดไม่ถูกต้อง'),
  handleValidationErrors
];
