[{"C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\context\\AuthContext.tsx": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\layouts\\AdminLayout.tsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\pages\\LoginPage.tsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\pages\\DashboardPage.tsx": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\utils\\auth.ts": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\utils\\sweetAlert.ts": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\services\\api.ts": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\pages\\MembersPage.tsx": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\pages\\ProductsPage.tsx": "12"}, {"size": 554, "mtime": 1749047294536, "results": "13", "hashOfConfig": "14"}, {"size": 425, "mtime": 1749047291296, "results": "15", "hashOfConfig": "14"}, {"size": 2962, "mtime": 1749050192924, "results": "16", "hashOfConfig": "14"}, {"size": 6267, "mtime": 1749049415414, "results": "17", "hashOfConfig": "14"}, {"size": 8994, "mtime": 1749050217972, "results": "18", "hashOfConfig": "14"}, {"size": 7070, "mtime": 1749049449120, "results": "19", "hashOfConfig": "14"}, {"size": 24862, "mtime": 1749050388098, "results": "20", "hashOfConfig": "14"}, {"size": 2150, "mtime": 1749047729143, "results": "21", "hashOfConfig": "14"}, {"size": 4332, "mtime": 1749047747529, "results": "22", "hashOfConfig": "14"}, {"size": 4387, "mtime": 1749047716706, "results": "23", "hashOfConfig": "14"}, {"size": 9196, "mtime": 1749050352576, "results": "24", "hashOfConfig": "14"}, {"size": 10961, "mtime": 1749050375527, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "62m5ik", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\context\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\layouts\\AdminLayout.tsx", ["62", "63", "64", "65", "66"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\pages\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\pages\\DashboardPage.tsx", ["67"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\utils\\auth.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\utils\\sweetAlert.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\pages\\MembersPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\pages\\ProductsPage.tsx", [], [], {"ruleId": "68", "severity": 1, "message": "69", "line": 47, "column": 13, "nodeType": "70", "endLine": 56, "endColumn": 14}, {"ruleId": "68", "severity": 1, "message": "69", "line": 68, "column": 13, "nodeType": "70", "endLine": 68, "endColumn": 69}, {"ruleId": "71", "severity": 1, "message": "72", "line": 115, "column": 15, "nodeType": "70", "endLine": 122, "endColumn": 17}, {"ruleId": "68", "severity": 1, "message": "69", "line": 175, "column": 17, "nodeType": "70", "endLine": 175, "endColumn": 50}, {"ruleId": "68", "severity": 1, "message": "69", "line": 224, "column": 39, "nodeType": "70", "endLine": 224, "endColumn": 51}, {"ruleId": "73", "severity": 1, "message": "74", "line": 2, "column": 26, "nodeType": "75", "messageId": "76", "endLine": 2, "endColumn": 30}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "@typescript-eslint/no-unused-vars", "'Sale' is defined but never used.", "Identifier", "unusedVar"]