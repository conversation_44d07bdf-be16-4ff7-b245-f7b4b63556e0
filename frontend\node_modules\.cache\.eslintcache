[{"C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\context\\AuthContext.tsx": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\layouts\\AdminLayout.tsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\pages\\LoginPage.tsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\pages\\DashboardPage.tsx": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\utils\\auth.ts": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\utils\\sweetAlert.ts": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\services\\api.ts": "10"}, {"size": 554, "mtime": 1749047294536, "results": "11", "hashOfConfig": "12"}, {"size": 425, "mtime": 1749047291296, "results": "13", "hashOfConfig": "12"}, {"size": 2361, "mtime": 1749047929287, "results": "14", "hashOfConfig": "12"}, {"size": 6267, "mtime": 1749049415414, "results": "15", "hashOfConfig": "12"}, {"size": 8354, "mtime": 1749047858401, "results": "16", "hashOfConfig": "12"}, {"size": 7070, "mtime": 1749049449120, "results": "17", "hashOfConfig": "12"}, {"size": 10525, "mtime": 1749049429191, "results": "18", "hashOfConfig": "12"}, {"size": 2150, "mtime": 1749047729143, "results": "19", "hashOfConfig": "12"}, {"size": 4332, "mtime": 1749047747529, "results": "20", "hashOfConfig": "12"}, {"size": 4387, "mtime": 1749047716706, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "62m5ik", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\context\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\layouts\\AdminLayout.tsx", ["52", "53", "54", "55", "56"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\pages\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\pages\\DashboardPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\utils\\auth.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\utils\\sweetAlert.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\services\\api.ts", [], [], {"ruleId": "57", "severity": 1, "message": "58", "line": 28, "column": 13, "nodeType": "59", "endLine": 37, "endColumn": 14}, {"ruleId": "57", "severity": 1, "message": "58", "line": 49, "column": 13, "nodeType": "59", "endLine": 49, "endColumn": 69}, {"ruleId": "60", "severity": 1, "message": "61", "line": 96, "column": 15, "nodeType": "59", "endLine": 103, "endColumn": 17}, {"ruleId": "57", "severity": 1, "message": "58", "line": 156, "column": 17, "nodeType": "59", "endLine": 156, "endColumn": 50}, {"ruleId": "57", "severity": 1, "message": "58", "line": 205, "column": 39, "nodeType": "59", "endLine": 205, "endColumn": 51}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop."]