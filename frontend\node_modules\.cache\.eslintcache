[{"C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\context\\AuthContext.tsx": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\layouts\\AdminLayout.tsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\pages\\LoginPage.tsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\pages\\DashboardPage.tsx": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\utils\\auth.ts": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\utils\\sweetAlert.ts": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\services\\api.ts": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\pages\\MembersPage.tsx": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\pages\\ProductsPage.tsx": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\components\\ErrorBoundary.tsx": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\components\\Loading.tsx": "14"}, {"size": 554, "mtime": 1749047294536, "results": "15", "hashOfConfig": "16"}, {"size": 425, "mtime": 1749047291296, "results": "17", "hashOfConfig": "16"}, {"size": 3074, "mtime": 1749050584811, "results": "18", "hashOfConfig": "16"}, {"size": 6476, "mtime": 1749050696198, "results": "19", "hashOfConfig": "16"}, {"size": 8994, "mtime": 1749050217972, "results": "20", "hashOfConfig": "16"}, {"size": 7070, "mtime": 1749049449120, "results": "21", "hashOfConfig": "16"}, {"size": 24862, "mtime": 1749050388098, "results": "22", "hashOfConfig": "16"}, {"size": 2150, "mtime": 1749047729143, "results": "23", "hashOfConfig": "16"}, {"size": 4332, "mtime": 1749047747529, "results": "24", "hashOfConfig": "16"}, {"size": 4387, "mtime": 1749047716706, "results": "25", "hashOfConfig": "16"}, {"size": 9196, "mtime": 1749050352576, "results": "26", "hashOfConfig": "16"}, {"size": 10961, "mtime": 1749050375527, "results": "27", "hashOfConfig": "16"}, {"size": 2865, "mtime": 1749050551873, "results": "28", "hashOfConfig": "16"}, {"size": 841, "mtime": 1749050613712, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "62m5ik", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\context\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\layouts\\AdminLayout.tsx", ["72", "73", "74", "75", "76"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\pages\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\pages\\DashboardPage.tsx", ["77"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\utils\\auth.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\utils\\sweetAlert.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\pages\\MembersPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\pages\\ProductsPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\components\\ErrorBoundary.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\components\\Loading.tsx", [], [], {"ruleId": "78", "severity": 1, "message": "79", "line": 47, "column": 13, "nodeType": "80", "endLine": 56, "endColumn": 14}, {"ruleId": "78", "severity": 1, "message": "79", "line": 68, "column": 13, "nodeType": "80", "endLine": 68, "endColumn": 69}, {"ruleId": "81", "severity": 1, "message": "82", "line": 115, "column": 15, "nodeType": "80", "endLine": 122, "endColumn": 17}, {"ruleId": "78", "severity": 1, "message": "79", "line": 175, "column": 17, "nodeType": "80", "endLine": 175, "endColumn": 50}, {"ruleId": "78", "severity": 1, "message": "79", "line": 224, "column": 39, "nodeType": "80", "endLine": 224, "endColumn": 51}, {"ruleId": "83", "severity": 1, "message": "84", "line": 2, "column": 26, "nodeType": "85", "messageId": "86", "endLine": 2, "endColumn": 30}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "@typescript-eslint/no-unused-vars", "'Sale' is defined but never used.", "Identifier", "unusedVar"]