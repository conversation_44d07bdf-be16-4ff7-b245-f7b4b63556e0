[{"C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\context\\AuthContext.tsx": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\layouts\\AdminLayout.tsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\pages\\LoginPage.tsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\pages\\DashboardPage.tsx": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\pages\\MembersPage.tsx": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\pages\\ProductsPage.tsx": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\components\\ErrorBoundary.tsx": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\components\\Loading.tsx": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\utils\\auth.ts": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\utils\\sweetAlert.ts": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\services\\api.ts": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\TestApp.tsx": "15"}, {"size": 554, "mtime": 1749055185512, "results": "16", "hashOfConfig": "17"}, {"size": 425, "mtime": 1749047291296, "results": "18", "hashOfConfig": "17"}, {"size": 3011, "mtime": 1749055199107, "results": "19", "hashOfConfig": "17"}, {"size": 6476, "mtime": 1749050696198, "results": "20", "hashOfConfig": "17"}, {"size": 8994, "mtime": 1749050217972, "results": "21", "hashOfConfig": "17"}, {"size": 7070, "mtime": 1749049449120, "results": "22", "hashOfConfig": "17"}, {"size": 24862, "mtime": 1749050388098, "results": "23", "hashOfConfig": "17"}, {"size": 9196, "mtime": 1749050352576, "results": "24", "hashOfConfig": "17"}, {"size": 10961, "mtime": 1749050375527, "results": "25", "hashOfConfig": "17"}, {"size": 2865, "mtime": 1749050551873, "results": "26", "hashOfConfig": "17"}, {"size": 841, "mtime": 1749050613712, "results": "27", "hashOfConfig": "17"}, {"size": 2150, "mtime": 1749047729143, "results": "28", "hashOfConfig": "17"}, {"size": 4332, "mtime": 1749047747529, "results": "29", "hashOfConfig": "17"}, {"size": 4387, "mtime": 1749047716706, "results": "30", "hashOfConfig": "17"}, {"size": 1368, "mtime": 1749055137560, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "62m5ik", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\context\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\layouts\\AdminLayout.tsx", ["77", "78", "79", "80", "81"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\pages\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\pages\\DashboardPage.tsx", ["82"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\pages\\MembersPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\pages\\ProductsPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\components\\ErrorBoundary.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\components\\Loading.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\utils\\auth.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\utils\\sweetAlert.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coop\\frontend\\src\\TestApp.tsx", [], [], {"ruleId": "83", "severity": 1, "message": "84", "line": 47, "column": 13, "nodeType": "85", "endLine": 56, "endColumn": 14}, {"ruleId": "83", "severity": 1, "message": "84", "line": 68, "column": 13, "nodeType": "85", "endLine": 68, "endColumn": 69}, {"ruleId": "86", "severity": 1, "message": "87", "line": 115, "column": 15, "nodeType": "85", "endLine": 122, "endColumn": 17}, {"ruleId": "83", "severity": 1, "message": "84", "line": 175, "column": 17, "nodeType": "85", "endLine": 175, "endColumn": 50}, {"ruleId": "83", "severity": 1, "message": "84", "line": 224, "column": 39, "nodeType": "85", "endLine": 224, "endColumn": 51}, {"ruleId": "88", "severity": 1, "message": "89", "line": 2, "column": 26, "nodeType": "90", "messageId": "91", "endLine": 2, "endColumn": 30}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "@typescript-eslint/no-unused-vars", "'Sale' is defined but never used.", "Identifier", "unusedVar"]