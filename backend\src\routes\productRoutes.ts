import { Router } from 'express';
import {
  getProducts,
  getProduct,
  createProduct,
  updateProduct,
  deleteProduct,
  getActiveProducts,
  getLowStockProducts
} from '../controllers/productController';
import { authenticate, authorize } from '../middleware/auth';
import { validateProduct, validatePagination } from '../middleware/validation';

const router = Router();

// All routes require authentication
router.use(authenticate);

// Public product routes (for all authenticated users)
router.get('/active', getActiveProducts);
router.get('/low-stock', getLowStockProducts);
router.get('/:id', getProduct);
router.get('/', validatePagination, getProducts);

// Admin only routes
router.post('/', authorize('admin'), validateProduct, createProduct);
router.put('/:id', authorize('admin'), validateProduct, updateProduct);
router.delete('/:id', authorize('admin'), deleteProduct);

export default router;
