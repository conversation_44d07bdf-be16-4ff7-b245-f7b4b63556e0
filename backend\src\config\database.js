const mongoose = require('mongoose');
require('dotenv').config();

class Database {
  constructor() {
    this.connection = null;
    this.isConnected = false;
  }

  async connect() {
    try {
      // MongoDB connection options
      const options = {
        useNewUrlParser: true,
        useUnifiedTopology: true,
        maxPoolSize: 10, // Maintain up to 10 socket connections
        serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds
        socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
        family: 4, // Use IPv4, skip trying IPv6
        bufferCommands: false, // Disable mongoose buffering
        bufferMaxEntries: 0 // Disable mongoose buffering
      };

      // Connect to MongoDB
      this.connection = await mongoose.connect(process.env.MONGODB_URI, options);
      this.isConnected = true;

      console.log(`✅ MongoDB Connected: ${this.connection.connection.host}`);
      console.log(`📊 Database: ${this.connection.connection.name}`);

      // Handle connection events
      this.setupEventHandlers();

      return this.connection;
    } catch (error) {
      console.error('❌ MongoDB connection error:', error.message);
      this.isConnected = false;
      throw error;
    }
  }

  setupEventHandlers() {
    const db = mongoose.connection;

    db.on('connected', () => {
      console.log('🔗 Mongoose connected to MongoDB');
      this.isConnected = true;
    });

    db.on('error', (error) => {
      console.error('❌ Mongoose connection error:', error);
      this.isConnected = false;
    });

    db.on('disconnected', () => {
      console.log('🔌 Mongoose disconnected from MongoDB');
      this.isConnected = false;
    });

    // Handle application termination
    process.on('SIGINT', async () => {
      await this.disconnect();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      await this.disconnect();
      process.exit(0);
    });
  }

  async disconnect() {
    try {
      if (this.connection) {
        await mongoose.connection.close();
        console.log('🔌 MongoDB connection closed');
        this.isConnected = false;
      }
    } catch (error) {
      console.error('❌ Error closing MongoDB connection:', error);
    }
  }

  async ping() {
    try {
      if (!this.isConnected) {
        throw new Error('Database not connected');
      }
      
      await mongoose.connection.db.admin().ping();
      return { success: true, message: 'Database connection is healthy' };
    } catch (error) {
      return { success: false, message: error.message };
    }
  }

  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      readyState: mongoose.connection.readyState,
      host: mongoose.connection.host,
      port: mongoose.connection.port,
      name: mongoose.connection.name
    };
  }

  async getStats() {
    try {
      if (!this.isConnected) {
        throw new Error('Database not connected');
      }

      const stats = await mongoose.connection.db.stats();
      return {
        success: true,
        data: {
          collections: stats.collections,
          dataSize: stats.dataSize,
          storageSize: stats.storageSize,
          indexes: stats.indexes,
          indexSize: stats.indexSize,
          objects: stats.objects
        }
      };
    } catch (error) {
      return { success: false, message: error.message };
    }
  }

  async createIndexes() {
    try {
      console.log('🔍 Creating database indexes...');
      
      // Import models to ensure indexes are created
      require('../models/User');
      require('../models/Member');
      require('../models/Product');

      // Create indexes for all models
      await mongoose.connection.db.collection('users').createIndex({ email: 1 }, { unique: true });
      await mongoose.connection.db.collection('members').createIndex({ memberCode: 1 }, { unique: true });
      await mongoose.connection.db.collection('products').createIndex({ productCode: 1 }, { unique: true });

      console.log('✅ Database indexes created successfully');
    } catch (error) {
      console.error('❌ Error creating indexes:', error);
    }
  }

  async seedData() {
    try {
      console.log('🌱 Seeding initial data...');
      
      const User = require('../models/User');
      const Member = require('../models/Member');
      const Product = require('../models/Product');

      // Create default admin user if not exists
      const adminExists = await User.findOne({ email: process.env.ADMIN_EMAIL });
      if (!adminExists) {
        await User.createAdmin({
          email: process.env.ADMIN_EMAIL,
          password: process.env.ADMIN_PASSWORD,
          name: process.env.ADMIN_NAME
        });
        console.log('👤 Default admin user created');
      }

      // Create sample members if none exist
      const memberCount = await Member.countDocuments();
      if (memberCount === 0) {
        const sampleMembers = [
          {
            name: 'นักเรียนตัวอย่าง 1',
            email: '<EMAIL>',
            phone: '0812345678',
            studentId: 'S001',
            grade: 'ม.1',
            class: '1/1',
            shares: 10,
            sharePrice: 100
          },
          {
            name: 'นักเรียนตัวอย่าง 2',
            email: '<EMAIL>',
            phone: '0823456789',
            studentId: 'S002',
            grade: 'ม.2',
            class: '2/1',
            shares: 5,
            sharePrice: 100
          }
        ];

        await Member.insertMany(sampleMembers);
        console.log('👥 Sample members created');
      }

      // Create sample products if none exist
      const productCount = await Product.countDocuments();
      if (productCount === 0) {
        const sampleProducts = [
          {
            name: 'ปากกาลูกลื่น',
            category: 'เครื่องเขียน',
            costPrice: 8,
            sellingPrice: 12,
            stock: 100,
            minStock: 10,
            unit: 'ด้าม',
            brand: 'Pilot'
          },
          {
            name: 'สมุดบันทึก',
            category: 'เครื่องเขียน',
            costPrice: 15,
            sellingPrice: 25,
            stock: 50,
            minStock: 5,
            unit: 'เล่ม',
            brand: 'Double A'
          },
          {
            name: 'ดินสอ 2B',
            category: 'เครื่องเขียน',
            costPrice: 5,
            sellingPrice: 8,
            stock: 200,
            minStock: 20,
            unit: 'ด้าม',
            brand: 'Steadtler'
          }
        ];

        await Product.insertMany(sampleProducts);
        console.log('📦 Sample products created');
      }

      console.log('✅ Data seeding completed');
    } catch (error) {
      console.error('❌ Error seeding data:', error);
    }
  }
}

// Create singleton instance
const database = new Database();

module.exports = database;
