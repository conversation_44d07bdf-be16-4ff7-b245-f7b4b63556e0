"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Sale = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const saleItemSchema = new mongoose_1.Schema({
    productId: {
        type: String,
        required: [true, 'รหัสสินค้าจำเป็นต้องระบุ']
    },
    productName: {
        type: String,
        required: [true, 'ชื่อสินค้าจำเป็นต้องระบุ']
    },
    quantity: {
        type: Number,
        required: [true, 'จำนวนจำเป็นต้องระบุ'],
        min: [1, 'จำนวนต้องมากกว่า 0']
    },
    costPrice: {
        type: Number,
        required: [true, 'ราคาทุนจำเป็นต้องระบุ'],
        min: [0, 'ราคาทุนต้องไม่น้อยกว่า 0']
    },
    sellingPrice: {
        type: Number,
        required: [true, 'ราคาขายจำเป็นต้องระบุ'],
        min: [0, 'ราคาขายต้องไม่น้อยกว่า 0']
    },
    totalCost: {
        type: Number,
        required: true
    },
    totalAmount: {
        type: Number,
        required: true
    },
    profit: {
        type: Number,
        required: true
    }
}, { _id: false });
const saleSchema = new mongoose_1.Schema({
    saleCode: {
        type: String,
        required: [true, 'รหัสการขายจำเป็นต้องระบุ'],
        unique: true,
        trim: true,
        uppercase: true
    },
    memberId: {
        type: String,
        trim: true
    },
    memberName: {
        type: String,
        trim: true
    },
    items: {
        type: [saleItemSchema],
        required: [true, 'รายการสินค้าจำเป็นต้องระบุ'],
        validate: {
            validator: function (items) {
                return items && items.length > 0;
            },
            message: 'ต้องมีรายการสินค้าอย่างน้อย 1 รายการ'
        }
    },
    totalAmount: {
        type: Number,
        required: [true, 'ยอดรวมจำเป็นต้องระบุ'],
        min: [0, 'ยอดรวมต้องไม่น้อยกว่า 0']
    },
    totalCost: {
        type: Number,
        required: [true, 'ต้นทุนรวมจำเป็นต้องระบุ'],
        min: [0, 'ต้นทุนรวมต้องไม่น้อยกว่า 0']
    },
    profit: {
        type: Number,
        required: [true, 'กำไรจำเป็นต้องระบุ']
    },
    paymentMethod: {
        type: String,
        enum: ['cash', 'transfer'],
        default: 'cash'
    },
    saleDate: {
        type: Date,
        default: Date.now
    },
    createdBy: {
        type: String,
        required: [true, 'ผู้สร้างรายการจำเป็นต้องระบุ']
    }
}, {
    timestamps: true
});
saleSchema.index({ saleCode: 1 });
saleSchema.index({ memberId: 1 });
saleSchema.index({ saleDate: 1 });
saleSchema.index({ createdBy: 1 });
saleSchema.index({ 'items.productId': 1 });
saleSchema.pre('save', async function (next) {
    if (!this.isNew || this.saleCode)
        return next();
    try {
        const today = new Date();
        const dateStr = today.toISOString().slice(0, 10).replace(/-/g, '');
        const count = await mongoose_1.default.model('Sale').countDocuments({
            saleDate: {
                $gte: new Date(today.getFullYear(), today.getMonth(), today.getDate()),
                $lt: new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1)
            }
        });
        this.saleCode = `S${dateStr}${String(count + 1).padStart(4, '0')}`;
        next();
    }
    catch (error) {
        next(error);
    }
});
saleSchema.pre('save', function (next) {
    if (this.items && this.items.length > 0) {
        this.totalAmount = this.items.reduce((sum, item) => {
            item.totalAmount = item.quantity * item.sellingPrice;
            item.totalCost = item.quantity * item.costPrice;
            item.profit = item.totalAmount - item.totalCost;
            return sum + item.totalAmount;
        }, 0);
        this.totalCost = this.items.reduce((sum, item) => sum + item.totalCost, 0);
        this.profit = this.totalAmount - this.totalCost;
    }
    next();
});
saleSchema.statics.findByDateRange = function (startDate, endDate) {
    return this.find({
        saleDate: {
            $gte: startDate,
            $lte: endDate
        }
    });
};
saleSchema.statics.findByMember = function (memberId) {
    return this.find({ memberId });
};
saleSchema.statics.getTodaySales = function () {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);
    return this.find({
        saleDate: {
            $gte: startOfDay,
            $lt: endOfDay
        }
    });
};
saleSchema.statics.getSalesStats = async function (startDate, endDate) {
    const match = {};
    if (startDate && endDate) {
        match.saleDate = { $gte: startDate, $lte: endDate };
    }
    const stats = await this.aggregate([
        { $match: match },
        {
            $group: {
                _id: null,
                totalSales: { $sum: '$totalAmount' },
                totalCost: { $sum: '$totalCost' },
                totalProfit: { $sum: '$profit' },
                salesCount: { $sum: 1 },
                avgSaleAmount: { $avg: '$totalAmount' }
            }
        }
    ]);
    return stats[0] || {
        totalSales: 0,
        totalCost: 0,
        totalProfit: 0,
        salesCount: 0,
        avgSaleAmount: 0
    };
};
exports.Sale = mongoose_1.default.model('Sale', saleSchema);
exports.default = exports.Sale;
//# sourceMappingURL=Sale.js.map