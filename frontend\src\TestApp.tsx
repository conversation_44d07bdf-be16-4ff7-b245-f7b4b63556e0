import React from 'react';

const TestApp: React.FC = () => {
  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>🎉 React App Test</h1>
      <div style={{ 
        backgroundColor: '#f8f9fa', 
        padding: '20px', 
        borderRadius: '8px',
        marginTop: '20px'
      }}>
        <h2>✅ React is Working!</h2>
        <p>Current time: {new Date().toLocaleString('th-TH')}</p>
        <p>Environment: {process.env.NODE_ENV}</p>
        <p>API URL: {process.env.REACT_APP_API_URL || 'http://localhost:5000/api'}</p>
        
        <div style={{ marginTop: '20px' }}>
          <h3>🔧 Test Features:</h3>
          <ul>
            <li>✅ TypeScript compilation</li>
            <li>✅ React rendering</li>
            <li>✅ CSS styling</li>
            <li>✅ Environment variables</li>
          </ul>
        </div>

        <div style={{ marginTop: '20px' }}>
          <button 
            onClick={() => alert('Button clicked!')}
            style={{
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              padding: '10px 20px',
              borderRadius: '5px',
              cursor: 'pointer'
            }}
          >
            Test Button
          </button>
        </div>
      </div>
    </div>
  );
};

export default TestApp;
