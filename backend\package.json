{"name": "student-coop-backend", "version": "1.0.0", "description": "Backend API for Student Cooperative System", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon src/index.ts", "build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["cooperative", "student", "api", "nodejs", "typescript"], "author": "Student Cooperative System", "license": "MIT", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "morgan": "^1.10.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.9", "@types/node": "^22.15.29", "concurrently": "^9.1.2", "nodemon": "^3.1.10", "rimraf": "^6.0.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}