import { Request, Response } from 'express';
export declare const getMembers: (req: Request, res: Response, next: import("express").NextFunction) => Promise<any>;
export declare const getMember: (req: Request, res: Response, next: import("express").NextFunction) => Promise<any>;
export declare const createMember: (req: Request, res: Response, next: import("express").NextFunction) => Promise<any>;
export declare const updateMember: (req: Request, res: Response, next: import("express").NextFunction) => Promise<any>;
export declare const deleteMember: (req: Request, res: Response, next: import("express").NextFunction) => Promise<any>;
export declare const getActiveMembers: (req: Request, res: Response, next: import("express").NextFunction) => Promise<any>;
export declare const searchMembers: (req: Request, res: Response, next: import("express").NextFunction) => Promise<any>;
//# sourceMappingURL=memberController.d.ts.map