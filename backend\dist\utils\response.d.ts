import { Response } from 'express';
export declare const sendSuccess: <T>(res: Response, message: string, data?: T, statusCode?: number) => Response;
export declare const sendError: (res: Response, message: string, statusCode?: number, error?: string) => Response;
export declare const sendCreated: <T>(res: Response, message: string, data?: T) => Response;
export declare const sendNotFound: (res: Response, message?: string) => Response;
export declare const sendUnauthorized: (res: Response, message?: string) => Response;
export declare const sendForbidden: (res: Response, message?: string) => Response;
export declare const sendValidationError: (res: Response, message?: string, errors?: any) => Response;
//# sourceMappingURL=response.d.ts.map