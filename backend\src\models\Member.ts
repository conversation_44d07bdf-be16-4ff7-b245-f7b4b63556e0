import mongoose, { Schema } from 'mongoose';
import { IMember } from '../types';

// Extend IMember interface to include methods
interface IMemberMethods {
  addPurchase(amount: number): Promise<IMember>;
  addDividend(amount: number): Promise<IMember>;
  updateShares(shares: number): Promise<IMember>;
}

interface IMemberModel extends mongoose.Model<IMember, {}, IMemberMethods> {
  findActive(): mongoose.Query<IMember[], IMember>;
  findByCode(code: string): mongoose.Query<IMember | null, IMember>;
}

const memberSchema = new Schema<IMember>({
  memberCode: {
    type: String,
    required: [true, 'รหัสสมาชิกจำเป็นต้องระบุ'],
    unique: true,
    trim: true,
    uppercase: true
  },
  name: {
    type: String,
    required: [true, 'ชื่อสมาชิกจำเป็นต้องระบุ'],
    trim: true,
    maxlength: [100, 'ชื่อต้องไม่เกิน 100 ตัวอักษร']
  },
  email: {
    type: String,
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'รูปแบบอีเมลไม่ถูกต้อง']
  },
  phone: {
    type: String,
    trim: true,
    match: [/^[0-9]{10}$/, 'เบอร์โทรศัพท์ต้องเป็นตัวเลข 10 หลัก']
  },
  address: {
    type: String,
    trim: true,
    maxlength: [500, 'ที่อยู่ต้องไม่เกิน 500 ตัวอักษร']
  },
  studentId: {
    type: String,
    trim: true,
    maxlength: [20, 'รหัสนักเรียนต้องไม่เกิน 20 ตัวอักษร']
  },
  class: {
    type: String,
    trim: true,
    maxlength: [50, 'ชั้นเรียนต้องไม่เกิน 50 ตัวอักษร']
  },
  shares: {
    type: Number,
    default: 0,
    min: [0, 'จำนวนหุ้นต้องไม่น้อยกว่า 0']
  },
  totalPurchase: {
    type: Number,
    default: 0,
    min: [0, 'ยอดซื้อสะสมต้องไม่น้อยกว่า 0']
  },
  totalDividend: {
    type: Number,
    default: 0,
    min: [0, 'ปันผลสะสมต้องไม่น้อยกว่า 0']
  },
  isActive: {
    type: Boolean,
    default: true
  },
  joinDate: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Indexes for better performance
memberSchema.index({ memberCode: 1 });
memberSchema.index({ name: 1 });
memberSchema.index({ email: 1 });
memberSchema.index({ studentId: 1 });
memberSchema.index({ isActive: 1 });
memberSchema.index({ joinDate: 1 });

// Generate member code before saving
memberSchema.pre('save', async function(next) {
  if (!this.isNew || this.memberCode) return next();
  
  try {
    const count = await mongoose.model('Member').countDocuments();
    this.memberCode = `M${String(count + 1).padStart(6, '0')}`;
    next();
  } catch (error) {
    next(error as Error);
  }
});

// Virtual for full member info
memberSchema.virtual('memberInfo').get(function() {
  return `${this.memberCode} - ${this.name}`;
});

// Static methods
memberSchema.statics.findActive = function() {
  return this.find({ isActive: true });
};

memberSchema.statics.findByCode = function(code: string) {
  return this.findOne({ memberCode: code.toUpperCase() });
};

// Instance methods
memberSchema.methods.addPurchase = function(amount: number) {
  this.totalPurchase += amount;
  return this.save();
};

memberSchema.methods.addDividend = function(amount: number) {
  this.totalDividend += amount;
  return this.save();
};

memberSchema.methods.updateShares = function(shares: number) {
  this.shares = shares;
  return this.save();
};

export const Member = mongoose.model<IMember, IMemberModel>('Member', memberSchema);
export default Member;
