{"ast": null, "code": "export const AUTH_TOKEN_KEY = 'token';\nexport const AUTH_USER_KEY = 'user';\nexport class AuthUtils {\n  static setToken(token) {\n    localStorage.setItem(AUTH_TOKEN_KEY, token);\n  }\n  static getToken() {\n    return localStorage.getItem(AUTH_TOKEN_KEY);\n  }\n  static removeToken() {\n    localStorage.removeItem(AUTH_TOKEN_KEY);\n  }\n  static setUser(user) {\n    localStorage.setItem(AUTH_USER_KEY, JSON.stringify(user));\n  }\n  static getUser() {\n    const userStr = localStorage.getItem(AUTH_USER_KEY);\n    if (userStr) {\n      try {\n        return JSON.parse(userStr);\n      } catch (error) {\n        console.error('Error parsing user data:', error);\n        return null;\n      }\n    }\n    return null;\n  }\n  static removeUser() {\n    localStorage.removeItem(AUTH_USER_KEY);\n  }\n  static isAuthenticated() {\n    const token = this.getToken();\n    const user = this.getUser();\n    return !!(token && user);\n  }\n  static isAdmin() {\n    const user = this.getUser();\n    return (user === null || user === void 0 ? void 0 : user.role) === 'admin';\n  }\n  static isMember() {\n    const user = this.getUser();\n    return (user === null || user === void 0 ? void 0 : user.role) === 'member';\n  }\n  static logout() {\n    this.removeToken();\n    this.removeUser();\n    window.location.href = '/login';\n  }\n  static login(token, user) {\n    this.setToken(token);\n    this.setUser(user);\n  }\n  static getUserName() {\n    const user = this.getUser();\n    return (user === null || user === void 0 ? void 0 : user.name) || 'ผู้ใช้งาน';\n  }\n  static getUserEmail() {\n    const user = this.getUser();\n    return (user === null || user === void 0 ? void 0 : user.email) || '';\n  }\n  static getUserRole() {\n    const user = this.getUser();\n    return (user === null || user === void 0 ? void 0 : user.role) || 'member';\n  }\n  static hasPermission(requiredRole) {\n    const user = this.getUser();\n    if (!user) return false;\n    if (requiredRole === 'admin') {\n      return user.role === 'admin';\n    }\n\n    // Members and admins can access member-level features\n    return user.role === 'admin' || user.role === 'member';\n  }\n}\nexport default AuthUtils;", "map": {"version": 3, "names": ["AUTH_TOKEN_KEY", "AUTH_USER_KEY", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setToken", "token", "localStorage", "setItem", "getToken", "getItem", "removeToken", "removeItem", "setUser", "user", "JSON", "stringify", "getUser", "userStr", "parse", "error", "console", "removeUser", "isAuthenticated", "isAdmin", "role", "isMember", "logout", "window", "location", "href", "login", "getUserName", "name", "getUserEmail", "email", "getUserRole", "hasPermission", "requiredRole"], "sources": ["C:/Users/<USER>/Documents/augment-projects/coop/frontend/src/utils/auth.ts"], "sourcesContent": ["import { User } from '../types';\n\nexport const AUTH_TOKEN_KEY = 'token';\nexport const AUTH_USER_KEY = 'user';\n\nexport class AuthUtils {\n  static setToken(token: string): void {\n    localStorage.setItem(AUTH_TOKEN_KEY, token);\n  }\n\n  static getToken(): string | null {\n    return localStorage.getItem(AUTH_TOKEN_KEY);\n  }\n\n  static removeToken(): void {\n    localStorage.removeItem(AUTH_TOKEN_KEY);\n  }\n\n  static setUser(user: User): void {\n    localStorage.setItem(AUTH_USER_KEY, JSON.stringify(user));\n  }\n\n  static getUser(): User | null {\n    const userStr = localStorage.getItem(AUTH_USER_KEY);\n    if (userStr) {\n      try {\n        return JSON.parse(userStr);\n      } catch (error) {\n        console.error('Error parsing user data:', error);\n        return null;\n      }\n    }\n    return null;\n  }\n\n  static removeUser(): void {\n    localStorage.removeItem(AUTH_USER_KEY);\n  }\n\n  static isAuthenticated(): boolean {\n    const token = this.getToken();\n    const user = this.getUser();\n    return !!(token && user);\n  }\n\n  static isAdmin(): boolean {\n    const user = this.getUser();\n    return user?.role === 'admin';\n  }\n\n  static isMember(): boolean {\n    const user = this.getUser();\n    return user?.role === 'member';\n  }\n\n  static logout(): void {\n    this.removeToken();\n    this.removeUser();\n    window.location.href = '/login';\n  }\n\n  static login(token: string, user: User): void {\n    this.setToken(token);\n    this.setUser(user);\n  }\n\n  static getUserName(): string {\n    const user = this.getUser();\n    return user?.name || 'ผู้ใช้งาน';\n  }\n\n  static getUserEmail(): string {\n    const user = this.getUser();\n    return user?.email || '';\n  }\n\n  static getUserRole(): string {\n    const user = this.getUser();\n    return user?.role || 'member';\n  }\n\n  static hasPermission(requiredRole: 'admin' | 'member'): boolean {\n    const user = this.getUser();\n    if (!user) return false;\n\n    if (requiredRole === 'admin') {\n      return user.role === 'admin';\n    }\n\n    // Members and admins can access member-level features\n    return user.role === 'admin' || user.role === 'member';\n  }\n}\n\nexport default AuthUtils;\n"], "mappings": "AAEA,OAAO,MAAMA,cAAc,GAAG,OAAO;AACrC,OAAO,MAAMC,aAAa,GAAG,MAAM;AAEnC,OAAO,MAAMC,SAAS,CAAC;EACrB,OAAOC,QAAQA,CAACC,KAAa,EAAQ;IACnCC,YAAY,CAACC,OAAO,CAACN,cAAc,EAAEI,KAAK,CAAC;EAC7C;EAEA,OAAOG,QAAQA,CAAA,EAAkB;IAC/B,OAAOF,YAAY,CAACG,OAAO,CAACR,cAAc,CAAC;EAC7C;EAEA,OAAOS,WAAWA,CAAA,EAAS;IACzBJ,YAAY,CAACK,UAAU,CAACV,cAAc,CAAC;EACzC;EAEA,OAAOW,OAAOA,CAACC,IAAU,EAAQ;IAC/BP,YAAY,CAACC,OAAO,CAACL,aAAa,EAAEY,IAAI,CAACC,SAAS,CAACF,IAAI,CAAC,CAAC;EAC3D;EAEA,OAAOG,OAAOA,CAAA,EAAgB;IAC5B,MAAMC,OAAO,GAAGX,YAAY,CAACG,OAAO,CAACP,aAAa,CAAC;IACnD,IAAIe,OAAO,EAAE;MACX,IAAI;QACF,OAAOH,IAAI,CAACI,KAAK,CAACD,OAAO,CAAC;MAC5B,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,OAAO,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAEA,OAAOE,UAAUA,CAAA,EAAS;IACxBf,YAAY,CAACK,UAAU,CAACT,aAAa,CAAC;EACxC;EAEA,OAAOoB,eAAeA,CAAA,EAAY;IAChC,MAAMjB,KAAK,GAAG,IAAI,CAACG,QAAQ,CAAC,CAAC;IAC7B,MAAMK,IAAI,GAAG,IAAI,CAACG,OAAO,CAAC,CAAC;IAC3B,OAAO,CAAC,EAAEX,KAAK,IAAIQ,IAAI,CAAC;EAC1B;EAEA,OAAOU,OAAOA,CAAA,EAAY;IACxB,MAAMV,IAAI,GAAG,IAAI,CAACG,OAAO,CAAC,CAAC;IAC3B,OAAO,CAAAH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,IAAI,MAAK,OAAO;EAC/B;EAEA,OAAOC,QAAQA,CAAA,EAAY;IACzB,MAAMZ,IAAI,GAAG,IAAI,CAACG,OAAO,CAAC,CAAC;IAC3B,OAAO,CAAAH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,IAAI,MAAK,QAAQ;EAChC;EAEA,OAAOE,MAAMA,CAAA,EAAS;IACpB,IAAI,CAAChB,WAAW,CAAC,CAAC;IAClB,IAAI,CAACW,UAAU,CAAC,CAAC;IACjBM,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EAEA,OAAOC,KAAKA,CAACzB,KAAa,EAAEQ,IAAU,EAAQ;IAC5C,IAAI,CAACT,QAAQ,CAACC,KAAK,CAAC;IACpB,IAAI,CAACO,OAAO,CAACC,IAAI,CAAC;EACpB;EAEA,OAAOkB,WAAWA,CAAA,EAAW;IAC3B,MAAMlB,IAAI,GAAG,IAAI,CAACG,OAAO,CAAC,CAAC;IAC3B,OAAO,CAAAH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,IAAI,KAAI,WAAW;EAClC;EAEA,OAAOC,YAAYA,CAAA,EAAW;IAC5B,MAAMpB,IAAI,GAAG,IAAI,CAACG,OAAO,CAAC,CAAC;IAC3B,OAAO,CAAAH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqB,KAAK,KAAI,EAAE;EAC1B;EAEA,OAAOC,WAAWA,CAAA,EAAW;IAC3B,MAAMtB,IAAI,GAAG,IAAI,CAACG,OAAO,CAAC,CAAC;IAC3B,OAAO,CAAAH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,IAAI,KAAI,QAAQ;EAC/B;EAEA,OAAOY,aAAaA,CAACC,YAAgC,EAAW;IAC9D,MAAMxB,IAAI,GAAG,IAAI,CAACG,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACH,IAAI,EAAE,OAAO,KAAK;IAEvB,IAAIwB,YAAY,KAAK,OAAO,EAAE;MAC5B,OAAOxB,IAAI,CAACW,IAAI,KAAK,OAAO;IAC9B;;IAEA;IACA,OAAOX,IAAI,CAACW,IAAI,KAAK,OAAO,IAAIX,IAAI,CAACW,IAAI,KAAK,QAAQ;EACxD;AACF;AAEA,eAAerB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}