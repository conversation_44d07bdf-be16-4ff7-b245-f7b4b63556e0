import React, { useState, useEffect } from 'react';
import { Member, PaginatedResponse } from '../types';
import { apiService } from '../services/api';
import SweetAlertUtils from '../utils/sweetAlert';
import { Link } from 'react-router-dom';

const MembersPage: React.FC = () => {
  const [members, setMembers] = useState<Member[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchMembers();
  }, []);

  const fetchMembers = async () => {
    try {
      setLoading(true);
      const response = await apiService.getMembers();
      if (response.success) {
        const data = response.data as PaginatedResponse<Member>;
        setMembers(data.data);
      } else {
        SweetAlertUtils.error('เกิดข้อผิดพลาด', 'ไม่สามารถโหลดข้อมูลสมาชิกได้');
      }
    } catch (error) {
      console.error('Error fetching members:', error);
      SweetAlertUtils.error('เกิดข้อผิดพลาด', 'ไม่สามารถโหลดข้อมูลสมาชิกได้');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('th-TH', {
      style: 'currency',
      currency: 'THB'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Intl.DateTimeFormat('th-TH', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(new Date(dateString));
  };

  const filteredMembers = members.filter(member =>
    member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.memberCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (member.email && member.email.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  if (loading) {
    return (
      <div className="content-header">
        <div className="container-fluid">
          <div className="row mb-2">
            <div className="col-sm-6">
              <h1 className="m-0">จัดการสมาชิก</h1>
            </div>
          </div>
        </div>
        <section className="content">
          <div className="container-fluid">
            <div className="d-flex justify-content-center">
              <div className="spinner-border text-primary" role="status">
                <span className="sr-only">กำลังโหลด...</span>
              </div>
            </div>
          </div>
        </section>
      </div>
    );
  }

  return (
    <>
      {/* Content Header */}
      <div className="content-header">
        <div className="container-fluid">
          <div className="row mb-2">
            <div className="col-sm-6">
              <h1 className="m-0">
                <i className="fas fa-users mr-2"></i>
                จัดการสมาชิก
              </h1>
            </div>
            <div className="col-sm-6">
              <ol className="breadcrumb float-sm-right">
                <li className="breadcrumb-item"><Link to="/">หน้าแรก</Link></li>
                <li className="breadcrumb-item active">จัดการสมาชิก</li>
              </ol>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <section className="content">
        <div className="container-fluid">
          <div className="row">
            <div className="col-12">
              <div className="card">
                <div className="card-header">
                  <h3 className="card-title">รายชื่อสมาชิก</h3>
                  <div className="card-tools">
                    <div className="input-group input-group-sm" style={{width: '250px'}}>
                      <input
                        type="text"
                        className="form-control float-right"
                        placeholder="ค้นหาสมาชิก..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                      <div className="input-group-append">
                        <button type="submit" className="btn btn-default">
                          <i className="fas fa-search"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="card-body table-responsive p-0">
                  <table className="table table-hover text-nowrap">
                    <thead>
                      <tr>
                        <th>รหัสสมาชิก</th>
                        <th>ชื่อ-นามสกุล</th>
                        <th>อีเมล</th>
                        <th>เบอร์โทร</th>
                        <th>จำนวนหุ้น</th>
                        <th>ยอดซื้อสะสม</th>
                        <th>ปันผลสะสม</th>
                        <th>วันที่สมัคร</th>
                        <th>สถานะ</th>
                        <th>จัดการ</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredMembers.map((member) => (
                        <tr key={member._id}>
                          <td>
                            <span className="badge badge-primary">{member.memberCode}</span>
                          </td>
                          <td>
                            <strong>{member.name}</strong>
                            {member.studentId && (
                              <>
                                <br />
                                <small className="text-muted">รหัสนักเรียน: {member.studentId}</small>
                              </>
                            )}
                          </td>
                          <td>{member.email || '-'}</td>
                          <td>{member.phone || '-'}</td>
                          <td>
                            <span className="badge badge-info">{member.shares} หุ้น</span>
                          </td>
                          <td>{formatCurrency(member.totalPurchase)}</td>
                          <td>{formatCurrency(member.totalDividend)}</td>
                          <td>{formatDate(member.joinDate)}</td>
                          <td>
                            {member.isActive ? (
                              <span className="badge badge-success">ใช้งาน</span>
                            ) : (
                              <span className="badge badge-danger">ระงับ</span>
                            )}
                          </td>
                          <td>
                            <div className="btn-group">
                              <button className="btn btn-sm btn-info">
                                <i className="fas fa-eye"></i>
                              </button>
                              <button className="btn btn-sm btn-warning">
                                <i className="fas fa-edit"></i>
                              </button>
                              <button className="btn btn-sm btn-danger">
                                <i className="fas fa-trash"></i>
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                  
                  {filteredMembers.length === 0 && (
                    <div className="text-center p-4">
                      <i className="fas fa-users fa-3x text-muted mb-3"></i>
                      <h5 className="text-muted">ไม่พบข้อมูลสมาชิก</h5>
                      <p className="text-muted">
                        {searchTerm ? 'ไม่พบสมาชิกที่ตรงกับคำค้นหา' : 'ยังไม่มีสมาชิกในระบบ'}
                      </p>
                    </div>
                  )}
                </div>
                <div className="card-footer">
                  <div className="row">
                    <div className="col-sm-6">
                      <span className="text-muted">
                        แสดง {filteredMembers.length} จาก {members.length} รายการ
                      </span>
                    </div>
                    <div className="col-sm-6">
                      <button className="btn btn-primary float-right">
                        <i className="fas fa-plus mr-1"></i>
                        เพิ่มสมาชิกใหม่
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default MembersPage;
