"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authController_1 = require("../controllers/authController");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const router = (0, express_1.Router)();
router.post('/register', validation_1.validateUserRegistration, authController_1.register);
router.post('/login', validation_1.validateUserLogin, authController_1.login);
router.use(auth_1.authenticate);
router.get('/me', authController_1.getMe);
router.put('/profile', authController_1.updateProfile);
router.put('/change-password', authController_1.changePassword);
router.post('/logout', authController_1.logout);
exports.default = router;
//# sourceMappingURL=authRoutes.js.map