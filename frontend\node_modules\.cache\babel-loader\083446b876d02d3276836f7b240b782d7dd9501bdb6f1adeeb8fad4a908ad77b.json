{"ast": null, "code": "import Swal from 'sweetalert2';\nexport class SweetAlertUtils {\n  // Success alert\n  static success(title, text) {\n    return Swal.fire({\n      icon: 'success',\n      title,\n      text,\n      confirmButtonText: 'ตกลง',\n      confirmButtonColor: '#28a745'\n    });\n  }\n\n  // Error alert\n  static error(title, text) {\n    return Swal.fire({\n      icon: 'error',\n      title,\n      text,\n      confirmButtonText: 'ตกลง',\n      confirmButtonColor: '#dc3545'\n    });\n  }\n\n  // Warning alert\n  static warning(title, text) {\n    return Swal.fire({\n      icon: 'warning',\n      title,\n      text,\n      confirmButtonText: 'ตกลง',\n      confirmButtonColor: '#ffc107'\n    });\n  }\n\n  // Info alert\n  static info(title, text) {\n    return Swal.fire({\n      icon: 'info',\n      title,\n      text,\n      confirmButtonText: 'ตกลง',\n      confirmButtonColor: '#17a2b8'\n    });\n  }\n\n  // Confirmation dialog\n  static confirm(title, text, confirmText = 'ยืนยัน', cancelText = 'ยกเลิก') {\n    return Swal.fire({\n      icon: 'question',\n      title,\n      text,\n      showCancelButton: true,\n      confirmButtonText: confirmText,\n      cancelButtonText: cancelText,\n      confirmButtonColor: '#007bff',\n      cancelButtonColor: '#6c757d',\n      reverseButtons: true\n    });\n  }\n\n  // Delete confirmation\n  static confirmDelete(itemName = 'รายการนี้') {\n    return Swal.fire({\n      icon: 'warning',\n      title: 'ยืนยันการลบ',\n      text: `คุณต้องการลบ${itemName}หรือไม่?`,\n      showCancelButton: true,\n      confirmButtonText: 'ลบ',\n      cancelButtonText: 'ยกเลิก',\n      confirmButtonColor: '#dc3545',\n      cancelButtonColor: '#6c757d',\n      reverseButtons: true\n    });\n  }\n\n  // Loading alert\n  static loading(title = 'กำลังดำเนินการ...') {\n    return Swal.fire({\n      title,\n      allowOutsideClick: false,\n      allowEscapeKey: false,\n      showConfirmButton: false,\n      didOpen: () => {\n        Swal.showLoading();\n      }\n    });\n  }\n\n  // Close loading\n  static closeLoading() {\n    Swal.close();\n  }\n\n  // Toast notification\n  static toast(icon, title) {\n    const Toast = Swal.mixin({\n      toast: true,\n      position: 'top-end',\n      showConfirmButton: false,\n      timer: 3000,\n      timerProgressBar: true,\n      didOpen: toast => {\n        toast.addEventListener('mouseenter', Swal.stopTimer);\n        toast.addEventListener('mouseleave', Swal.resumeTimer);\n      }\n    });\n    return Toast.fire({\n      icon,\n      title\n    });\n  }\n\n  // Input dialog\n  static input(title, inputPlaceholder = '', inputType = 'text') {\n    return Swal.fire({\n      title,\n      input: inputType,\n      inputPlaceholder,\n      showCancelButton: true,\n      confirmButtonText: 'ตกลง',\n      cancelButtonText: 'ยกเลิก',\n      confirmButtonColor: '#007bff',\n      cancelButtonColor: '#6c757d',\n      inputValidator: value => {\n        if (!value) {\n          return 'กรุณากรอกข้อมูล';\n        }\n      }\n    });\n  }\n\n  // Select dialog\n  static select(title, options, placeholder = 'เลือก...') {\n    return Swal.fire({\n      title,\n      input: 'select',\n      inputOptions: options,\n      inputPlaceholder: placeholder,\n      showCancelButton: true,\n      confirmButtonText: 'ตกลง',\n      cancelButtonText: 'ยกเลิก',\n      confirmButtonColor: '#007bff',\n      cancelButtonColor: '#6c757d',\n      inputValidator: value => {\n        if (!value) {\n          return 'กรุณาเลือกตัวเลือก';\n        }\n      }\n    });\n  }\n\n  // Custom HTML dialog\n  static html(title, html) {\n    return Swal.fire({\n      title,\n      html,\n      showCancelButton: true,\n      confirmButtonText: 'ตกลง',\n      cancelButtonText: 'ยกเลิก',\n      confirmButtonColor: '#007bff',\n      cancelButtonColor: '#6c757d'\n    });\n  }\n}\nexport default SweetAlertUtils;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Sweet<PERSON>lertUtils", "success", "title", "text", "fire", "icon", "confirmButtonText", "confirmButtonColor", "error", "warning", "info", "confirm", "confirmText", "cancelText", "showCancelButton", "cancelButtonText", "cancelButtonColor", "reverseButtons", "confirmDelete", "itemName", "loading", "allowOutsideClick", "allowEscapeKey", "showConfirmButton", "did<PERSON><PERSON>", "showLoading", "closeLoading", "close", "toast", "Toast", "mixin", "position", "timer", "timerP<PERSON>ressBar", "addEventListener", "stopTimer", "resumeTimer", "input", "inputPlaceholder", "inputType", "inputValidator", "value", "select", "options", "placeholder", "inputOptions", "html"], "sources": ["C:/Users/<USER>/Documents/augment-projects/coop/frontend/src/utils/sweetAlert.ts"], "sourcesContent": ["import Swal from 'sweetalert2';\n\nexport class SweetAlertUtils {\n  // Success alert\n  static success(title: string, text?: string) {\n    return Swal.fire({\n      icon: 'success',\n      title,\n      text,\n      confirmButtonText: 'ตกลง',\n      confirmButtonColor: '#28a745',\n    });\n  }\n\n  // Error alert\n  static error(title: string, text?: string) {\n    return Swal.fire({\n      icon: 'error',\n      title,\n      text,\n      confirmButtonText: 'ตกลง',\n      confirmButtonColor: '#dc3545',\n    });\n  }\n\n  // Warning alert\n  static warning(title: string, text?: string) {\n    return Swal.fire({\n      icon: 'warning',\n      title,\n      text,\n      confirmButtonText: 'ตกลง',\n      confirmButtonColor: '#ffc107',\n    });\n  }\n\n  // Info alert\n  static info(title: string, text?: string) {\n    return Swal.fire({\n      icon: 'info',\n      title,\n      text,\n      confirmButtonText: 'ตกลง',\n      confirmButtonColor: '#17a2b8',\n    });\n  }\n\n  // Confirmation dialog\n  static confirm(title: string, text?: string, confirmText: string = 'ยืนยัน', cancelText: string = 'ยกเลิก') {\n    return Swal.fire({\n      icon: 'question',\n      title,\n      text,\n      showCancelButton: true,\n      confirmButtonText: confirmText,\n      cancelButtonText: cancelText,\n      confirmButtonColor: '#007bff',\n      cancelButtonColor: '#6c757d',\n      reverseButtons: true,\n    });\n  }\n\n  // Delete confirmation\n  static confirmDelete(itemName: string = 'รายการนี้') {\n    return Swal.fire({\n      icon: 'warning',\n      title: 'ยืนยันการลบ',\n      text: `คุณต้องการลบ${itemName}หรือไม่?`,\n      showCancelButton: true,\n      confirmButtonText: 'ลบ',\n      cancelButtonText: 'ยกเลิก',\n      confirmButtonColor: '#dc3545',\n      cancelButtonColor: '#6c757d',\n      reverseButtons: true,\n    });\n  }\n\n  // Loading alert\n  static loading(title: string = 'กำลังดำเนินการ...') {\n    return Swal.fire({\n      title,\n      allowOutsideClick: false,\n      allowEscapeKey: false,\n      showConfirmButton: false,\n      didOpen: () => {\n        Swal.showLoading();\n      },\n    });\n  }\n\n  // Close loading\n  static closeLoading() {\n    Swal.close();\n  }\n\n  // Toast notification\n  static toast(icon: 'success' | 'error' | 'warning' | 'info', title: string) {\n    const Toast = Swal.mixin({\n      toast: true,\n      position: 'top-end',\n      showConfirmButton: false,\n      timer: 3000,\n      timerProgressBar: true,\n      didOpen: (toast) => {\n        toast.addEventListener('mouseenter', Swal.stopTimer);\n        toast.addEventListener('mouseleave', Swal.resumeTimer);\n      },\n    });\n\n    return Toast.fire({\n      icon,\n      title,\n    });\n  }\n\n  // Input dialog\n  static input(title: string, inputPlaceholder: string = '', inputType: 'text' | 'email' | 'password' | 'number' = 'text') {\n    return Swal.fire({\n      title,\n      input: inputType,\n      inputPlaceholder,\n      showCancelButton: true,\n      confirmButtonText: 'ตกลง',\n      cancelButtonText: 'ยกเลิก',\n      confirmButtonColor: '#007bff',\n      cancelButtonColor: '#6c757d',\n      inputValidator: (value) => {\n        if (!value) {\n          return 'กรุณากรอกข้อมูล';\n        }\n      },\n    });\n  }\n\n  // Select dialog\n  static select(title: string, options: { [key: string]: string }, placeholder: string = 'เลือก...') {\n    return Swal.fire({\n      title,\n      input: 'select',\n      inputOptions: options,\n      inputPlaceholder: placeholder,\n      showCancelButton: true,\n      confirmButtonText: 'ตกลง',\n      cancelButtonText: 'ยกเลิก',\n      confirmButtonColor: '#007bff',\n      cancelButtonColor: '#6c757d',\n      inputValidator: (value) => {\n        if (!value) {\n          return 'กรุณาเลือกตัวเลือก';\n        }\n      },\n    });\n  }\n\n  // Custom HTML dialog\n  static html(title: string, html: string) {\n    return Swal.fire({\n      title,\n      html,\n      showCancelButton: true,\n      confirmButtonText: 'ตกลง',\n      cancelButtonText: 'ยกเลิก',\n      confirmButtonColor: '#007bff',\n      cancelButtonColor: '#6c757d',\n    });\n  }\n}\n\nexport default SweetAlertUtils;\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,aAAa;AAE9B,OAAO,MAAMC,eAAe,CAAC;EAC3B;EACA,OAAOC,OAAOA,CAACC,KAAa,EAAEC,IAAa,EAAE;IAC3C,OAAOJ,IAAI,CAACK,IAAI,CAAC;MACfC,IAAI,EAAE,SAAS;MACfH,KAAK;MACLC,IAAI;MACJG,iBAAiB,EAAE,MAAM;MACzBC,kBAAkB,EAAE;IACtB,CAAC,CAAC;EACJ;;EAEA;EACA,OAAOC,KAAKA,CAACN,KAAa,EAAEC,IAAa,EAAE;IACzC,OAAOJ,IAAI,CAACK,IAAI,CAAC;MACfC,IAAI,EAAE,OAAO;MACbH,KAAK;MACLC,IAAI;MACJG,iBAAiB,EAAE,MAAM;MACzBC,kBAAkB,EAAE;IACtB,CAAC,CAAC;EACJ;;EAEA;EACA,OAAOE,OAAOA,CAACP,KAAa,EAAEC,IAAa,EAAE;IAC3C,OAAOJ,IAAI,CAACK,IAAI,CAAC;MACfC,IAAI,EAAE,SAAS;MACfH,KAAK;MACLC,IAAI;MACJG,iBAAiB,EAAE,MAAM;MACzBC,kBAAkB,EAAE;IACtB,CAAC,CAAC;EACJ;;EAEA;EACA,OAAOG,IAAIA,CAACR,KAAa,EAAEC,IAAa,EAAE;IACxC,OAAOJ,IAAI,CAACK,IAAI,CAAC;MACfC,IAAI,EAAE,MAAM;MACZH,KAAK;MACLC,IAAI;MACJG,iBAAiB,EAAE,MAAM;MACzBC,kBAAkB,EAAE;IACtB,CAAC,CAAC;EACJ;;EAEA;EACA,OAAOI,OAAOA,CAACT,KAAa,EAAEC,IAAa,EAAES,WAAmB,GAAG,QAAQ,EAAEC,UAAkB,GAAG,QAAQ,EAAE;IAC1G,OAAOd,IAAI,CAACK,IAAI,CAAC;MACfC,IAAI,EAAE,UAAU;MAChBH,KAAK;MACLC,IAAI;MACJW,gBAAgB,EAAE,IAAI;MACtBR,iBAAiB,EAAEM,WAAW;MAC9BG,gBAAgB,EAAEF,UAAU;MAC5BN,kBAAkB,EAAE,SAAS;MAC7BS,iBAAiB,EAAE,SAAS;MAC5BC,cAAc,EAAE;IAClB,CAAC,CAAC;EACJ;;EAEA;EACA,OAAOC,aAAaA,CAACC,QAAgB,GAAG,WAAW,EAAE;IACnD,OAAOpB,IAAI,CAACK,IAAI,CAAC;MACfC,IAAI,EAAE,SAAS;MACfH,KAAK,EAAE,aAAa;MACpBC,IAAI,EAAE,eAAegB,QAAQ,UAAU;MACvCL,gBAAgB,EAAE,IAAI;MACtBR,iBAAiB,EAAE,IAAI;MACvBS,gBAAgB,EAAE,QAAQ;MAC1BR,kBAAkB,EAAE,SAAS;MAC7BS,iBAAiB,EAAE,SAAS;MAC5BC,cAAc,EAAE;IAClB,CAAC,CAAC;EACJ;;EAEA;EACA,OAAOG,OAAOA,CAAClB,KAAa,GAAG,mBAAmB,EAAE;IAClD,OAAOH,IAAI,CAACK,IAAI,CAAC;MACfF,KAAK;MACLmB,iBAAiB,EAAE,KAAK;MACxBC,cAAc,EAAE,KAAK;MACrBC,iBAAiB,EAAE,KAAK;MACxBC,OAAO,EAAEA,CAAA,KAAM;QACbzB,IAAI,CAAC0B,WAAW,CAAC,CAAC;MACpB;IACF,CAAC,CAAC;EACJ;;EAEA;EACA,OAAOC,YAAYA,CAAA,EAAG;IACpB3B,IAAI,CAAC4B,KAAK,CAAC,CAAC;EACd;;EAEA;EACA,OAAOC,KAAKA,CAACvB,IAA8C,EAAEH,KAAa,EAAE;IAC1E,MAAM2B,KAAK,GAAG9B,IAAI,CAAC+B,KAAK,CAAC;MACvBF,KAAK,EAAE,IAAI;MACXG,QAAQ,EAAE,SAAS;MACnBR,iBAAiB,EAAE,KAAK;MACxBS,KAAK,EAAE,IAAI;MACXC,gBAAgB,EAAE,IAAI;MACtBT,OAAO,EAAGI,KAAK,IAAK;QAClBA,KAAK,CAACM,gBAAgB,CAAC,YAAY,EAAEnC,IAAI,CAACoC,SAAS,CAAC;QACpDP,KAAK,CAACM,gBAAgB,CAAC,YAAY,EAAEnC,IAAI,CAACqC,WAAW,CAAC;MACxD;IACF,CAAC,CAAC;IAEF,OAAOP,KAAK,CAACzB,IAAI,CAAC;MAChBC,IAAI;MACJH;IACF,CAAC,CAAC;EACJ;;EAEA;EACA,OAAOmC,KAAKA,CAACnC,KAAa,EAAEoC,gBAAwB,GAAG,EAAE,EAAEC,SAAmD,GAAG,MAAM,EAAE;IACvH,OAAOxC,IAAI,CAACK,IAAI,CAAC;MACfF,KAAK;MACLmC,KAAK,EAAEE,SAAS;MAChBD,gBAAgB;MAChBxB,gBAAgB,EAAE,IAAI;MACtBR,iBAAiB,EAAE,MAAM;MACzBS,gBAAgB,EAAE,QAAQ;MAC1BR,kBAAkB,EAAE,SAAS;MAC7BS,iBAAiB,EAAE,SAAS;MAC5BwB,cAAc,EAAGC,KAAK,IAAK;QACzB,IAAI,CAACA,KAAK,EAAE;UACV,OAAO,iBAAiB;QAC1B;MACF;IACF,CAAC,CAAC;EACJ;;EAEA;EACA,OAAOC,MAAMA,CAACxC,KAAa,EAAEyC,OAAkC,EAAEC,WAAmB,GAAG,UAAU,EAAE;IACjG,OAAO7C,IAAI,CAACK,IAAI,CAAC;MACfF,KAAK;MACLmC,KAAK,EAAE,QAAQ;MACfQ,YAAY,EAAEF,OAAO;MACrBL,gBAAgB,EAAEM,WAAW;MAC7B9B,gBAAgB,EAAE,IAAI;MACtBR,iBAAiB,EAAE,MAAM;MACzBS,gBAAgB,EAAE,QAAQ;MAC1BR,kBAAkB,EAAE,SAAS;MAC7BS,iBAAiB,EAAE,SAAS;MAC5BwB,cAAc,EAAGC,KAAK,IAAK;QACzB,IAAI,CAACA,KAAK,EAAE;UACV,OAAO,oBAAoB;QAC7B;MACF;IACF,CAAC,CAAC;EACJ;;EAEA;EACA,OAAOK,IAAIA,CAAC5C,KAAa,EAAE4C,IAAY,EAAE;IACvC,OAAO/C,IAAI,CAACK,IAAI,CAAC;MACfF,KAAK;MACL4C,IAAI;MACJhC,gBAAgB,EAAE,IAAI;MACtBR,iBAAiB,EAAE,MAAM;MACzBS,gBAAgB,EAAE,QAAQ;MAC1BR,kBAAkB,EAAE,SAAS;MAC7BS,iBAAiB,EAAE;IACrB,CAAC,CAAC;EACJ;AACF;AAEA,eAAehB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}