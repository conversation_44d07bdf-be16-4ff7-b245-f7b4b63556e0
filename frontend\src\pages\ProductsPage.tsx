import React, { useState, useEffect } from 'react';
import { Product, PaginatedResponse } from '../types';
import { apiService } from '../services/api';
import SweetAlertUtils from '../utils/sweetAlert';

const ProductsPage: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await apiService.getProducts();
      if (response.success) {
        const data = response.data as PaginatedResponse<Product>;
        setProducts(data.data);
      } else {
        SweetAlertUtils.error('เกิดข้อผิดพลาด', 'ไม่สามารถโหลดข้อมูลสินค้าได้');
      }
    } catch (error) {
      console.error('Error fetching products:', error);
      SweetAlertUtils.error('เกิดข้อผิดพลาด', 'ไม่สามารถโหลดข้อมูลสินค้าได้');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('th-TH', {
      style: 'currency',
      currency: 'THB'
    }).format(amount);
  };

  const calculateProfitMargin = (costPrice: number, sellingPrice: number) => {
    if (costPrice === 0) return 0;
    return ((sellingPrice - costPrice) / costPrice) * 100;
  };

  const getStockStatus = (stock: number, minStock: number) => {
    if (stock === 0) return { class: 'danger', text: 'หมด' };
    if (stock <= minStock) return { class: 'warning', text: 'ใกล้หมด' };
    return { class: 'success', text: 'เพียงพอ' };
  };

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.productCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="content-header">
        <div className="container-fluid">
          <div className="row mb-2">
            <div className="col-sm-6">
              <h1 className="m-0">จัดการสินค้า</h1>
            </div>
          </div>
        </div>
        <section className="content">
          <div className="container-fluid">
            <div className="d-flex justify-content-center">
              <div className="spinner-border text-primary" role="status">
                <span className="sr-only">กำลังโหลด...</span>
              </div>
            </div>
          </div>
        </section>
      </div>
    );
  }

  return (
    <>
      {/* Content Header */}
      <div className="content-header">
        <div className="container-fluid">
          <div className="row mb-2">
            <div className="col-sm-6">
              <h1 className="m-0">
                <i className="fas fa-box mr-2"></i>
                จัดการสินค้า
              </h1>
            </div>
            <div className="col-sm-6">
              <ol className="breadcrumb float-sm-right">
                <li className="breadcrumb-item"><a href="/">หน้าแรก</a></li>
                <li className="breadcrumb-item active">จัดการสินค้า</li>
              </ol>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <section className="content">
        <div className="container-fluid">
          <div className="row">
            <div className="col-12">
              <div className="card">
                <div className="card-header">
                  <h3 className="card-title">รายการสินค้า</h3>
                  <div className="card-tools">
                    <div className="input-group input-group-sm" style={{width: '250px'}}>
                      <input
                        type="text"
                        className="form-control float-right"
                        placeholder="ค้นหาสินค้า..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                      <div className="input-group-append">
                        <button type="submit" className="btn btn-default">
                          <i className="fas fa-search"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="card-body table-responsive p-0">
                  <table className="table table-hover text-nowrap">
                    <thead>
                      <tr>
                        <th>รหัสสินค้า</th>
                        <th>ชื่อสินค้า</th>
                        <th>หมวดหมู่</th>
                        <th>ราคาทุน</th>
                        <th>ราคาขาย</th>
                        <th>กำไร</th>
                        <th>สต๊อก</th>
                        <th>สถานะ</th>
                        <th>จัดการ</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredProducts.map((product) => {
                        const stockStatus = getStockStatus(product.stock, product.minStock);
                        const profitMargin = calculateProfitMargin(product.costPrice, product.sellingPrice);
                        
                        return (
                          <tr key={product._id}>
                            <td>
                              <span className="badge badge-primary">{product.productCode}</span>
                            </td>
                            <td>
                              <strong>{product.name}</strong>
                              {product.description && (
                                <br />
                                <small className="text-muted">{product.description}</small>
                              )}
                            </td>
                            <td>
                              <span className="badge badge-secondary">{product.category}</span>
                            </td>
                            <td>{formatCurrency(product.costPrice)}</td>
                            <td>{formatCurrency(product.sellingPrice)}</td>
                            <td>
                              <span className="text-success">
                                {formatCurrency(product.sellingPrice - product.costPrice)}
                              </span>
                              <br />
                              <small className="text-muted">
                                ({profitMargin.toFixed(1)}%)
                              </small>
                            </td>
                            <td>
                              <strong>{product.stock}</strong> {product.unit}
                              <br />
                              <small className="text-muted">
                                ขั้นต่ำ: {product.minStock} {product.unit}
                              </small>
                            </td>
                            <td>
                              <span className={`badge badge-${stockStatus.class}`}>
                                {stockStatus.text}
                              </span>
                              <br />
                              {product.isActive ? (
                                <span className="badge badge-success">ใช้งาน</span>
                              ) : (
                                <span className="badge badge-danger">ระงับ</span>
                              )}
                            </td>
                            <td>
                              <div className="btn-group">
                                <button className="btn btn-sm btn-info">
                                  <i className="fas fa-eye"></i>
                                </button>
                                <button className="btn btn-sm btn-warning">
                                  <i className="fas fa-edit"></i>
                                </button>
                                <button className="btn btn-sm btn-success">
                                  <i className="fas fa-plus"></i>
                                </button>
                                <button className="btn btn-sm btn-danger">
                                  <i className="fas fa-trash"></i>
                                </button>
                              </div>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                  
                  {filteredProducts.length === 0 && (
                    <div className="text-center p-4">
                      <i className="fas fa-box fa-3x text-muted mb-3"></i>
                      <h5 className="text-muted">ไม่พบข้อมูลสินค้า</h5>
                      <p className="text-muted">
                        {searchTerm ? 'ไม่พบสินค้าที่ตรงกับคำค้นหา' : 'ยังไม่มีสินค้าในระบบ'}
                      </p>
                    </div>
                  )}
                </div>
                <div className="card-footer">
                  <div className="row">
                    <div className="col-sm-6">
                      <span className="text-muted">
                        แสดง {filteredProducts.length} จาก {products.length} รายการ
                      </span>
                    </div>
                    <div className="col-sm-6">
                      <button className="btn btn-primary float-right">
                        <i className="fas fa-plus mr-1"></i>
                        เพิ่มสินค้าใหม่
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default ProductsPage;
