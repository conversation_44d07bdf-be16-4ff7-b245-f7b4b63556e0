"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Product = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const productSchema = new mongoose_1.Schema({
    productCode: {
        type: String,
        required: [true, 'รหัสสินค้าจำเป็นต้องระบุ'],
        unique: true,
        trim: true,
        uppercase: true
    },
    name: {
        type: String,
        required: [true, 'ชื่อสินค้าจำเป็นต้องระบุ'],
        trim: true,
        maxlength: [200, 'ชื่อสินค้าต้องไม่เกิน 200 ตัวอักษร']
    },
    description: {
        type: String,
        trim: true,
        maxlength: [1000, 'รายละเอียดต้องไม่เกิน 1000 ตัวอักษร']
    },
    category: {
        type: String,
        required: [true, 'หมวดหมู่สินค้าจำเป็นต้องระบุ'],
        trim: true,
        maxlength: [100, 'หมวดหมู่ต้องไม่เกิน 100 ตัวอักษร']
    },
    costPrice: {
        type: Number,
        required: [true, 'ราคาทุนจำเป็นต้องระบุ'],
        min: [0, 'ราคาทุนต้องไม่น้อยกว่า 0']
    },
    sellingPrice: {
        type: Number,
        required: [true, 'ราคาขายจำเป็นต้องระบุ'],
        min: [0, 'ราคาขายต้องไม่น้อยกว่า 0']
    },
    stock: {
        type: Number,
        required: [true, 'จำนวนสต๊อกจำเป็นต้องระบุ'],
        min: [0, 'จำนวนสต๊อกต้องไม่น้อยกว่า 0'],
        default: 0
    },
    minStock: {
        type: Number,
        default: 5,
        min: [0, 'สต๊อกขั้นต่ำต้องไม่น้อยกว่า 0']
    },
    unit: {
        type: String,
        required: [true, 'หน่วยนับจำเป็นต้องระบุ'],
        trim: true,
        maxlength: [20, 'หน่วยนับต้องไม่เกิน 20 ตัวอักษร']
    },
    isActive: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true
});
productSchema.index({ productCode: 1 });
productSchema.index({ name: 1 });
productSchema.index({ category: 1 });
productSchema.index({ isActive: 1 });
productSchema.index({ stock: 1 });
productSchema.pre('save', async function (next) {
    if (!this.isNew || this.productCode)
        return next();
    try {
        const count = await mongoose_1.default.model('Product').countDocuments();
        this.productCode = `P${String(count + 1).padStart(6, '0')}`;
        next();
    }
    catch (error) {
        next(error);
    }
});
productSchema.pre('save', function (next) {
    if (this.sellingPrice < this.costPrice) {
        return next(new Error('ราคาขายต้องมากกว่าหรือเท่ากับราคาทุน'));
    }
    next();
});
productSchema.virtual('profitMargin').get(function () {
    if (this.costPrice === 0)
        return 0;
    return ((this.sellingPrice - this.costPrice) / this.costPrice) * 100;
});
productSchema.virtual('profitAmount').get(function () {
    return this.sellingPrice - this.costPrice;
});
productSchema.virtual('isLowStock').get(function () {
    return this.stock <= this.minStock;
});
productSchema.virtual('isOutOfStock').get(function () {
    return this.stock === 0;
});
productSchema.statics.findActive = function () {
    return this.find({ isActive: true });
};
productSchema.statics.findLowStock = function () {
    return this.find({
        isActive: true,
        $expr: { $lte: ['$stock', '$minStock'] }
    });
};
productSchema.statics.findByCode = function (code) {
    return this.findOne({ productCode: code.toUpperCase() });
};
productSchema.statics.findByCategory = function (category) {
    return this.find({ category, isActive: true });
};
productSchema.methods.updateStock = function (quantity) {
    this.stock += quantity;
    if (this.stock < 0) {
        throw new Error('สต๊อกไม่เพียงพอ');
    }
    return this.save();
};
productSchema.methods.reduceStock = function (quantity) {
    if (this.stock < quantity) {
        throw new Error('สต๊อกไม่เพียงพอ');
    }
    this.stock -= quantity;
    return this.save();
};
productSchema.methods.addStock = function (quantity) {
    this.stock += quantity;
    return this.save();
};
exports.Product = mongoose_1.default.model('Product', productSchema);
exports.default = exports.Product;
//# sourceMappingURL=Product.js.map