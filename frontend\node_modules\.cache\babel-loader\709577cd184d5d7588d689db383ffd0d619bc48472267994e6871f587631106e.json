{"ast": null, "code": "import axios from 'axios';\nclass ApiService {\n  constructor() {\n    this.api = void 0;\n    this.api = axios.create({\n      baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',\n      timeout: 10000,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n\n    // Request interceptor to add auth token\n    this.api.interceptors.request.use(config => {\n      const token = localStorage.getItem('token');\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n      return config;\n    }, error => {\n      return Promise.reject(error);\n    });\n\n    // Response interceptor to handle errors\n    this.api.interceptors.response.use(response => {\n      return response;\n    }, error => {\n      var _error$response;\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n        // Unauthorized - clear token and redirect to login\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n        window.location.href = '/login';\n      }\n      return Promise.reject(error);\n    });\n  }\n\n  // Generic methods\n  async get(url, params) {\n    const response = await this.api.get(url, {\n      params\n    });\n    return response.data;\n  }\n  async post(url, data) {\n    const response = await this.api.post(url, data);\n    return response.data;\n  }\n  async put(url, data) {\n    const response = await this.api.put(url, data);\n    return response.data;\n  }\n  async delete(url) {\n    const response = await this.api.delete(url);\n    return response.data;\n  }\n\n  // Auth methods\n  async login(email, password) {\n    return this.post('/auth/login', {\n      email,\n      password\n    });\n  }\n  async register(userData) {\n    return this.post('/auth/register', userData);\n  }\n  async getProfile() {\n    return this.get('/auth/me');\n  }\n  async updateProfile(data) {\n    return this.put('/auth/profile', data);\n  }\n  async changePassword(currentPassword, newPassword) {\n    return this.put('/auth/change-password', {\n      currentPassword,\n      newPassword\n    });\n  }\n  async logout() {\n    return this.post('/auth/logout');\n  }\n\n  // Member methods\n  async getMembers(params) {\n    return this.get('/members', params);\n  }\n  async getMember(id) {\n    return this.get(`/members/${id}`);\n  }\n  async createMember(data) {\n    return this.post('/members', data);\n  }\n  async updateMember(id, data) {\n    return this.put(`/members/${id}`, data);\n  }\n  async deleteMember(id) {\n    return this.delete(`/members/${id}`);\n  }\n  async getActiveMembers() {\n    return this.get('/members/active');\n  }\n  async searchMembers(query) {\n    return this.get('/members/search', {\n      q: query\n    });\n  }\n\n  // Product methods\n  async getProducts(params) {\n    return this.get('/products', params);\n  }\n  async getProduct(id) {\n    return this.get(`/products/${id}`);\n  }\n  async createProduct(data) {\n    return this.post('/products', data);\n  }\n  async updateProduct(id, data) {\n    return this.put(`/products/${id}`, data);\n  }\n  async deleteProduct(id) {\n    return this.delete(`/products/${id}`);\n  }\n  async getActiveProducts() {\n    return this.get('/products/active');\n  }\n  async getLowStockProducts() {\n    return this.get('/products/low-stock');\n  }\n\n  // Sale methods\n  async getSales(params) {\n    return this.get('/sales', params);\n  }\n  async getSale(id) {\n    return this.get(`/sales/${id}`);\n  }\n  async createSale(data) {\n    return this.post('/sales', data);\n  }\n  async getTodaySales() {\n    return this.get('/sales/today');\n  }\n  async getSalesStats(startDate, endDate) {\n    return this.get('/sales/stats', {\n      startDate,\n      endDate\n    });\n  }\n\n  // Dashboard methods\n  async getDashboardStats() {\n    return this.get('/dashboard/stats');\n  }\n\n  // Health check\n  async healthCheck() {\n    return this.get('/health');\n  }\n}\nexport const apiService = new ApiService();\nexport default apiService;", "map": {"version": 3, "names": ["axios", "ApiService", "constructor", "api", "create", "baseURL", "process", "env", "REACT_APP_API_URL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "status", "removeItem", "window", "location", "href", "get", "url", "params", "data", "post", "put", "delete", "login", "email", "password", "register", "userData", "getProfile", "updateProfile", "changePassword", "currentPassword", "newPassword", "logout", "getMembers", "getMember", "id", "createMember", "updateMember", "deleteMember", "getActiveMembers", "searchMembers", "query", "q", "getProducts", "getProduct", "createProduct", "updateProduct", "deleteProduct", "getActiveProducts", "getLowStockProducts", "getSales", "getSale", "createSale", "getTodaySales", "getSalesStats", "startDate", "endDate", "getDashboardStats", "healthCheck", "apiService"], "sources": ["C:/Users/<USER>/Documents/augment-projects/coop/frontend/src/services/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosResponse } from 'axios';\nimport { ApiResponse } from '../types';\n\nclass ApiService {\n  private api: AxiosInstance;\n\n  constructor() {\n    this.api = axios.create({\n      baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',\n      timeout: 10000,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    // Request interceptor to add auth token\n    this.api.interceptors.request.use(\n      (config) => {\n        const token = localStorage.getItem('token');\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n      },\n      (error) => {\n        return Promise.reject(error);\n      }\n    );\n\n    // Response interceptor to handle errors\n    this.api.interceptors.response.use(\n      (response: AxiosResponse<ApiResponse>) => {\n        return response;\n      },\n      (error) => {\n        if (error.response?.status === 401) {\n          // Unauthorized - clear token and redirect to login\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n          window.location.href = '/login';\n        }\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  // Generic methods\n  async get<T>(url: string, params?: any): Promise<ApiResponse<T>> {\n    const response = await this.api.get<ApiResponse<T>>(url, { params });\n    return response.data;\n  }\n\n  async post<T>(url: string, data?: any): Promise<ApiResponse<T>> {\n    const response = await this.api.post<ApiResponse<T>>(url, data);\n    return response.data;\n  }\n\n  async put<T>(url: string, data?: any): Promise<ApiResponse<T>> {\n    const response = await this.api.put<ApiResponse<T>>(url, data);\n    return response.data;\n  }\n\n  async delete<T>(url: string): Promise<ApiResponse<T>> {\n    const response = await this.api.delete<ApiResponse<T>>(url);\n    return response.data;\n  }\n\n  // Auth methods\n  async login(email: string, password: string) {\n    return this.post('/auth/login', { email, password });\n  }\n\n  async register(userData: any) {\n    return this.post('/auth/register', userData);\n  }\n\n  async getProfile() {\n    return this.get('/auth/me');\n  }\n\n  async updateProfile(data: any) {\n    return this.put('/auth/profile', data);\n  }\n\n  async changePassword(currentPassword: string, newPassword: string) {\n    return this.put('/auth/change-password', { currentPassword, newPassword });\n  }\n\n  async logout() {\n    return this.post('/auth/logout');\n  }\n\n  // Member methods\n  async getMembers(params?: any) {\n    return this.get('/members', params);\n  }\n\n  async getMember(id: string) {\n    return this.get(`/members/${id}`);\n  }\n\n  async createMember(data: any) {\n    return this.post('/members', data);\n  }\n\n  async updateMember(id: string, data: any) {\n    return this.put(`/members/${id}`, data);\n  }\n\n  async deleteMember(id: string) {\n    return this.delete(`/members/${id}`);\n  }\n\n  async getActiveMembers() {\n    return this.get('/members/active');\n  }\n\n  async searchMembers(query: string) {\n    return this.get('/members/search', { q: query });\n  }\n\n  // Product methods\n  async getProducts(params?: any) {\n    return this.get('/products', params);\n  }\n\n  async getProduct(id: string) {\n    return this.get(`/products/${id}`);\n  }\n\n  async createProduct(data: any) {\n    return this.post('/products', data);\n  }\n\n  async updateProduct(id: string, data: any) {\n    return this.put(`/products/${id}`, data);\n  }\n\n  async deleteProduct(id: string) {\n    return this.delete(`/products/${id}`);\n  }\n\n  async getActiveProducts() {\n    return this.get('/products/active');\n  }\n\n  async getLowStockProducts() {\n    return this.get('/products/low-stock');\n  }\n\n  // Sale methods\n  async getSales(params?: any) {\n    return this.get('/sales', params);\n  }\n\n  async getSale(id: string) {\n    return this.get(`/sales/${id}`);\n  }\n\n  async createSale(data: any) {\n    return this.post('/sales', data);\n  }\n\n  async getTodaySales() {\n    return this.get('/sales/today');\n  }\n\n  async getSalesStats(startDate?: string, endDate?: string) {\n    return this.get('/sales/stats', { startDate, endDate });\n  }\n\n  // Dashboard methods\n  async getDashboardStats() {\n    return this.get('/dashboard/stats');\n  }\n\n  // Health check\n  async healthCheck() {\n    return this.get('/health');\n  }\n}\n\nexport const apiService = new ApiService();\nexport default apiService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAwC,OAAO;AAG3D,MAAMC,UAAU,CAAC;EAGfC,WAAWA,CAAA,EAAG;IAAA,KAFNC,GAAG;IAGT,IAAI,CAACA,GAAG,GAAGH,KAAK,CAACI,MAAM,CAAC;MACtBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;MACrEC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;;IAEF;IACA,IAAI,CAACP,GAAG,CAACQ,YAAY,CAACC,OAAO,CAACC,GAAG,CAC9BC,MAAM,IAAK;MACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAIF,KAAK,EAAE;QACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;MAClD;MACA,OAAOD,MAAM;IACf,CAAC,EACAK,KAAK,IAAK;MACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;;IAED;IACA,IAAI,CAAChB,GAAG,CAACQ,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC/BS,QAAoC,IAAK;MACxC,OAAOA,QAAQ;IACjB,CAAC,EACAH,KAAK,IAAK;MAAA,IAAAI,eAAA;MACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;QAClC;QACAR,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;QAChCT,YAAY,CAACS,UAAU,CAAC,MAAM,CAAC;QAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;MACjC;MACA,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;EACH;;EAEA;EACA,MAAMU,GAAGA,CAAIC,GAAW,EAAEC,MAAY,EAA2B;IAC/D,MAAMT,QAAQ,GAAG,MAAM,IAAI,CAACnB,GAAG,CAAC0B,GAAG,CAAiBC,GAAG,EAAE;MAAEC;IAAO,CAAC,CAAC;IACpE,OAAOT,QAAQ,CAACU,IAAI;EACtB;EAEA,MAAMC,IAAIA,CAAIH,GAAW,EAAEE,IAAU,EAA2B;IAC9D,MAAMV,QAAQ,GAAG,MAAM,IAAI,CAACnB,GAAG,CAAC8B,IAAI,CAAiBH,GAAG,EAAEE,IAAI,CAAC;IAC/D,OAAOV,QAAQ,CAACU,IAAI;EACtB;EAEA,MAAME,GAAGA,CAAIJ,GAAW,EAAEE,IAAU,EAA2B;IAC7D,MAAMV,QAAQ,GAAG,MAAM,IAAI,CAACnB,GAAG,CAAC+B,GAAG,CAAiBJ,GAAG,EAAEE,IAAI,CAAC;IAC9D,OAAOV,QAAQ,CAACU,IAAI;EACtB;EAEA,MAAMG,MAAMA,CAAIL,GAAW,EAA2B;IACpD,MAAMR,QAAQ,GAAG,MAAM,IAAI,CAACnB,GAAG,CAACgC,MAAM,CAAiBL,GAAG,CAAC;IAC3D,OAAOR,QAAQ,CAACU,IAAI;EACtB;;EAEA;EACA,MAAMI,KAAKA,CAACC,KAAa,EAAEC,QAAgB,EAAE;IAC3C,OAAO,IAAI,CAACL,IAAI,CAAC,aAAa,EAAE;MAAEI,KAAK;MAAEC;IAAS,CAAC,CAAC;EACtD;EAEA,MAAMC,QAAQA,CAACC,QAAa,EAAE;IAC5B,OAAO,IAAI,CAACP,IAAI,CAAC,gBAAgB,EAAEO,QAAQ,CAAC;EAC9C;EAEA,MAAMC,UAAUA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACZ,GAAG,CAAC,UAAU,CAAC;EAC7B;EAEA,MAAMa,aAAaA,CAACV,IAAS,EAAE;IAC7B,OAAO,IAAI,CAACE,GAAG,CAAC,eAAe,EAAEF,IAAI,CAAC;EACxC;EAEA,MAAMW,cAAcA,CAACC,eAAuB,EAAEC,WAAmB,EAAE;IACjE,OAAO,IAAI,CAACX,GAAG,CAAC,uBAAuB,EAAE;MAAEU,eAAe;MAAEC;IAAY,CAAC,CAAC;EAC5E;EAEA,MAAMC,MAAMA,CAAA,EAAG;IACb,OAAO,IAAI,CAACb,IAAI,CAAC,cAAc,CAAC;EAClC;;EAEA;EACA,MAAMc,UAAUA,CAAChB,MAAY,EAAE;IAC7B,OAAO,IAAI,CAACF,GAAG,CAAC,UAAU,EAAEE,MAAM,CAAC;EACrC;EAEA,MAAMiB,SAASA,CAACC,EAAU,EAAE;IAC1B,OAAO,IAAI,CAACpB,GAAG,CAAC,YAAYoB,EAAE,EAAE,CAAC;EACnC;EAEA,MAAMC,YAAYA,CAAClB,IAAS,EAAE;IAC5B,OAAO,IAAI,CAACC,IAAI,CAAC,UAAU,EAAED,IAAI,CAAC;EACpC;EAEA,MAAMmB,YAAYA,CAACF,EAAU,EAAEjB,IAAS,EAAE;IACxC,OAAO,IAAI,CAACE,GAAG,CAAC,YAAYe,EAAE,EAAE,EAAEjB,IAAI,CAAC;EACzC;EAEA,MAAMoB,YAAYA,CAACH,EAAU,EAAE;IAC7B,OAAO,IAAI,CAACd,MAAM,CAAC,YAAYc,EAAE,EAAE,CAAC;EACtC;EAEA,MAAMI,gBAAgBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAACxB,GAAG,CAAC,iBAAiB,CAAC;EACpC;EAEA,MAAMyB,aAAaA,CAACC,KAAa,EAAE;IACjC,OAAO,IAAI,CAAC1B,GAAG,CAAC,iBAAiB,EAAE;MAAE2B,CAAC,EAAED;IAAM,CAAC,CAAC;EAClD;;EAEA;EACA,MAAME,WAAWA,CAAC1B,MAAY,EAAE;IAC9B,OAAO,IAAI,CAACF,GAAG,CAAC,WAAW,EAAEE,MAAM,CAAC;EACtC;EAEA,MAAM2B,UAAUA,CAACT,EAAU,EAAE;IAC3B,OAAO,IAAI,CAACpB,GAAG,CAAC,aAAaoB,EAAE,EAAE,CAAC;EACpC;EAEA,MAAMU,aAAaA,CAAC3B,IAAS,EAAE;IAC7B,OAAO,IAAI,CAACC,IAAI,CAAC,WAAW,EAAED,IAAI,CAAC;EACrC;EAEA,MAAM4B,aAAaA,CAACX,EAAU,EAAEjB,IAAS,EAAE;IACzC,OAAO,IAAI,CAACE,GAAG,CAAC,aAAae,EAAE,EAAE,EAAEjB,IAAI,CAAC;EAC1C;EAEA,MAAM6B,aAAaA,CAACZ,EAAU,EAAE;IAC9B,OAAO,IAAI,CAACd,MAAM,CAAC,aAAac,EAAE,EAAE,CAAC;EACvC;EAEA,MAAMa,iBAAiBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAACjC,GAAG,CAAC,kBAAkB,CAAC;EACrC;EAEA,MAAMkC,mBAAmBA,CAAA,EAAG;IAC1B,OAAO,IAAI,CAAClC,GAAG,CAAC,qBAAqB,CAAC;EACxC;;EAEA;EACA,MAAMmC,QAAQA,CAACjC,MAAY,EAAE;IAC3B,OAAO,IAAI,CAACF,GAAG,CAAC,QAAQ,EAAEE,MAAM,CAAC;EACnC;EAEA,MAAMkC,OAAOA,CAAChB,EAAU,EAAE;IACxB,OAAO,IAAI,CAACpB,GAAG,CAAC,UAAUoB,EAAE,EAAE,CAAC;EACjC;EAEA,MAAMiB,UAAUA,CAAClC,IAAS,EAAE;IAC1B,OAAO,IAAI,CAACC,IAAI,CAAC,QAAQ,EAAED,IAAI,CAAC;EAClC;EAEA,MAAMmC,aAAaA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACtC,GAAG,CAAC,cAAc,CAAC;EACjC;EAEA,MAAMuC,aAAaA,CAACC,SAAkB,EAAEC,OAAgB,EAAE;IACxD,OAAO,IAAI,CAACzC,GAAG,CAAC,cAAc,EAAE;MAAEwC,SAAS;MAAEC;IAAQ,CAAC,CAAC;EACzD;;EAEA;EACA,MAAMC,iBAAiBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAAC1C,GAAG,CAAC,kBAAkB,CAAC;EACrC;;EAEA;EACA,MAAM2C,WAAWA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC3C,GAAG,CAAC,SAAS,CAAC;EAC5B;AACF;AAEA,OAAO,MAAM4C,UAAU,GAAG,IAAIxE,UAAU,CAAC,CAAC;AAC1C,eAAewE,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}