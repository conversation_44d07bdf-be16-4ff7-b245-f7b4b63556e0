import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false
  };

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Uncaught error:', error, errorInfo);
  }

  public render() {
    if (this.state.hasError) {
      return (
        <div className="error-page" style={{ 
          minHeight: '100vh', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          backgroundColor: '#f4f4f4'
        }}>
          <div className="error-content text-center">
            <div className="card" style={{ maxWidth: '500px', margin: 'auto' }}>
              <div className="card-header bg-danger text-white">
                <h3 className="card-title mb-0">
                  <i className="fas fa-exclamation-triangle mr-2"></i>
                  เกิดข้อผิดพลาด
                </h3>
              </div>
              <div className="card-body">
                <h4>ขออภัย เกิดข้อผิดพลาดในระบบ</h4>
                <p className="text-muted">
                  กรุณารีเฟรชหน้าเว็บหรือติดต่อผู้ดูแลระบบ
                </p>
                {this.state.error && (
                  <details className="mt-3">
                    <summary className="btn btn-sm btn-outline-secondary">
                      ดูรายละเอียดข้อผิดพลาด
                    </summary>
                    <pre className="mt-2 p-2 bg-light text-left" style={{ fontSize: '12px' }}>
                      {this.state.error.toString()}
                    </pre>
                  </details>
                )}
              </div>
              <div className="card-footer">
                <button 
                  className="btn btn-primary mr-2"
                  onClick={() => window.location.reload()}
                >
                  <i className="fas fa-sync-alt mr-1"></i>
                  รีเฟรชหน้า
                </button>
                <button 
                  className="btn btn-secondary"
                  onClick={() => window.location.href = '/'}
                >
                  <i className="fas fa-home mr-1"></i>
                  กลับหน้าแรก
                </button>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
