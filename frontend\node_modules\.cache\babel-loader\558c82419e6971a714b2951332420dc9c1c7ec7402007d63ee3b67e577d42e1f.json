{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\coop\\\\frontend\\\\src\\\\TestApp.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TestApp = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '20px',\n      fontFamily: 'Arial, sans-serif'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"\\uD83C\\uDF89 React App Test\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: '#f8f9fa',\n        padding: '20px',\n        borderRadius: '8px',\n        marginTop: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"\\u2705 React is Working!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Current time: \", new Date().toLocaleString('th-TH')]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Environment: \", process.env.NODE_ENV]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"API URL: \", process.env.REACT_APP_API_URL || 'http://localhost:5000/api']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\uD83D\\uDD27 Test Features:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 TypeScript compilation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 React rendering\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 CSS styling\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2705 Environment variables\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '20px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => alert('Button clicked!'),\n          style: {\n            backgroundColor: '#007bff',\n            color: 'white',\n            border: 'none',\n            padding: '10px 20px',\n            borderRadius: '5px',\n            cursor: 'pointer'\n          },\n          children: \"Test Button\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = TestApp;\nexport default TestApp;\nvar _c;\n$RefreshReg$(_c, \"TestApp\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "TestApp", "style", "padding", "fontFamily", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "backgroundColor", "borderRadius", "marginTop", "Date", "toLocaleString", "process", "env", "NODE_ENV", "REACT_APP_API_URL", "onClick", "alert", "color", "border", "cursor", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/coop/frontend/src/TestApp.tsx"], "sourcesContent": ["import React from 'react';\n\nconst TestApp: React.FC = () => {\n  return (\n    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>\n      <h1>🎉 React App Test</h1>\n      <div style={{ \n        backgroundColor: '#f8f9fa', \n        padding: '20px', \n        borderRadius: '8px',\n        marginTop: '20px'\n      }}>\n        <h2>✅ React is Working!</h2>\n        <p>Current time: {new Date().toLocaleString('th-TH')}</p>\n        <p>Environment: {process.env.NODE_ENV}</p>\n        <p>API URL: {process.env.REACT_APP_API_URL || 'http://localhost:5000/api'}</p>\n        \n        <div style={{ marginTop: '20px' }}>\n          <h3>🔧 Test Features:</h3>\n          <ul>\n            <li>✅ TypeScript compilation</li>\n            <li>✅ React rendering</li>\n            <li>✅ CSS styling</li>\n            <li>✅ Environment variables</li>\n          </ul>\n        </div>\n\n        <div style={{ marginTop: '20px' }}>\n          <button \n            onClick={() => alert('Button clicked!')}\n            style={{\n              backgroundColor: '#007bff',\n              color: 'white',\n              border: 'none',\n              padding: '10px 20px',\n              borderRadius: '5px',\n              cursor: 'pointer'\n            }}\n          >\n            Test Button\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TestApp;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,OAAiB,GAAGA,CAAA,KAAM;EAC9B,oBACED,OAAA;IAAKE,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,UAAU,EAAE;IAAoB,CAAE;IAAAC,QAAA,gBAC/DL,OAAA;MAAAK,QAAA,EAAI;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC1BT,OAAA;MAAKE,KAAK,EAAE;QACVQ,eAAe,EAAE,SAAS;QAC1BP,OAAO,EAAE,MAAM;QACfQ,YAAY,EAAE,KAAK;QACnBC,SAAS,EAAE;MACb,CAAE;MAAAP,QAAA,gBACAL,OAAA;QAAAK,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5BT,OAAA;QAAAK,QAAA,GAAG,gBAAc,EAAC,IAAIQ,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,OAAO,CAAC;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzDT,OAAA;QAAAK,QAAA,GAAG,eAAa,EAACU,OAAO,CAACC,GAAG,CAACC,QAAQ;MAAA;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1CT,OAAA;QAAAK,QAAA,GAAG,WAAS,EAACU,OAAO,CAACC,GAAG,CAACE,iBAAiB,IAAI,2BAA2B;MAAA;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAE9ET,OAAA;QAAKE,KAAK,EAAE;UAAEU,SAAS,EAAE;QAAO,CAAE;QAAAP,QAAA,gBAChCL,OAAA;UAAAK,QAAA,EAAI;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1BT,OAAA;UAAAK,QAAA,gBACEL,OAAA;YAAAK,QAAA,EAAI;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjCT,OAAA;YAAAK,QAAA,EAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BT,OAAA;YAAAK,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtBT,OAAA;YAAAK,QAAA,EAAI;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAENT,OAAA;QAAKE,KAAK,EAAE;UAAEU,SAAS,EAAE;QAAO,CAAE;QAAAP,QAAA,eAChCL,OAAA;UACEmB,OAAO,EAAEA,CAAA,KAAMC,KAAK,CAAC,iBAAiB,CAAE;UACxClB,KAAK,EAAE;YACLQ,eAAe,EAAE,SAAS;YAC1BW,KAAK,EAAE,OAAO;YACdC,MAAM,EAAE,MAAM;YACdnB,OAAO,EAAE,WAAW;YACpBQ,YAAY,EAAE,KAAK;YACnBY,MAAM,EAAE;UACV,CAAE;UAAAlB,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACe,EAAA,GA3CIvB,OAAiB;AA6CvB,eAAeA,OAAO;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}