"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const dotenv_1 = __importDefault(require("dotenv"));
const database_1 = __importDefault(require("./config/database"));
const routes_1 = __importDefault(require("./routes"));
const errorHandler_1 = require("./middleware/errorHandler");
const User_1 = require("./models/User");
dotenv_1.default.config();
const app = (0, express_1.default)();
const PORT = process.env.PORT || 5000;
(0, database_1.default)();
app.use((0, helmet_1.default)());
app.use((0, cors_1.default)({
    origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
    credentials: true
}));
app.use((0, morgan_1.default)('combined'));
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
app.use('/api', routes_1.default);
const createDefaultAdmin = async () => {
    try {
        const adminExists = await User_1.User.findOne({ role: 'admin' });
        if (!adminExists) {
            await User_1.User.create({
                email: process.env.ADMIN_EMAIL || '<EMAIL>',
                password: process.env.ADMIN_PASSWORD || 'admin123',
                name: process.env.ADMIN_NAME || 'ผู้ดูแลระบบ',
                role: 'admin'
            });
            console.log('✅ Default admin user created');
        }
    }
    catch (error) {
        console.error('❌ Error creating default admin:', error);
    }
};
app.use(errorHandler_1.notFound);
app.use(errorHandler_1.errorHandler);
app.listen(PORT, async () => {
    console.log(`🚀 Server running on port ${PORT}`);
    console.log(`📱 Environment: ${process.env.NODE_ENV}`);
    await createDefaultAdmin();
});
exports.default = app;
//# sourceMappingURL=index.js.map