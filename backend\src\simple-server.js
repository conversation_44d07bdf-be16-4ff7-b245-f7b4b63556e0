const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
require('dotenv').config();

// Import database and models
const database = require('./config/database');
const User = require('./models/User');
const Member = require('./models/Member');
const Product = require('./models/Product');

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors({
  origin: 'http://localhost:3000',
  credentials: true
}));
app.use(express.json());

// Database Connection
let isMongoConnected = false;

// Initialize database connection
const initializeDatabase = async () => {
  try {
    await database.connect();
    await database.createIndexes();
    await database.seedData();
    isMongoConnected = true;
    console.log('✅ Database initialized successfully');
  } catch (error) {
    console.error('❌ Database initialization failed:', error.message);
    console.log('⚠️ Using in-memory data as fallback');
    isMongoConnected = false;
  }
};

// Models are imported from separate files

// JWT Helper
const generateToken = (payload) => {
  return jwt.sign(payload, process.env.JWT_SECRET, { expiresIn: process.env.JWT_EXPIRES_IN });
};

// In-memory data for testing when MongoDB is not available
const inMemoryUsers = [
  {
    _id: '1',
    email: '<EMAIL>',
    password: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uIoO', // admin123
    name: 'ผู้ดูแลระบบ',
    role: 'admin',
    isActive: true
  }
];

const inMemoryMembers = [
  {
    _id: '1',
    memberCode: 'M000001',
    name: 'นักเรียนตัวอย่าง 1',
    email: '<EMAIL>',
    phone: '0812345678',
    shares: 10,
    totalPurchase: 5000,
    isActive: true
  },
  {
    _id: '2',
    memberCode: 'M000002',
    name: 'นักเรียนตัวอย่าง 2',
    email: '<EMAIL>',
    phone: '0823456789',
    shares: 5,
    totalPurchase: 2500,
    isActive: true
  }
];

const inMemoryProducts = [
  {
    _id: '1',
    productCode: 'P000001',
    name: 'ปากกาลูกลื่น',
    category: 'เครื่องเขียน',
    costPrice: 8,
    sellingPrice: 12,
    stock: 100,
    unit: 'ด้าม',
    isActive: true
  },
  {
    _id: '2',
    productCode: 'P000002',
    name: 'สมุดบันทึก',
    category: 'เครื่องเขียน',
    costPrice: 15,
    sellingPrice: 25,
    stock: 50,
    unit: 'เล่ม',
    isActive: true
  }
];

// Auth Routes
app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    let user = null;

    if (isMongoConnected) {
      // Use MongoDB
      user = await User.findOne({ email }).select('+password');
      if (user && !await user.comparePassword(password)) {
        user = null;
      }
    } else {
      // Use in-memory data
      user = inMemoryUsers.find(u => u.email === email);
      if (user && password !== 'admin123') { // Simple password check for demo
        user = null;
      }
    }

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'อีเมลหรือรหัสผ่านไม่ถูกต้อง'
      });
    }

    const token = generateToken({
      userId: user._id,
      email: user.email,
      role: user.role
    });

    res.json({
      success: true,
      message: 'เข้าสู่ระบบสำเร็จ',
      data: {
        user: {
          id: user._id,
          email: user.email,
          name: user.name,
          role: user.role,
          isActive: user.isActive
        },
        token
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์'
    });
  }
});

// Member Routes
app.get('/api/members', async (req, res) => {
  try {
    let members = [];

    if (isMongoConnected) {
      members = await Member.find({ isActive: true }).sort({ createdAt: -1 });
    } else {
      members = inMemoryMembers.filter(m => m.isActive);
    }

    res.json({
      success: true,
      message: 'ดึงข้อมูลสมาชิกสำเร็จ',
      data: {
        data: members,
        pagination: {
          page: 1,
          limit: 10,
          total: members.length,
          pages: 1,
          hasNext: false,
          hasPrev: false
        }
      }
    });
  } catch (error) {
    console.error('Members error:', error);
    res.status(500).json({
      success: false,
      message: 'เกิดข้อผิดพลาดในการดึงข้อมูลสมาชิก'
    });
  }
});

// Product Routes
app.get('/api/products', async (req, res) => {
  try {
    let products = [];

    if (isMongoConnected) {
      products = await Product.find({ isActive: true }).sort({ createdAt: -1 });
    } else {
      products = inMemoryProducts.filter(p => p.isActive);
    }

    res.json({
      success: true,
      message: 'ดึงข้อมูลสินค้าสำเร็จ',
      data: {
        data: products,
        pagination: {
          page: 1,
          limit: 10,
          total: products.length,
          pages: 1,
          hasNext: false,
          hasPrev: false
        }
      }
    });
  } catch (error) {
    console.error('Products error:', error);
    res.status(500).json({
      success: false,
      message: 'เกิดข้อผิดพลาดในการดึงข้อมูลสินค้า'
    });
  }
});

// Dashboard Stats
app.get('/api/dashboard/stats', async (req, res) => {
  try {
    let totalMembers = 0;
    let totalProducts = 0;

    if (isMongoConnected) {
      totalMembers = await Member.countDocuments({ isActive: true });
      totalProducts = await Product.countDocuments({ isActive: true });
    } else {
      totalMembers = inMemoryMembers.filter(m => m.isActive).length;
      totalProducts = inMemoryProducts.filter(p => p.isActive).length;
    }

    res.json({
      success: true,
      message: 'ดึงข้อมูลแดชบอร์ดสำเร็จ',
      data: {
        totalMembers,
        totalProducts,
        totalSales: 125000,
        totalProfit: 25000,
        todaySales: 5500,
        todayProfit: 1100,
        lowStockProducts: 3,
        recentSales: []
      }
    });
  } catch (error) {
    console.error('Dashboard error:', error);
    res.status(500).json({
      success: false,
      message: 'เกิดข้อผิดพลาดในการดึงข้อมูลแดชบอร์ด'
    });
  }
});

// Health Check
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'API is running',
    timestamp: new Date().toISOString()
  });
});

// Start server
const startServer = async () => {
  // Initialize database
  await initializeDatabase();

  // Start HTTP server
  app.listen(PORT, () => {
    console.log(`🚀 Server running on port ${PORT}`);
    console.log(`📱 Environment: ${process.env.NODE_ENV}`);
    console.log(`🌐 CORS Origin: ${process.env.CORS_ORIGIN}`);
    console.log(`💾 Database: ${isMongoConnected ? 'MongoDB' : 'In-Memory'}`);
  });
};

// Start the application
startServer().catch(error => {
  console.error('❌ Failed to start server:', error);
  process.exit(1);
});

module.exports = app;
