const express = require('express');
const cors = require('cors');
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors({
  origin: 'http://localhost:3000',
  credentials: true
}));
app.use(express.json());

// MongoDB Connection (Optional - will use in-memory data if not available)
const MONGODB_URI = 'mongodb://localhost:27017/student_coop';
let isMongoConnected = false;

mongoose.connect(MONGODB_URI)
  .then(() => {
    console.log('✅ MongoDB Connected');
    isMongoConnected = true;
  })
  .catch(err => {
    console.log('⚠️ MongoDB not available, using in-memory data');
    isMongoConnected = false;
  });

// User Schema
const userSchema = new mongoose.Schema({
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  name: { type: String, required: true },
  role: { type: String, enum: ['admin', 'member'], default: 'member' },
  isActive: { type: Boolean, default: true }
}, { timestamps: true });

userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  this.password = await bcrypt.hash(this.password, 12);
  next();
});

userSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

const User = mongoose.model('User', userSchema);

// Member Schema
const memberSchema = new mongoose.Schema({
  memberCode: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  email: String,
  phone: String,
  shares: { type: Number, default: 0 },
  totalPurchase: { type: Number, default: 0 },
  isActive: { type: Boolean, default: true }
}, { timestamps: true });

const Member = mongoose.model('Member', memberSchema);

// Product Schema
const productSchema = new mongoose.Schema({
  productCode: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  category: { type: String, required: true },
  costPrice: { type: Number, required: true },
  sellingPrice: { type: Number, required: true },
  stock: { type: Number, default: 0 },
  unit: { type: String, required: true },
  isActive: { type: Boolean, default: true }
}, { timestamps: true });

const Product = mongoose.model('Product', productSchema);

// JWT Helper
const generateToken = (payload) => {
  return jwt.sign(payload, 'student_coop_secret_key_2024', { expiresIn: '7d' });
};

// In-memory data for testing when MongoDB is not available
const inMemoryUsers = [
  {
    _id: '1',
    email: '<EMAIL>',
    password: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uIoO', // admin123
    name: 'ผู้ดูแลระบบ',
    role: 'admin',
    isActive: true
  }
];

const inMemoryMembers = [
  {
    _id: '1',
    memberCode: 'M000001',
    name: 'นักเรียนตัวอย่าง 1',
    email: '<EMAIL>',
    phone: '0812345678',
    shares: 10,
    totalPurchase: 5000,
    isActive: true
  },
  {
    _id: '2',
    memberCode: 'M000002',
    name: 'นักเรียนตัวอย่าง 2',
    email: '<EMAIL>',
    phone: '0823456789',
    shares: 5,
    totalPurchase: 2500,
    isActive: true
  }
];

const inMemoryProducts = [
  {
    _id: '1',
    productCode: 'P000001',
    name: 'ปากกาลูกลื่น',
    category: 'เครื่องเขียน',
    costPrice: 8,
    sellingPrice: 12,
    stock: 100,
    unit: 'ด้าม',
    isActive: true
  },
  {
    _id: '2',
    productCode: 'P000002',
    name: 'สมุดบันทึก',
    category: 'เครื่องเขียน',
    costPrice: 15,
    sellingPrice: 25,
    stock: 50,
    unit: 'เล่ม',
    isActive: true
  }
];

// Auth Routes
app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    let user = null;

    if (isMongoConnected) {
      // Use MongoDB
      user = await User.findOne({ email }).select('+password');
      if (user && !await user.comparePassword(password)) {
        user = null;
      }
    } else {
      // Use in-memory data
      user = inMemoryUsers.find(u => u.email === email);
      if (user && password !== 'admin123') { // Simple password check for demo
        user = null;
      }
    }

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'อีเมลหรือรหัสผ่านไม่ถูกต้อง'
      });
    }

    const token = generateToken({
      userId: user._id,
      email: user.email,
      role: user.role
    });

    res.json({
      success: true,
      message: 'เข้าสู่ระบบสำเร็จ',
      data: {
        user: {
          id: user._id,
          email: user.email,
          name: user.name,
          role: user.role,
          isActive: user.isActive
        },
        token
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์'
    });
  }
});

// Member Routes
app.get('/api/members', async (req, res) => {
  try {
    let members = [];

    if (isMongoConnected) {
      members = await Member.find({ isActive: true }).sort({ createdAt: -1 });
    } else {
      members = inMemoryMembers.filter(m => m.isActive);
    }

    res.json({
      success: true,
      message: 'ดึงข้อมูลสมาชิกสำเร็จ',
      data: {
        data: members,
        pagination: {
          page: 1,
          limit: 10,
          total: members.length,
          pages: 1,
          hasNext: false,
          hasPrev: false
        }
      }
    });
  } catch (error) {
    console.error('Members error:', error);
    res.status(500).json({
      success: false,
      message: 'เกิดข้อผิดพลาดในการดึงข้อมูลสมาชิก'
    });
  }
});

// Product Routes
app.get('/api/products', async (req, res) => {
  try {
    let products = [];

    if (isMongoConnected) {
      products = await Product.find({ isActive: true }).sort({ createdAt: -1 });
    } else {
      products = inMemoryProducts.filter(p => p.isActive);
    }

    res.json({
      success: true,
      message: 'ดึงข้อมูลสินค้าสำเร็จ',
      data: {
        data: products,
        pagination: {
          page: 1,
          limit: 10,
          total: products.length,
          pages: 1,
          hasNext: false,
          hasPrev: false
        }
      }
    });
  } catch (error) {
    console.error('Products error:', error);
    res.status(500).json({
      success: false,
      message: 'เกิดข้อผิดพลาดในการดึงข้อมูลสินค้า'
    });
  }
});

// Dashboard Stats
app.get('/api/dashboard/stats', async (req, res) => {
  try {
    let totalMembers = 0;
    let totalProducts = 0;

    if (isMongoConnected) {
      totalMembers = await Member.countDocuments({ isActive: true });
      totalProducts = await Product.countDocuments({ isActive: true });
    } else {
      totalMembers = inMemoryMembers.filter(m => m.isActive).length;
      totalProducts = inMemoryProducts.filter(p => p.isActive).length;
    }

    res.json({
      success: true,
      message: 'ดึงข้อมูลแดชบอร์ดสำเร็จ',
      data: {
        totalMembers,
        totalProducts,
        totalSales: 125000,
        totalProfit: 25000,
        todaySales: 5500,
        todayProfit: 1100,
        lowStockProducts: 3,
        recentSales: []
      }
    });
  } catch (error) {
    console.error('Dashboard error:', error);
    res.status(500).json({
      success: false,
      message: 'เกิดข้อผิดพลาดในการดึงข้อมูลแดชบอร์ด'
    });
  }
});

// Health Check
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'API is running',
    timestamp: new Date().toISOString()
  });
});

// Create default admin user
const createDefaultAdmin = async () => {
  try {
    const adminExists = await User.findOne({ role: 'admin' });
    if (!adminExists) {
      await User.create({
        email: '<EMAIL>',
        password: 'admin123',
        name: 'ผู้ดูแลระบบ',
        role: 'admin'
      });
      console.log('✅ Default admin user created');
    }
  } catch (error) {
    console.error('❌ Error creating default admin:', error);
  }
};

// Start server
app.listen(PORT, async () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📱 Environment: development`);
  await createDefaultAdmin();
});

module.exports = app;
