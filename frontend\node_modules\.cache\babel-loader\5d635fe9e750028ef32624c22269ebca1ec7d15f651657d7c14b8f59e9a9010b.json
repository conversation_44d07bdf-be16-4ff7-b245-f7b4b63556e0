{"ast": null, "code": "/*!\n* sweetalert2 v11.22.0\n* Released under the MIT License.\n*/\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() : typeof define === 'function' && define.amd ? define(factory) : (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global.Sweetalert2 = factory());\n})(this, function () {\n  'use strict';\n\n  function _assertClassBrand(e, t, n) {\n    if (\"function\" == typeof e ? e === t : e.has(t)) return arguments.length < 3 ? t : n;\n    throw new TypeError(\"Private element is not present on this object\");\n  }\n  function _checkPrivateRedeclaration(e, t) {\n    if (t.has(e)) throw new TypeError(\"Cannot initialize the same private elements twice on an object\");\n  }\n  function _classPrivateFieldGet2(s, a) {\n    return s.get(_assertClassBrand(s, a));\n  }\n  function _classPrivateFieldInitSpec(e, t, a) {\n    _checkPrivateRedeclaration(e, t), t.set(e, a);\n  }\n  function _classPrivateFieldSet2(s, a, r) {\n    return s.set(_assertClassBrand(s, a), r), r;\n  }\n  const RESTORE_FOCUS_TIMEOUT = 100;\n\n  /** @type {GlobalState} */\n  const globalState = {};\n  const focusPreviousActiveElement = () => {\n    if (globalState.previousActiveElement instanceof HTMLElement) {\n      globalState.previousActiveElement.focus();\n      globalState.previousActiveElement = null;\n    } else if (document.body) {\n      document.body.focus();\n    }\n  };\n\n  /**\n   * Restore previous active (focused) element\n   *\n   * @param {boolean} returnFocus\n   * @returns {Promise<void>}\n   */\n  const restoreActiveElement = returnFocus => {\n    return new Promise(resolve => {\n      if (!returnFocus) {\n        return resolve();\n      }\n      const x = window.scrollX;\n      const y = window.scrollY;\n      globalState.restoreFocusTimeout = setTimeout(() => {\n        focusPreviousActiveElement();\n        resolve();\n      }, RESTORE_FOCUS_TIMEOUT); // issues/900\n\n      window.scrollTo(x, y);\n    });\n  };\n  const swalPrefix = 'swal2-';\n\n  /**\n   * @typedef {Record<SwalClass, string>} SwalClasses\n   */\n\n  /**\n   * @typedef {'success' | 'warning' | 'info' | 'question' | 'error'} SwalIcon\n   * @typedef {Record<SwalIcon, string>} SwalIcons\n   */\n\n  /** @type {SwalClass[]} */\n  const classNames = ['container', 'shown', 'height-auto', 'iosfix', 'popup', 'modal', 'no-backdrop', 'no-transition', 'toast', 'toast-shown', 'show', 'hide', 'close', 'title', 'html-container', 'actions', 'confirm', 'deny', 'cancel', 'footer', 'icon', 'icon-content', 'image', 'input', 'file', 'range', 'select', 'radio', 'checkbox', 'label', 'textarea', 'inputerror', 'input-label', 'validation-message', 'progress-steps', 'active-progress-step', 'progress-step', 'progress-step-line', 'loader', 'loading', 'styled', 'top', 'top-start', 'top-end', 'top-left', 'top-right', 'center', 'center-start', 'center-end', 'center-left', 'center-right', 'bottom', 'bottom-start', 'bottom-end', 'bottom-left', 'bottom-right', 'grow-row', 'grow-column', 'grow-fullscreen', 'rtl', 'timer-progress-bar', 'timer-progress-bar-container', 'scrollbar-measure', 'icon-success', 'icon-warning', 'icon-info', 'icon-question', 'icon-error', 'draggable', 'dragging'];\n  const swalClasses = classNames.reduce((acc, className) => {\n    acc[className] = swalPrefix + className;\n    return acc;\n  }, /** @type {SwalClasses} */{});\n\n  /** @type {SwalIcon[]} */\n  const icons = ['success', 'warning', 'info', 'question', 'error'];\n  const iconTypes = icons.reduce((acc, icon) => {\n    acc[icon] = swalPrefix + icon;\n    return acc;\n  }, /** @type {SwalIcons} */{});\n  const consolePrefix = 'SweetAlert2:';\n\n  /**\n   * Capitalize the first letter of a string\n   *\n   * @param {string} str\n   * @returns {string}\n   */\n  const capitalizeFirstLetter = str => str.charAt(0).toUpperCase() + str.slice(1);\n\n  /**\n   * Standardize console warnings\n   *\n   * @param {string | string[]} message\n   */\n  const warn = message => {\n    console.warn(`${consolePrefix} ${typeof message === 'object' ? message.join(' ') : message}`);\n  };\n\n  /**\n   * Standardize console errors\n   *\n   * @param {string} message\n   */\n  const error = message => {\n    console.error(`${consolePrefix} ${message}`);\n  };\n\n  /**\n   * Private global state for `warnOnce`\n   *\n   * @type {string[]}\n   * @private\n   */\n  const previousWarnOnceMessages = [];\n\n  /**\n   * Show a console warning, but only if it hasn't already been shown\n   *\n   * @param {string} message\n   */\n  const warnOnce = message => {\n    if (!previousWarnOnceMessages.includes(message)) {\n      previousWarnOnceMessages.push(message);\n      warn(message);\n    }\n  };\n\n  /**\n   * Show a one-time console warning about deprecated params/methods\n   *\n   * @param {string} deprecatedParam\n   * @param {string?} useInstead\n   */\n  const warnAboutDeprecation = (deprecatedParam, useInstead = null) => {\n    warnOnce(`\"${deprecatedParam}\" is deprecated and will be removed in the next major release.${useInstead ? ` Use \"${useInstead}\" instead.` : ''}`);\n  };\n\n  /**\n   * If `arg` is a function, call it (with no arguments or context) and return the result.\n   * Otherwise, just pass the value through\n   *\n   * @param {Function | any} arg\n   * @returns {any}\n   */\n  const callIfFunction = arg => typeof arg === 'function' ? arg() : arg;\n\n  /**\n   * @param {any} arg\n   * @returns {boolean}\n   */\n  const hasToPromiseFn = arg => arg && typeof arg.toPromise === 'function';\n\n  /**\n   * @param {any} arg\n   * @returns {Promise<any>}\n   */\n  const asPromise = arg => hasToPromiseFn(arg) ? arg.toPromise() : Promise.resolve(arg);\n\n  /**\n   * @param {any} arg\n   * @returns {boolean}\n   */\n  const isPromise = arg => arg && Promise.resolve(arg) === arg;\n\n  /**\n   * Gets the popup container which contains the backdrop and the popup itself.\n   *\n   * @returns {HTMLElement | null}\n   */\n  const getContainer = () => document.body.querySelector(`.${swalClasses.container}`);\n\n  /**\n   * @param {string} selectorString\n   * @returns {HTMLElement | null}\n   */\n  const elementBySelector = selectorString => {\n    const container = getContainer();\n    return container ? container.querySelector(selectorString) : null;\n  };\n\n  /**\n   * @param {string} className\n   * @returns {HTMLElement | null}\n   */\n  const elementByClass = className => {\n    return elementBySelector(`.${className}`);\n  };\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getPopup = () => elementByClass(swalClasses.popup);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getIcon = () => elementByClass(swalClasses.icon);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getIconContent = () => elementByClass(swalClasses['icon-content']);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getTitle = () => elementByClass(swalClasses.title);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getHtmlContainer = () => elementByClass(swalClasses['html-container']);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getImage = () => elementByClass(swalClasses.image);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getProgressSteps = () => elementByClass(swalClasses['progress-steps']);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getValidationMessage = () => elementByClass(swalClasses['validation-message']);\n\n  /**\n   * @returns {HTMLButtonElement | null}\n   */\n  const getConfirmButton = () => (/** @type {HTMLButtonElement} */elementBySelector(`.${swalClasses.actions} .${swalClasses.confirm}`));\n\n  /**\n   * @returns {HTMLButtonElement | null}\n   */\n  const getCancelButton = () => (/** @type {HTMLButtonElement} */elementBySelector(`.${swalClasses.actions} .${swalClasses.cancel}`));\n\n  /**\n   * @returns {HTMLButtonElement | null}\n   */\n  const getDenyButton = () => (/** @type {HTMLButtonElement} */elementBySelector(`.${swalClasses.actions} .${swalClasses.deny}`));\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getInputLabel = () => elementByClass(swalClasses['input-label']);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getLoader = () => elementBySelector(`.${swalClasses.loader}`);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getActions = () => elementByClass(swalClasses.actions);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getFooter = () => elementByClass(swalClasses.footer);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getTimerProgressBar = () => elementByClass(swalClasses['timer-progress-bar']);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getCloseButton = () => elementByClass(swalClasses.close);\n\n  // https://github.com/jkup/focusable/blob/master/index.js\n  const focusable = `\n  a[href],\n  area[href],\n  input:not([disabled]),\n  select:not([disabled]),\n  textarea:not([disabled]),\n  button:not([disabled]),\n  iframe,\n  object,\n  embed,\n  [tabindex=\"0\"],\n  [contenteditable],\n  audio[controls],\n  video[controls],\n  summary\n`;\n  /**\n   * @returns {HTMLElement[]}\n   */\n  const getFocusableElements = () => {\n    const popup = getPopup();\n    if (!popup) {\n      return [];\n    }\n    /** @type {NodeListOf<HTMLElement>} */\n    const focusableElementsWithTabindex = popup.querySelectorAll('[tabindex]:not([tabindex=\"-1\"]):not([tabindex=\"0\"])');\n    const focusableElementsWithTabindexSorted = Array.from(focusableElementsWithTabindex)\n    // sort according to tabindex\n    .sort((a, b) => {\n      const tabindexA = parseInt(a.getAttribute('tabindex') || '0');\n      const tabindexB = parseInt(b.getAttribute('tabindex') || '0');\n      if (tabindexA > tabindexB) {\n        return 1;\n      } else if (tabindexA < tabindexB) {\n        return -1;\n      }\n      return 0;\n    });\n\n    /** @type {NodeListOf<HTMLElement>} */\n    const otherFocusableElements = popup.querySelectorAll(focusable);\n    const otherFocusableElementsFiltered = Array.from(otherFocusableElements).filter(el => el.getAttribute('tabindex') !== '-1');\n    return [...new Set(focusableElementsWithTabindexSorted.concat(otherFocusableElementsFiltered))].filter(el => isVisible$1(el));\n  };\n\n  /**\n   * @returns {boolean}\n   */\n  const isModal = () => {\n    return hasClass(document.body, swalClasses.shown) && !hasClass(document.body, swalClasses['toast-shown']) && !hasClass(document.body, swalClasses['no-backdrop']);\n  };\n\n  /**\n   * @returns {boolean}\n   */\n  const isToast = () => {\n    const popup = getPopup();\n    if (!popup) {\n      return false;\n    }\n    return hasClass(popup, swalClasses.toast);\n  };\n\n  /**\n   * @returns {boolean}\n   */\n  const isLoading = () => {\n    const popup = getPopup();\n    if (!popup) {\n      return false;\n    }\n    return popup.hasAttribute('data-loading');\n  };\n\n  /**\n   * Securely set innerHTML of an element\n   * https://github.com/sweetalert2/sweetalert2/issues/1926\n   *\n   * @param {HTMLElement} elem\n   * @param {string} html\n   */\n  const setInnerHtml = (elem, html) => {\n    elem.textContent = '';\n    if (html) {\n      const parser = new DOMParser();\n      const parsed = parser.parseFromString(html, `text/html`);\n      const head = parsed.querySelector('head');\n      if (head) {\n        Array.from(head.childNodes).forEach(child => {\n          elem.appendChild(child);\n        });\n      }\n      const body = parsed.querySelector('body');\n      if (body) {\n        Array.from(body.childNodes).forEach(child => {\n          if (child instanceof HTMLVideoElement || child instanceof HTMLAudioElement) {\n            elem.appendChild(child.cloneNode(true)); // https://github.com/sweetalert2/sweetalert2/issues/2507\n          } else {\n            elem.appendChild(child);\n          }\n        });\n      }\n    }\n  };\n\n  /**\n   * @param {HTMLElement} elem\n   * @param {string} className\n   * @returns {boolean}\n   */\n  const hasClass = (elem, className) => {\n    if (!className) {\n      return false;\n    }\n    const classList = className.split(/\\s+/);\n    for (let i = 0; i < classList.length; i++) {\n      if (!elem.classList.contains(classList[i])) {\n        return false;\n      }\n    }\n    return true;\n  };\n\n  /**\n   * @param {HTMLElement} elem\n   * @param {SweetAlertOptions} params\n   */\n  const removeCustomClasses = (elem, params) => {\n    Array.from(elem.classList).forEach(className => {\n      if (!Object.values(swalClasses).includes(className) && !Object.values(iconTypes).includes(className) && !Object.values(params.showClass || {}).includes(className)) {\n        elem.classList.remove(className);\n      }\n    });\n  };\n\n  /**\n   * @param {HTMLElement} elem\n   * @param {SweetAlertOptions} params\n   * @param {string} className\n   */\n  const applyCustomClass = (elem, params, className) => {\n    removeCustomClasses(elem, params);\n    if (!params.customClass) {\n      return;\n    }\n    const customClass = params.customClass[(/** @type {keyof SweetAlertCustomClass} */className)];\n    if (!customClass) {\n      return;\n    }\n    if (typeof customClass !== 'string' && !customClass.forEach) {\n      warn(`Invalid type of customClass.${className}! Expected string or iterable object, got \"${typeof customClass}\"`);\n      return;\n    }\n    addClass(elem, customClass);\n  };\n\n  /**\n   * @param {HTMLElement} popup\n   * @param {import('./renderers/renderInput').InputClass | SweetAlertInput} inputClass\n   * @returns {HTMLInputElement | null}\n   */\n  const getInput$1 = (popup, inputClass) => {\n    if (!inputClass) {\n      return null;\n    }\n    switch (inputClass) {\n      case 'select':\n      case 'textarea':\n      case 'file':\n        return popup.querySelector(`.${swalClasses.popup} > .${swalClasses[inputClass]}`);\n      case 'checkbox':\n        return popup.querySelector(`.${swalClasses.popup} > .${swalClasses.checkbox} input`);\n      case 'radio':\n        return popup.querySelector(`.${swalClasses.popup} > .${swalClasses.radio} input:checked`) || popup.querySelector(`.${swalClasses.popup} > .${swalClasses.radio} input:first-child`);\n      case 'range':\n        return popup.querySelector(`.${swalClasses.popup} > .${swalClasses.range} input`);\n      default:\n        return popup.querySelector(`.${swalClasses.popup} > .${swalClasses.input}`);\n    }\n  };\n\n  /**\n   * @param {HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement} input\n   */\n  const focusInput = input => {\n    input.focus();\n\n    // place cursor at end of text in text input\n    if (input.type !== 'file') {\n      // http://stackoverflow.com/a/2345915\n      const val = input.value;\n      input.value = '';\n      input.value = val;\n    }\n  };\n\n  /**\n   * @param {HTMLElement | HTMLElement[] | null} target\n   * @param {string | string[] | readonly string[] | undefined} classList\n   * @param {boolean} condition\n   */\n  const toggleClass = (target, classList, condition) => {\n    if (!target || !classList) {\n      return;\n    }\n    if (typeof classList === 'string') {\n      classList = classList.split(/\\s+/).filter(Boolean);\n    }\n    classList.forEach(className => {\n      if (Array.isArray(target)) {\n        target.forEach(elem => {\n          if (condition) {\n            elem.classList.add(className);\n          } else {\n            elem.classList.remove(className);\n          }\n        });\n      } else {\n        if (condition) {\n          target.classList.add(className);\n        } else {\n          target.classList.remove(className);\n        }\n      }\n    });\n  };\n\n  /**\n   * @param {HTMLElement | HTMLElement[] | null} target\n   * @param {string | string[] | readonly string[] | undefined} classList\n   */\n  const addClass = (target, classList) => {\n    toggleClass(target, classList, true);\n  };\n\n  /**\n   * @param {HTMLElement | HTMLElement[] | null} target\n   * @param {string | string[] | readonly string[] | undefined} classList\n   */\n  const removeClass = (target, classList) => {\n    toggleClass(target, classList, false);\n  };\n\n  /**\n   * Get direct child of an element by class name\n   *\n   * @param {HTMLElement} elem\n   * @param {string} className\n   * @returns {HTMLElement | undefined}\n   */\n  const getDirectChildByClass = (elem, className) => {\n    const children = Array.from(elem.children);\n    for (let i = 0; i < children.length; i++) {\n      const child = children[i];\n      if (child instanceof HTMLElement && hasClass(child, className)) {\n        return child;\n      }\n    }\n  };\n\n  /**\n   * @param {HTMLElement} elem\n   * @param {string} property\n   * @param {*} value\n   */\n  const applyNumericalStyle = (elem, property, value) => {\n    if (value === `${parseInt(value)}`) {\n      value = parseInt(value);\n    }\n    if (value || parseInt(value) === 0) {\n      elem.style.setProperty(property, typeof value === 'number' ? `${value}px` : value);\n    } else {\n      elem.style.removeProperty(property);\n    }\n  };\n\n  /**\n   * @param {HTMLElement | null} elem\n   * @param {string} display\n   */\n  const show = (elem, display = 'flex') => {\n    if (!elem) {\n      return;\n    }\n    elem.style.display = display;\n  };\n\n  /**\n   * @param {HTMLElement | null} elem\n   */\n  const hide = elem => {\n    if (!elem) {\n      return;\n    }\n    elem.style.display = 'none';\n  };\n\n  /**\n   * @param {HTMLElement | null} elem\n   * @param {string} display\n   */\n  const showWhenInnerHtmlPresent = (elem, display = 'block') => {\n    if (!elem) {\n      return;\n    }\n    new MutationObserver(() => {\n      toggle(elem, elem.innerHTML, display);\n    }).observe(elem, {\n      childList: true,\n      subtree: true\n    });\n  };\n\n  /**\n   * @param {HTMLElement} parent\n   * @param {string} selector\n   * @param {string} property\n   * @param {string} value\n   */\n  const setStyle = (parent, selector, property, value) => {\n    /** @type {HTMLElement | null} */\n    const el = parent.querySelector(selector);\n    if (el) {\n      el.style.setProperty(property, value);\n    }\n  };\n\n  /**\n   * @param {HTMLElement} elem\n   * @param {any} condition\n   * @param {string} display\n   */\n  const toggle = (elem, condition, display = 'flex') => {\n    if (condition) {\n      show(elem, display);\n    } else {\n      hide(elem);\n    }\n  };\n\n  /**\n   * borrowed from jquery $(elem).is(':visible') implementation\n   *\n   * @param {HTMLElement | null} elem\n   * @returns {boolean}\n   */\n  const isVisible$1 = elem => !!(elem && (elem.offsetWidth || elem.offsetHeight || elem.getClientRects().length));\n\n  /**\n   * @returns {boolean}\n   */\n  const allButtonsAreHidden = () => !isVisible$1(getConfirmButton()) && !isVisible$1(getDenyButton()) && !isVisible$1(getCancelButton());\n\n  /**\n   * @param {HTMLElement} elem\n   * @returns {boolean}\n   */\n  const isScrollable = elem => !!(elem.scrollHeight > elem.clientHeight);\n\n  /**\n   * @param {HTMLElement} element\n   * @param {HTMLElement} stopElement\n   * @returns {boolean}\n   */\n  const selfOrParentIsScrollable = (element, stopElement) => {\n    let parent = element;\n    while (parent && parent !== stopElement) {\n      if (isScrollable(parent)) {\n        return true;\n      }\n      parent = parent.parentElement;\n    }\n    return false;\n  };\n\n  /**\n   * borrowed from https://stackoverflow.com/a/46352119\n   *\n   * @param {HTMLElement} elem\n   * @returns {boolean}\n   */\n  const hasCssAnimation = elem => {\n    const style = window.getComputedStyle(elem);\n    const animDuration = parseFloat(style.getPropertyValue('animation-duration') || '0');\n    const transDuration = parseFloat(style.getPropertyValue('transition-duration') || '0');\n    return animDuration > 0 || transDuration > 0;\n  };\n\n  /**\n   * @param {number} timer\n   * @param {boolean} reset\n   */\n  const animateTimerProgressBar = (timer, reset = false) => {\n    const timerProgressBar = getTimerProgressBar();\n    if (!timerProgressBar) {\n      return;\n    }\n    if (isVisible$1(timerProgressBar)) {\n      if (reset) {\n        timerProgressBar.style.transition = 'none';\n        timerProgressBar.style.width = '100%';\n      }\n      setTimeout(() => {\n        timerProgressBar.style.transition = `width ${timer / 1000}s linear`;\n        timerProgressBar.style.width = '0%';\n      }, 10);\n    }\n  };\n  const stopTimerProgressBar = () => {\n    const timerProgressBar = getTimerProgressBar();\n    if (!timerProgressBar) {\n      return;\n    }\n    const timerProgressBarWidth = parseInt(window.getComputedStyle(timerProgressBar).width);\n    timerProgressBar.style.removeProperty('transition');\n    timerProgressBar.style.width = '100%';\n    const timerProgressBarFullWidth = parseInt(window.getComputedStyle(timerProgressBar).width);\n    const timerProgressBarPercent = timerProgressBarWidth / timerProgressBarFullWidth * 100;\n    timerProgressBar.style.width = `${timerProgressBarPercent}%`;\n  };\n\n  /**\n   * Detect Node env\n   *\n   * @returns {boolean}\n   */\n  const isNodeEnv = () => typeof window === 'undefined' || typeof document === 'undefined';\n  const sweetHTML = `\n <div aria-labelledby=\"${swalClasses.title}\" aria-describedby=\"${swalClasses['html-container']}\" class=\"${swalClasses.popup}\" tabindex=\"-1\">\n   <button type=\"button\" class=\"${swalClasses.close}\"></button>\n   <ul class=\"${swalClasses['progress-steps']}\"></ul>\n   <div class=\"${swalClasses.icon}\"></div>\n   <img class=\"${swalClasses.image}\" />\n   <h2 class=\"${swalClasses.title}\" id=\"${swalClasses.title}\"></h2>\n   <div class=\"${swalClasses['html-container']}\" id=\"${swalClasses['html-container']}\"></div>\n   <input class=\"${swalClasses.input}\" id=\"${swalClasses.input}\" />\n   <input type=\"file\" class=\"${swalClasses.file}\" />\n   <div class=\"${swalClasses.range}\">\n     <input type=\"range\" />\n     <output></output>\n   </div>\n   <select class=\"${swalClasses.select}\" id=\"${swalClasses.select}\"></select>\n   <div class=\"${swalClasses.radio}\"></div>\n   <label class=\"${swalClasses.checkbox}\">\n     <input type=\"checkbox\" id=\"${swalClasses.checkbox}\" />\n     <span class=\"${swalClasses.label}\"></span>\n   </label>\n   <textarea class=\"${swalClasses.textarea}\" id=\"${swalClasses.textarea}\"></textarea>\n   <div class=\"${swalClasses['validation-message']}\" id=\"${swalClasses['validation-message']}\"></div>\n   <div class=\"${swalClasses.actions}\">\n     <div class=\"${swalClasses.loader}\"></div>\n     <button type=\"button\" class=\"${swalClasses.confirm}\"></button>\n     <button type=\"button\" class=\"${swalClasses.deny}\"></button>\n     <button type=\"button\" class=\"${swalClasses.cancel}\"></button>\n   </div>\n   <div class=\"${swalClasses.footer}\"></div>\n   <div class=\"${swalClasses['timer-progress-bar-container']}\">\n     <div class=\"${swalClasses['timer-progress-bar']}\"></div>\n   </div>\n </div>\n`.replace(/(^|\\n)\\s*/g, '');\n\n  /**\n   * @returns {boolean}\n   */\n  const resetOldContainer = () => {\n    const oldContainer = getContainer();\n    if (!oldContainer) {\n      return false;\n    }\n    oldContainer.remove();\n    removeClass([document.documentElement, document.body], [swalClasses['no-backdrop'], swalClasses['toast-shown'], swalClasses['has-column']]);\n    return true;\n  };\n  const resetValidationMessage$1 = () => {\n    globalState.currentInstance.resetValidationMessage();\n  };\n  const addInputChangeListeners = () => {\n    const popup = getPopup();\n    const input = getDirectChildByClass(popup, swalClasses.input);\n    const file = getDirectChildByClass(popup, swalClasses.file);\n    /** @type {HTMLInputElement} */\n    const range = popup.querySelector(`.${swalClasses.range} input`);\n    /** @type {HTMLOutputElement} */\n    const rangeOutput = popup.querySelector(`.${swalClasses.range} output`);\n    const select = getDirectChildByClass(popup, swalClasses.select);\n    /** @type {HTMLInputElement} */\n    const checkbox = popup.querySelector(`.${swalClasses.checkbox} input`);\n    const textarea = getDirectChildByClass(popup, swalClasses.textarea);\n    input.oninput = resetValidationMessage$1;\n    file.onchange = resetValidationMessage$1;\n    select.onchange = resetValidationMessage$1;\n    checkbox.onchange = resetValidationMessage$1;\n    textarea.oninput = resetValidationMessage$1;\n    range.oninput = () => {\n      resetValidationMessage$1();\n      rangeOutput.value = range.value;\n    };\n    range.onchange = () => {\n      resetValidationMessage$1();\n      rangeOutput.value = range.value;\n    };\n  };\n\n  /**\n   * @param {string | HTMLElement} target\n   * @returns {HTMLElement}\n   */\n  const getTarget = target => typeof target === 'string' ? document.querySelector(target) : target;\n\n  /**\n   * @param {SweetAlertOptions} params\n   */\n  const setupAccessibility = params => {\n    const popup = getPopup();\n    popup.setAttribute('role', params.toast ? 'alert' : 'dialog');\n    popup.setAttribute('aria-live', params.toast ? 'polite' : 'assertive');\n    if (!params.toast) {\n      popup.setAttribute('aria-modal', 'true');\n    }\n  };\n\n  /**\n   * @param {HTMLElement} targetElement\n   */\n  const setupRTL = targetElement => {\n    if (window.getComputedStyle(targetElement).direction === 'rtl') {\n      addClass(getContainer(), swalClasses.rtl);\n    }\n  };\n\n  /**\n   * Add modal + backdrop + no-war message for Russians to DOM\n   *\n   * @param {SweetAlertOptions} params\n   */\n  const init = params => {\n    // Clean up the old popup container if it exists\n    const oldContainerExisted = resetOldContainer();\n    if (isNodeEnv()) {\n      error('SweetAlert2 requires document to initialize');\n      return;\n    }\n    const container = document.createElement('div');\n    container.className = swalClasses.container;\n    if (oldContainerExisted) {\n      addClass(container, swalClasses['no-transition']);\n    }\n    setInnerHtml(container, sweetHTML);\n    container.dataset['swal2Theme'] = params.theme;\n    const targetElement = getTarget(params.target);\n    targetElement.appendChild(container);\n    if (params.topLayer) {\n      container.setAttribute('popover', '');\n      container.showPopover();\n    }\n    setupAccessibility(params);\n    setupRTL(targetElement);\n    addInputChangeListeners();\n  };\n\n  /**\n   * @param {HTMLElement | object | string} param\n   * @param {HTMLElement} target\n   */\n  const parseHtmlToContainer = (param, target) => {\n    // DOM element\n    if (param instanceof HTMLElement) {\n      target.appendChild(param);\n    }\n\n    // Object\n    else if (typeof param === 'object') {\n      handleObject(param, target);\n    }\n\n    // Plain string\n    else if (param) {\n      setInnerHtml(target, param);\n    }\n  };\n\n  /**\n   * @param {any} param\n   * @param {HTMLElement} target\n   */\n  const handleObject = (param, target) => {\n    // JQuery element(s)\n    if (param.jquery) {\n      handleJqueryElem(target, param);\n    }\n\n    // For other objects use their string representation\n    else {\n      setInnerHtml(target, param.toString());\n    }\n  };\n\n  /**\n   * @param {HTMLElement} target\n   * @param {any} elem\n   */\n  const handleJqueryElem = (target, elem) => {\n    target.textContent = '';\n    if (0 in elem) {\n      for (let i = 0; i in elem; i++) {\n        target.appendChild(elem[i].cloneNode(true));\n      }\n    } else {\n      target.appendChild(elem.cloneNode(true));\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderActions = (instance, params) => {\n    const actions = getActions();\n    const loader = getLoader();\n    if (!actions || !loader) {\n      return;\n    }\n\n    // Actions (buttons) wrapper\n    if (!params.showConfirmButton && !params.showDenyButton && !params.showCancelButton) {\n      hide(actions);\n    } else {\n      show(actions);\n    }\n\n    // Custom class\n    applyCustomClass(actions, params, 'actions');\n\n    // Render all the buttons\n    renderButtons(actions, loader, params);\n\n    // Loader\n    setInnerHtml(loader, params.loaderHtml || '');\n    applyCustomClass(loader, params, 'loader');\n  };\n\n  /**\n   * @param {HTMLElement} actions\n   * @param {HTMLElement} loader\n   * @param {SweetAlertOptions} params\n   */\n  function renderButtons(actions, loader, params) {\n    const confirmButton = getConfirmButton();\n    const denyButton = getDenyButton();\n    const cancelButton = getCancelButton();\n    if (!confirmButton || !denyButton || !cancelButton) {\n      return;\n    }\n\n    // Render buttons\n    renderButton(confirmButton, 'confirm', params);\n    renderButton(denyButton, 'deny', params);\n    renderButton(cancelButton, 'cancel', params);\n    handleButtonsStyling(confirmButton, denyButton, cancelButton, params);\n    if (params.reverseButtons) {\n      if (params.toast) {\n        actions.insertBefore(cancelButton, confirmButton);\n        actions.insertBefore(denyButton, confirmButton);\n      } else {\n        actions.insertBefore(cancelButton, loader);\n        actions.insertBefore(denyButton, loader);\n        actions.insertBefore(confirmButton, loader);\n      }\n    }\n  }\n\n  /**\n   * @param {HTMLElement} confirmButton\n   * @param {HTMLElement} denyButton\n   * @param {HTMLElement} cancelButton\n   * @param {SweetAlertOptions} params\n   */\n  function handleButtonsStyling(confirmButton, denyButton, cancelButton, params) {\n    if (!params.buttonsStyling) {\n      removeClass([confirmButton, denyButton, cancelButton], swalClasses.styled);\n      return;\n    }\n    addClass([confirmButton, denyButton, cancelButton], swalClasses.styled);\n\n    // Apply custom background colors to action buttons\n    if (params.confirmButtonColor) {\n      confirmButton.style.setProperty('--swal2-confirm-button-background-color', params.confirmButtonColor);\n    }\n    if (params.denyButtonColor) {\n      denyButton.style.setProperty('--swal2-deny-button-background-color', params.denyButtonColor);\n    }\n    if (params.cancelButtonColor) {\n      cancelButton.style.setProperty('--swal2-cancel-button-background-color', params.cancelButtonColor);\n    }\n\n    // Apply the outline color to action buttons\n    applyOutlineColor(confirmButton);\n    applyOutlineColor(denyButton);\n    applyOutlineColor(cancelButton);\n  }\n\n  /**\n   * @param {HTMLElement} button\n   */\n  function applyOutlineColor(button) {\n    const buttonStyle = window.getComputedStyle(button);\n    if (buttonStyle.getPropertyValue('--swal2-action-button-focus-box-shadow')) {\n      // If the button already has a custom outline color, no need to change it\n      return;\n    }\n    const outlineColor = buttonStyle.backgroundColor.replace(/rgba?\\((\\d+), (\\d+), (\\d+).*/, 'rgba($1, $2, $3, 0.5)');\n    button.style.setProperty('--swal2-action-button-focus-box-shadow', buttonStyle.getPropertyValue('--swal2-outline').replace(/ rgba\\(.*/, ` ${outlineColor}`));\n  }\n\n  /**\n   * @param {HTMLElement} button\n   * @param {'confirm' | 'deny' | 'cancel'} buttonType\n   * @param {SweetAlertOptions} params\n   */\n  function renderButton(button, buttonType, params) {\n    const buttonName = /** @type {'Confirm' | 'Deny' | 'Cancel'} */capitalizeFirstLetter(buttonType);\n    toggle(button, params[`show${buttonName}Button`], 'inline-block');\n    setInnerHtml(button, params[`${buttonType}ButtonText`] || ''); // Set caption text\n    button.setAttribute('aria-label', params[`${buttonType}ButtonAriaLabel`] || ''); // ARIA label\n\n    // Add buttons custom classes\n    button.className = swalClasses[buttonType];\n    applyCustomClass(button, params, `${buttonType}Button`);\n  }\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderCloseButton = (instance, params) => {\n    const closeButton = getCloseButton();\n    if (!closeButton) {\n      return;\n    }\n    setInnerHtml(closeButton, params.closeButtonHtml || '');\n\n    // Custom class\n    applyCustomClass(closeButton, params, 'closeButton');\n    toggle(closeButton, params.showCloseButton);\n    closeButton.setAttribute('aria-label', params.closeButtonAriaLabel || '');\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderContainer = (instance, params) => {\n    const container = getContainer();\n    if (!container) {\n      return;\n    }\n    handleBackdropParam(container, params.backdrop);\n    handlePositionParam(container, params.position);\n    handleGrowParam(container, params.grow);\n\n    // Custom class\n    applyCustomClass(container, params, 'container');\n  };\n\n  /**\n   * @param {HTMLElement} container\n   * @param {SweetAlertOptions['backdrop']} backdrop\n   */\n  function handleBackdropParam(container, backdrop) {\n    if (typeof backdrop === 'string') {\n      container.style.background = backdrop;\n    } else if (!backdrop) {\n      addClass([document.documentElement, document.body], swalClasses['no-backdrop']);\n    }\n  }\n\n  /**\n   * @param {HTMLElement} container\n   * @param {SweetAlertOptions['position']} position\n   */\n  function handlePositionParam(container, position) {\n    if (!position) {\n      return;\n    }\n    if (position in swalClasses) {\n      addClass(container, swalClasses[position]);\n    } else {\n      warn('The \"position\" parameter is not valid, defaulting to \"center\"');\n      addClass(container, swalClasses.center);\n    }\n  }\n\n  /**\n   * @param {HTMLElement} container\n   * @param {SweetAlertOptions['grow']} grow\n   */\n  function handleGrowParam(container, grow) {\n    if (!grow) {\n      return;\n    }\n    addClass(container, swalClasses[`grow-${grow}`]);\n  }\n\n  /**\n   * This module contains `WeakMap`s for each effectively-\"private  property\" that a `Swal` has.\n   * For example, to set the private property \"foo\" of `this` to \"bar\", you can `privateProps.foo.set(this, 'bar')`\n   * This is the approach that Babel will probably take to implement private methods/fields\n   *   https://github.com/tc39/proposal-private-methods\n   *   https://github.com/babel/babel/pull/7555\n   * Once we have the changes from that PR in Babel, and our core class fits reasonable in *one module*\n   *   then we can use that language feature.\n   */\n\n  var privateProps = {\n    innerParams: new WeakMap(),\n    domCache: new WeakMap()\n  };\n\n  /// <reference path=\"../../../../sweetalert2.d.ts\"/>\n\n  /** @type {InputClass[]} */\n  const inputClasses = ['input', 'file', 'range', 'select', 'radio', 'checkbox', 'textarea'];\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderInput = (instance, params) => {\n    const popup = getPopup();\n    if (!popup) {\n      return;\n    }\n    const innerParams = privateProps.innerParams.get(instance);\n    const rerender = !innerParams || params.input !== innerParams.input;\n    inputClasses.forEach(inputClass => {\n      const inputContainer = getDirectChildByClass(popup, swalClasses[inputClass]);\n      if (!inputContainer) {\n        return;\n      }\n\n      // set attributes\n      setAttributes(inputClass, params.inputAttributes);\n\n      // set class\n      inputContainer.className = swalClasses[inputClass];\n      if (rerender) {\n        hide(inputContainer);\n      }\n    });\n    if (params.input) {\n      if (rerender) {\n        showInput(params);\n      }\n      // set custom class\n      setCustomClass(params);\n    }\n  };\n\n  /**\n   * @param {SweetAlertOptions} params\n   */\n  const showInput = params => {\n    if (!params.input) {\n      return;\n    }\n    if (!renderInputType[params.input]) {\n      error(`Unexpected type of input! Expected ${Object.keys(renderInputType).join(' | ')}, got \"${params.input}\"`);\n      return;\n    }\n    const inputContainer = getInputContainer(params.input);\n    if (!inputContainer) {\n      return;\n    }\n    const input = renderInputType[params.input](inputContainer, params);\n    show(inputContainer);\n\n    // input autofocus\n    if (params.inputAutoFocus) {\n      setTimeout(() => {\n        focusInput(input);\n      });\n    }\n  };\n\n  /**\n   * @param {HTMLInputElement} input\n   */\n  const removeAttributes = input => {\n    for (let i = 0; i < input.attributes.length; i++) {\n      const attrName = input.attributes[i].name;\n      if (!['id', 'type', 'value', 'style'].includes(attrName)) {\n        input.removeAttribute(attrName);\n      }\n    }\n  };\n\n  /**\n   * @param {InputClass} inputClass\n   * @param {SweetAlertOptions['inputAttributes']} inputAttributes\n   */\n  const setAttributes = (inputClass, inputAttributes) => {\n    const popup = getPopup();\n    if (!popup) {\n      return;\n    }\n    const input = getInput$1(popup, inputClass);\n    if (!input) {\n      return;\n    }\n    removeAttributes(input);\n    for (const attr in inputAttributes) {\n      input.setAttribute(attr, inputAttributes[attr]);\n    }\n  };\n\n  /**\n   * @param {SweetAlertOptions} params\n   */\n  const setCustomClass = params => {\n    if (!params.input) {\n      return;\n    }\n    const inputContainer = getInputContainer(params.input);\n    if (inputContainer) {\n      applyCustomClass(inputContainer, params, 'input');\n    }\n  };\n\n  /**\n   * @param {HTMLInputElement | HTMLTextAreaElement} input\n   * @param {SweetAlertOptions} params\n   */\n  const setInputPlaceholder = (input, params) => {\n    if (!input.placeholder && params.inputPlaceholder) {\n      input.placeholder = params.inputPlaceholder;\n    }\n  };\n\n  /**\n   * @param {Input} input\n   * @param {Input} prependTo\n   * @param {SweetAlertOptions} params\n   */\n  const setInputLabel = (input, prependTo, params) => {\n    if (params.inputLabel) {\n      const label = document.createElement('label');\n      const labelClass = swalClasses['input-label'];\n      label.setAttribute('for', input.id);\n      label.className = labelClass;\n      if (typeof params.customClass === 'object') {\n        addClass(label, params.customClass.inputLabel);\n      }\n      label.innerText = params.inputLabel;\n      prependTo.insertAdjacentElement('beforebegin', label);\n    }\n  };\n\n  /**\n   * @param {SweetAlertInput} inputType\n   * @returns {HTMLElement | undefined}\n   */\n  const getInputContainer = inputType => {\n    const popup = getPopup();\n    if (!popup) {\n      return;\n    }\n    return getDirectChildByClass(popup, swalClasses[(/** @type {SwalClass} */inputType)] || swalClasses.input);\n  };\n\n  /**\n   * @param {HTMLInputElement | HTMLOutputElement | HTMLTextAreaElement} input\n   * @param {SweetAlertOptions['inputValue']} inputValue\n   */\n  const checkAndSetInputValue = (input, inputValue) => {\n    if (['string', 'number'].includes(typeof inputValue)) {\n      input.value = `${inputValue}`;\n    } else if (!isPromise(inputValue)) {\n      warn(`Unexpected type of inputValue! Expected \"string\", \"number\" or \"Promise\", got \"${typeof inputValue}\"`);\n    }\n  };\n\n  /** @type {Record<SweetAlertInput, (input: Input | HTMLElement, params: SweetAlertOptions) => Input>} */\n  const renderInputType = {};\n\n  /**\n   * @param {HTMLInputElement} input\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLInputElement}\n   */\n  renderInputType.text = renderInputType.email = renderInputType.password = renderInputType.number = renderInputType.tel = renderInputType.url = renderInputType.search = renderInputType.date = renderInputType['datetime-local'] = renderInputType.time = renderInputType.week = renderInputType.month = /** @type {(input: Input | HTMLElement, params: SweetAlertOptions) => Input} */\n  (input, params) => {\n    checkAndSetInputValue(input, params.inputValue);\n    setInputLabel(input, input, params);\n    setInputPlaceholder(input, params);\n    input.type = params.input;\n    return input;\n  };\n\n  /**\n   * @param {HTMLInputElement} input\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLInputElement}\n   */\n  renderInputType.file = (input, params) => {\n    setInputLabel(input, input, params);\n    setInputPlaceholder(input, params);\n    return input;\n  };\n\n  /**\n   * @param {HTMLInputElement} range\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLInputElement}\n   */\n  renderInputType.range = (range, params) => {\n    const rangeInput = range.querySelector('input');\n    const rangeOutput = range.querySelector('output');\n    checkAndSetInputValue(rangeInput, params.inputValue);\n    rangeInput.type = params.input;\n    checkAndSetInputValue(rangeOutput, params.inputValue);\n    setInputLabel(rangeInput, range, params);\n    return range;\n  };\n\n  /**\n   * @param {HTMLSelectElement} select\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLSelectElement}\n   */\n  renderInputType.select = (select, params) => {\n    select.textContent = '';\n    if (params.inputPlaceholder) {\n      const placeholder = document.createElement('option');\n      setInnerHtml(placeholder, params.inputPlaceholder);\n      placeholder.value = '';\n      placeholder.disabled = true;\n      placeholder.selected = true;\n      select.appendChild(placeholder);\n    }\n    setInputLabel(select, select, params);\n    return select;\n  };\n\n  /**\n   * @param {HTMLInputElement} radio\n   * @returns {HTMLInputElement}\n   */\n  renderInputType.radio = radio => {\n    radio.textContent = '';\n    return radio;\n  };\n\n  /**\n   * @param {HTMLLabelElement} checkboxContainer\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLInputElement}\n   */\n  renderInputType.checkbox = (checkboxContainer, params) => {\n    const checkbox = getInput$1(getPopup(), 'checkbox');\n    checkbox.value = '1';\n    checkbox.checked = Boolean(params.inputValue);\n    const label = checkboxContainer.querySelector('span');\n    setInnerHtml(label, params.inputPlaceholder || params.inputLabel);\n    return checkbox;\n  };\n\n  /**\n   * @param {HTMLTextAreaElement} textarea\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLTextAreaElement}\n   */\n  renderInputType.textarea = (textarea, params) => {\n    checkAndSetInputValue(textarea, params.inputValue);\n    setInputPlaceholder(textarea, params);\n    setInputLabel(textarea, textarea, params);\n\n    /**\n     * @param {HTMLElement} el\n     * @returns {number}\n     */\n    const getMargin = el => parseInt(window.getComputedStyle(el).marginLeft) + parseInt(window.getComputedStyle(el).marginRight);\n\n    // https://github.com/sweetalert2/sweetalert2/issues/2291\n    setTimeout(() => {\n      // https://github.com/sweetalert2/sweetalert2/issues/1699\n      if ('MutationObserver' in window) {\n        const initialPopupWidth = parseInt(window.getComputedStyle(getPopup()).width);\n        const textareaResizeHandler = () => {\n          // check if texarea is still in document (i.e. popup wasn't closed in the meantime)\n          if (!document.body.contains(textarea)) {\n            return;\n          }\n          const textareaWidth = textarea.offsetWidth + getMargin(textarea);\n          if (textareaWidth > initialPopupWidth) {\n            getPopup().style.width = `${textareaWidth}px`;\n          } else {\n            applyNumericalStyle(getPopup(), 'width', params.width);\n          }\n        };\n        new MutationObserver(textareaResizeHandler).observe(textarea, {\n          attributes: true,\n          attributeFilter: ['style']\n        });\n      }\n    });\n    return textarea;\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderContent = (instance, params) => {\n    const htmlContainer = getHtmlContainer();\n    if (!htmlContainer) {\n      return;\n    }\n    showWhenInnerHtmlPresent(htmlContainer);\n    applyCustomClass(htmlContainer, params, 'htmlContainer');\n\n    // Content as HTML\n    if (params.html) {\n      parseHtmlToContainer(params.html, htmlContainer);\n      show(htmlContainer, 'block');\n    }\n\n    // Content as plain text\n    else if (params.text) {\n      htmlContainer.textContent = params.text;\n      show(htmlContainer, 'block');\n    }\n\n    // No content\n    else {\n      hide(htmlContainer);\n    }\n    renderInput(instance, params);\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderFooter = (instance, params) => {\n    const footer = getFooter();\n    if (!footer) {\n      return;\n    }\n    showWhenInnerHtmlPresent(footer);\n    toggle(footer, params.footer, 'block');\n    if (params.footer) {\n      parseHtmlToContainer(params.footer, footer);\n    }\n\n    // Custom class\n    applyCustomClass(footer, params, 'footer');\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderIcon = (instance, params) => {\n    const innerParams = privateProps.innerParams.get(instance);\n    const icon = getIcon();\n    if (!icon) {\n      return;\n    }\n\n    // if the given icon already rendered, apply the styling without re-rendering the icon\n    if (innerParams && params.icon === innerParams.icon) {\n      // Custom or default content\n      setContent(icon, params);\n      applyStyles(icon, params);\n      return;\n    }\n    if (!params.icon && !params.iconHtml) {\n      hide(icon);\n      return;\n    }\n    if (params.icon && Object.keys(iconTypes).indexOf(params.icon) === -1) {\n      error(`Unknown icon! Expected \"success\", \"error\", \"warning\", \"info\" or \"question\", got \"${params.icon}\"`);\n      hide(icon);\n      return;\n    }\n    show(icon);\n\n    // Custom or default content\n    setContent(icon, params);\n    applyStyles(icon, params);\n\n    // Animate icon\n    addClass(icon, params.showClass && params.showClass.icon);\n\n    // Re-adjust the success icon on system theme change\n    const colorSchemeQueryList = window.matchMedia('(prefers-color-scheme: dark)');\n    colorSchemeQueryList.addEventListener('change', adjustSuccessIconBackgroundColor);\n  };\n\n  /**\n   * @param {HTMLElement} icon\n   * @param {SweetAlertOptions} params\n   */\n  const applyStyles = (icon, params) => {\n    for (const [iconType, iconClassName] of Object.entries(iconTypes)) {\n      if (params.icon !== iconType) {\n        removeClass(icon, iconClassName);\n      }\n    }\n    addClass(icon, params.icon && iconTypes[params.icon]);\n\n    // Icon color\n    setColor(icon, params);\n\n    // Success icon background color\n    adjustSuccessIconBackgroundColor();\n\n    // Custom class\n    applyCustomClass(icon, params, 'icon');\n  };\n\n  // Adjust success icon background color to match the popup background color\n  const adjustSuccessIconBackgroundColor = () => {\n    const popup = getPopup();\n    if (!popup) {\n      return;\n    }\n    const popupBackgroundColor = window.getComputedStyle(popup).getPropertyValue('background-color');\n    /** @type {NodeListOf<HTMLElement>} */\n    const successIconParts = popup.querySelectorAll('[class^=swal2-success-circular-line], .swal2-success-fix');\n    for (let i = 0; i < successIconParts.length; i++) {\n      successIconParts[i].style.backgroundColor = popupBackgroundColor;\n    }\n  };\n  const successIconHtml = `\n  <div class=\"swal2-success-circular-line-left\"></div>\n  <span class=\"swal2-success-line-tip\"></span> <span class=\"swal2-success-line-long\"></span>\n  <div class=\"swal2-success-ring\"></div> <div class=\"swal2-success-fix\"></div>\n  <div class=\"swal2-success-circular-line-right\"></div>\n`;\n  const errorIconHtml = `\n  <span class=\"swal2-x-mark\">\n    <span class=\"swal2-x-mark-line-left\"></span>\n    <span class=\"swal2-x-mark-line-right\"></span>\n  </span>\n`;\n\n  /**\n   * @param {HTMLElement} icon\n   * @param {SweetAlertOptions} params\n   */\n  const setContent = (icon, params) => {\n    if (!params.icon && !params.iconHtml) {\n      return;\n    }\n    let oldContent = icon.innerHTML;\n    let newContent = '';\n    if (params.iconHtml) {\n      newContent = iconContent(params.iconHtml);\n    } else if (params.icon === 'success') {\n      newContent = successIconHtml;\n      oldContent = oldContent.replace(/ style=\".*?\"/g, ''); // undo adjustSuccessIconBackgroundColor()\n    } else if (params.icon === 'error') {\n      newContent = errorIconHtml;\n    } else if (params.icon) {\n      const defaultIconHtml = {\n        question: '?',\n        warning: '!',\n        info: 'i'\n      };\n      newContent = iconContent(defaultIconHtml[params.icon]);\n    }\n    if (oldContent.trim() !== newContent.trim()) {\n      setInnerHtml(icon, newContent);\n    }\n  };\n\n  /**\n   * @param {HTMLElement} icon\n   * @param {SweetAlertOptions} params\n   */\n  const setColor = (icon, params) => {\n    if (!params.iconColor) {\n      return;\n    }\n    icon.style.color = params.iconColor;\n    icon.style.borderColor = params.iconColor;\n    for (const sel of ['.swal2-success-line-tip', '.swal2-success-line-long', '.swal2-x-mark-line-left', '.swal2-x-mark-line-right']) {\n      setStyle(icon, sel, 'background-color', params.iconColor);\n    }\n    setStyle(icon, '.swal2-success-ring', 'border-color', params.iconColor);\n  };\n\n  /**\n   * @param {string} content\n   * @returns {string}\n   */\n  const iconContent = content => `<div class=\"${swalClasses['icon-content']}\">${content}</div>`;\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderImage = (instance, params) => {\n    const image = getImage();\n    if (!image) {\n      return;\n    }\n    if (!params.imageUrl) {\n      hide(image);\n      return;\n    }\n    show(image, '');\n\n    // Src, alt\n    image.setAttribute('src', params.imageUrl);\n    image.setAttribute('alt', params.imageAlt || '');\n\n    // Width, height\n    applyNumericalStyle(image, 'width', params.imageWidth);\n    applyNumericalStyle(image, 'height', params.imageHeight);\n\n    // Class\n    image.className = swalClasses.image;\n    applyCustomClass(image, params, 'image');\n  };\n  let dragging = false;\n  let mousedownX = 0;\n  let mousedownY = 0;\n  let initialX = 0;\n  let initialY = 0;\n\n  /**\n   * @param {HTMLElement} popup\n   */\n  const addDraggableListeners = popup => {\n    popup.addEventListener('mousedown', down);\n    document.body.addEventListener('mousemove', move);\n    popup.addEventListener('mouseup', up);\n    popup.addEventListener('touchstart', down);\n    document.body.addEventListener('touchmove', move);\n    popup.addEventListener('touchend', up);\n  };\n\n  /**\n   * @param {HTMLElement} popup\n   */\n  const removeDraggableListeners = popup => {\n    popup.removeEventListener('mousedown', down);\n    document.body.removeEventListener('mousemove', move);\n    popup.removeEventListener('mouseup', up);\n    popup.removeEventListener('touchstart', down);\n    document.body.removeEventListener('touchmove', move);\n    popup.removeEventListener('touchend', up);\n  };\n\n  /**\n   * @param {MouseEvent | TouchEvent} event\n   */\n  const down = event => {\n    const popup = getPopup();\n    if (event.target === popup || getIcon().contains(/** @type {HTMLElement} */event.target)) {\n      dragging = true;\n      const clientXY = getClientXY(event);\n      mousedownX = clientXY.clientX;\n      mousedownY = clientXY.clientY;\n      initialX = parseInt(popup.style.insetInlineStart) || 0;\n      initialY = parseInt(popup.style.insetBlockStart) || 0;\n      addClass(popup, 'swal2-dragging');\n    }\n  };\n\n  /**\n   * @param {MouseEvent | TouchEvent} event\n   */\n  const move = event => {\n    const popup = getPopup();\n    if (dragging) {\n      let {\n        clientX,\n        clientY\n      } = getClientXY(event);\n      popup.style.insetInlineStart = `${initialX + (clientX - mousedownX)}px`;\n      popup.style.insetBlockStart = `${initialY + (clientY - mousedownY)}px`;\n    }\n  };\n  const up = () => {\n    const popup = getPopup();\n    dragging = false;\n    removeClass(popup, 'swal2-dragging');\n  };\n\n  /**\n   * @param {MouseEvent | TouchEvent} event\n   * @returns {{ clientX: number, clientY: number }}\n   */\n  const getClientXY = event => {\n    let clientX = 0,\n      clientY = 0;\n    if (event.type.startsWith('mouse')) {\n      clientX = /** @type {MouseEvent} */event.clientX;\n      clientY = /** @type {MouseEvent} */event.clientY;\n    } else if (event.type.startsWith('touch')) {\n      clientX = /** @type {TouchEvent} */event.touches[0].clientX;\n      clientY = /** @type {TouchEvent} */event.touches[0].clientY;\n    }\n    return {\n      clientX,\n      clientY\n    };\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderPopup = (instance, params) => {\n    const container = getContainer();\n    const popup = getPopup();\n    if (!container || !popup) {\n      return;\n    }\n\n    // Width\n    // https://github.com/sweetalert2/sweetalert2/issues/2170\n    if (params.toast) {\n      applyNumericalStyle(container, 'width', params.width);\n      popup.style.width = '100%';\n      const loader = getLoader();\n      if (loader) {\n        popup.insertBefore(loader, getIcon());\n      }\n    } else {\n      applyNumericalStyle(popup, 'width', params.width);\n    }\n\n    // Padding\n    applyNumericalStyle(popup, 'padding', params.padding);\n\n    // Color\n    if (params.color) {\n      popup.style.color = params.color;\n    }\n\n    // Background\n    if (params.background) {\n      popup.style.background = params.background;\n    }\n    hide(getValidationMessage());\n\n    // Classes\n    addClasses$1(popup, params);\n    if (params.draggable && !params.toast) {\n      addClass(popup, swalClasses.draggable);\n      addDraggableListeners(popup);\n    } else {\n      removeClass(popup, swalClasses.draggable);\n      removeDraggableListeners(popup);\n    }\n  };\n\n  /**\n   * @param {HTMLElement} popup\n   * @param {SweetAlertOptions} params\n   */\n  const addClasses$1 = (popup, params) => {\n    const showClass = params.showClass || {};\n    // Default Class + showClass when updating Swal.update({})\n    popup.className = `${swalClasses.popup} ${isVisible$1(popup) ? showClass.popup : ''}`;\n    if (params.toast) {\n      addClass([document.documentElement, document.body], swalClasses['toast-shown']);\n      addClass(popup, swalClasses.toast);\n    } else {\n      addClass(popup, swalClasses.modal);\n    }\n\n    // Custom class\n    applyCustomClass(popup, params, 'popup');\n    // TODO: remove in the next major\n    if (typeof params.customClass === 'string') {\n      addClass(popup, params.customClass);\n    }\n\n    // Icon class (#1842)\n    if (params.icon) {\n      addClass(popup, swalClasses[`icon-${params.icon}`]);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderProgressSteps = (instance, params) => {\n    const progressStepsContainer = getProgressSteps();\n    if (!progressStepsContainer) {\n      return;\n    }\n    const {\n      progressSteps,\n      currentProgressStep\n    } = params;\n    if (!progressSteps || progressSteps.length === 0 || currentProgressStep === undefined) {\n      hide(progressStepsContainer);\n      return;\n    }\n    show(progressStepsContainer);\n    progressStepsContainer.textContent = '';\n    if (currentProgressStep >= progressSteps.length) {\n      warn('Invalid currentProgressStep parameter, it should be less than progressSteps.length ' + '(currentProgressStep like JS arrays starts from 0)');\n    }\n    progressSteps.forEach((step, index) => {\n      const stepEl = createStepElement(step);\n      progressStepsContainer.appendChild(stepEl);\n      if (index === currentProgressStep) {\n        addClass(stepEl, swalClasses['active-progress-step']);\n      }\n      if (index !== progressSteps.length - 1) {\n        const lineEl = createLineElement(params);\n        progressStepsContainer.appendChild(lineEl);\n      }\n    });\n  };\n\n  /**\n   * @param {string} step\n   * @returns {HTMLLIElement}\n   */\n  const createStepElement = step => {\n    const stepEl = document.createElement('li');\n    addClass(stepEl, swalClasses['progress-step']);\n    setInnerHtml(stepEl, step);\n    return stepEl;\n  };\n\n  /**\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLLIElement}\n   */\n  const createLineElement = params => {\n    const lineEl = document.createElement('li');\n    addClass(lineEl, swalClasses['progress-step-line']);\n    if (params.progressStepsDistance) {\n      applyNumericalStyle(lineEl, 'width', params.progressStepsDistance);\n    }\n    return lineEl;\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderTitle = (instance, params) => {\n    const title = getTitle();\n    if (!title) {\n      return;\n    }\n    showWhenInnerHtmlPresent(title);\n    toggle(title, params.title || params.titleText, 'block');\n    if (params.title) {\n      parseHtmlToContainer(params.title, title);\n    }\n    if (params.titleText) {\n      title.innerText = params.titleText;\n    }\n\n    // Custom class\n    applyCustomClass(title, params, 'title');\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const render = (instance, params) => {\n    renderPopup(instance, params);\n    renderContainer(instance, params);\n    renderProgressSteps(instance, params);\n    renderIcon(instance, params);\n    renderImage(instance, params);\n    renderTitle(instance, params);\n    renderCloseButton(instance, params);\n    renderContent(instance, params);\n    renderActions(instance, params);\n    renderFooter(instance, params);\n    const popup = getPopup();\n    if (typeof params.didRender === 'function' && popup) {\n      params.didRender(popup);\n    }\n    globalState.eventEmitter.emit('didRender', popup);\n  };\n\n  /*\n   * Global function to determine if SweetAlert2 popup is shown\n   */\n  const isVisible = () => {\n    return isVisible$1(getPopup());\n  };\n\n  /*\n   * Global function to click 'Confirm' button\n   */\n  const clickConfirm = () => {\n    var _dom$getConfirmButton;\n    return (_dom$getConfirmButton = getConfirmButton()) === null || _dom$getConfirmButton === void 0 ? void 0 : _dom$getConfirmButton.click();\n  };\n\n  /*\n   * Global function to click 'Deny' button\n   */\n  const clickDeny = () => {\n    var _dom$getDenyButton;\n    return (_dom$getDenyButton = getDenyButton()) === null || _dom$getDenyButton === void 0 ? void 0 : _dom$getDenyButton.click();\n  };\n\n  /*\n   * Global function to click 'Cancel' button\n   */\n  const clickCancel = () => {\n    var _dom$getCancelButton;\n    return (_dom$getCancelButton = getCancelButton()) === null || _dom$getCancelButton === void 0 ? void 0 : _dom$getCancelButton.click();\n  };\n\n  /** @typedef {'cancel' | 'backdrop' | 'close' | 'esc' | 'timer'} DismissReason */\n\n  /** @type {Record<DismissReason, DismissReason>} */\n  const DismissReason = Object.freeze({\n    cancel: 'cancel',\n    backdrop: 'backdrop',\n    close: 'close',\n    esc: 'esc',\n    timer: 'timer'\n  });\n\n  /**\n   * @param {GlobalState} globalState\n   */\n  const removeKeydownHandler = globalState => {\n    if (globalState.keydownTarget && globalState.keydownHandlerAdded) {\n      globalState.keydownTarget.removeEventListener('keydown', globalState.keydownHandler, {\n        capture: globalState.keydownListenerCapture\n      });\n      globalState.keydownHandlerAdded = false;\n    }\n  };\n\n  /**\n   * @param {GlobalState} globalState\n   * @param {SweetAlertOptions} innerParams\n   * @param {*} dismissWith\n   */\n  const addKeydownHandler = (globalState, innerParams, dismissWith) => {\n    removeKeydownHandler(globalState);\n    if (!innerParams.toast) {\n      globalState.keydownHandler = e => keydownHandler(innerParams, e, dismissWith);\n      globalState.keydownTarget = innerParams.keydownListenerCapture ? window : getPopup();\n      globalState.keydownListenerCapture = innerParams.keydownListenerCapture;\n      globalState.keydownTarget.addEventListener('keydown', globalState.keydownHandler, {\n        capture: globalState.keydownListenerCapture\n      });\n      globalState.keydownHandlerAdded = true;\n    }\n  };\n\n  /**\n   * @param {number} index\n   * @param {number} increment\n   */\n  const setFocus = (index, increment) => {\n    var _dom$getPopup;\n    const focusableElements = getFocusableElements();\n    // search for visible elements and select the next possible match\n    if (focusableElements.length) {\n      index = index + increment;\n\n      // shift + tab when .swal2-popup is focused\n      if (index === -2) {\n        index = focusableElements.length - 1;\n      }\n\n      // rollover to first item\n      if (index === focusableElements.length) {\n        index = 0;\n\n        // go to last item\n      } else if (index === -1) {\n        index = focusableElements.length - 1;\n      }\n      focusableElements[index].focus();\n      return;\n    }\n    // no visible focusable elements, focus the popup\n    (_dom$getPopup = getPopup()) === null || _dom$getPopup === void 0 || _dom$getPopup.focus();\n  };\n  const arrowKeysNextButton = ['ArrowRight', 'ArrowDown'];\n  const arrowKeysPreviousButton = ['ArrowLeft', 'ArrowUp'];\n\n  /**\n   * @param {SweetAlertOptions} innerParams\n   * @param {KeyboardEvent} event\n   * @param {Function} dismissWith\n   */\n  const keydownHandler = (innerParams, event, dismissWith) => {\n    if (!innerParams) {\n      return; // This instance has already been destroyed\n    }\n\n    // Ignore keydown during IME composition\n    // https://developer.mozilla.org/en-US/docs/Web/API/Document/keydown_event#ignoring_keydown_during_ime_composition\n    // https://github.com/sweetalert2/sweetalert2/issues/720\n    // https://github.com/sweetalert2/sweetalert2/issues/2406\n    if (event.isComposing || event.keyCode === 229) {\n      return;\n    }\n    if (innerParams.stopKeydownPropagation) {\n      event.stopPropagation();\n    }\n\n    // ENTER\n    if (event.key === 'Enter') {\n      handleEnter(event, innerParams);\n    }\n\n    // TAB\n    else if (event.key === 'Tab') {\n      handleTab(event);\n    }\n\n    // ARROWS - switch focus between buttons\n    else if ([...arrowKeysNextButton, ...arrowKeysPreviousButton].includes(event.key)) {\n      handleArrows(event.key);\n    }\n\n    // ESC\n    else if (event.key === 'Escape') {\n      handleEsc(event, innerParams, dismissWith);\n    }\n  };\n\n  /**\n   * @param {KeyboardEvent} event\n   * @param {SweetAlertOptions} innerParams\n   */\n  const handleEnter = (event, innerParams) => {\n    // https://github.com/sweetalert2/sweetalert2/issues/2386\n    if (!callIfFunction(innerParams.allowEnterKey)) {\n      return;\n    }\n    const input = getInput$1(getPopup(), innerParams.input);\n    if (event.target && input && event.target instanceof HTMLElement && event.target.outerHTML === input.outerHTML) {\n      if (['textarea', 'file'].includes(innerParams.input)) {\n        return; // do not submit\n      }\n      clickConfirm();\n      event.preventDefault();\n    }\n  };\n\n  /**\n   * @param {KeyboardEvent} event\n   */\n  const handleTab = event => {\n    const targetElement = event.target;\n    const focusableElements = getFocusableElements();\n    let btnIndex = -1;\n    for (let i = 0; i < focusableElements.length; i++) {\n      if (targetElement === focusableElements[i]) {\n        btnIndex = i;\n        break;\n      }\n    }\n\n    // Cycle to the next button\n    if (!event.shiftKey) {\n      setFocus(btnIndex, 1);\n    }\n\n    // Cycle to the prev button\n    else {\n      setFocus(btnIndex, -1);\n    }\n    event.stopPropagation();\n    event.preventDefault();\n  };\n\n  /**\n   * @param {string} key\n   */\n  const handleArrows = key => {\n    const actions = getActions();\n    const confirmButton = getConfirmButton();\n    const denyButton = getDenyButton();\n    const cancelButton = getCancelButton();\n    if (!actions || !confirmButton || !denyButton || !cancelButton) {\n      return;\n    }\n    /** @type HTMLElement[] */\n    const buttons = [confirmButton, denyButton, cancelButton];\n    if (document.activeElement instanceof HTMLElement && !buttons.includes(document.activeElement)) {\n      return;\n    }\n    const sibling = arrowKeysNextButton.includes(key) ? 'nextElementSibling' : 'previousElementSibling';\n    let buttonToFocus = document.activeElement;\n    if (!buttonToFocus) {\n      return;\n    }\n    for (let i = 0; i < actions.children.length; i++) {\n      buttonToFocus = buttonToFocus[sibling];\n      if (!buttonToFocus) {\n        return;\n      }\n      if (buttonToFocus instanceof HTMLButtonElement && isVisible$1(buttonToFocus)) {\n        break;\n      }\n    }\n    if (buttonToFocus instanceof HTMLButtonElement) {\n      buttonToFocus.focus();\n    }\n  };\n\n  /**\n   * @param {KeyboardEvent} event\n   * @param {SweetAlertOptions} innerParams\n   * @param {Function} dismissWith\n   */\n  const handleEsc = (event, innerParams, dismissWith) => {\n    if (callIfFunction(innerParams.allowEscapeKey)) {\n      event.preventDefault();\n      dismissWith(DismissReason.esc);\n    }\n  };\n\n  /**\n   * This module contains `WeakMap`s for each effectively-\"private  property\" that a `Swal` has.\n   * For example, to set the private property \"foo\" of `this` to \"bar\", you can `privateProps.foo.set(this, 'bar')`\n   * This is the approach that Babel will probably take to implement private methods/fields\n   *   https://github.com/tc39/proposal-private-methods\n   *   https://github.com/babel/babel/pull/7555\n   * Once we have the changes from that PR in Babel, and our core class fits reasonable in *one module*\n   *   then we can use that language feature.\n   */\n\n  var privateMethods = {\n    swalPromiseResolve: new WeakMap(),\n    swalPromiseReject: new WeakMap()\n  };\n\n  // From https://developer.paciellogroup.com/blog/2018/06/the-current-state-of-modal-dialog-accessibility/\n  // Adding aria-hidden=\"true\" to elements outside of the active modal dialog ensures that\n  // elements not within the active modal dialog will not be surfaced if a user opens a screen\n  // reader’s list of elements (headings, form controls, landmarks, etc.) in the document.\n\n  const setAriaHidden = () => {\n    const container = getContainer();\n    const bodyChildren = Array.from(document.body.children);\n    bodyChildren.forEach(el => {\n      if (el.contains(container)) {\n        return;\n      }\n      if (el.hasAttribute('aria-hidden')) {\n        el.setAttribute('data-previous-aria-hidden', el.getAttribute('aria-hidden') || '');\n      }\n      el.setAttribute('aria-hidden', 'true');\n    });\n  };\n  const unsetAriaHidden = () => {\n    const bodyChildren = Array.from(document.body.children);\n    bodyChildren.forEach(el => {\n      if (el.hasAttribute('data-previous-aria-hidden')) {\n        el.setAttribute('aria-hidden', el.getAttribute('data-previous-aria-hidden') || '');\n        el.removeAttribute('data-previous-aria-hidden');\n      } else {\n        el.removeAttribute('aria-hidden');\n      }\n    });\n  };\n\n  // @ts-ignore\n  const isSafariOrIOS = typeof window !== 'undefined' && !!window.GestureEvent; // true for Safari desktop + all iOS browsers https://stackoverflow.com/a/70585394\n\n  /**\n   * Fix iOS scrolling\n   * http://stackoverflow.com/q/39626302\n   */\n  const iOSfix = () => {\n    if (isSafariOrIOS && !hasClass(document.body, swalClasses.iosfix)) {\n      const offset = document.body.scrollTop;\n      document.body.style.top = `${offset * -1}px`;\n      addClass(document.body, swalClasses.iosfix);\n      lockBodyScroll();\n    }\n  };\n\n  /**\n   * https://github.com/sweetalert2/sweetalert2/issues/1246\n   */\n  const lockBodyScroll = () => {\n    const container = getContainer();\n    if (!container) {\n      return;\n    }\n    /** @type {boolean} */\n    let preventTouchMove;\n    /**\n     * @param {TouchEvent} event\n     */\n    container.ontouchstart = event => {\n      preventTouchMove = shouldPreventTouchMove(event);\n    };\n    /**\n     * @param {TouchEvent} event\n     */\n    container.ontouchmove = event => {\n      if (preventTouchMove) {\n        event.preventDefault();\n        event.stopPropagation();\n      }\n    };\n  };\n\n  /**\n   * @param {TouchEvent} event\n   * @returns {boolean}\n   */\n  const shouldPreventTouchMove = event => {\n    const target = event.target;\n    const container = getContainer();\n    const htmlContainer = getHtmlContainer();\n    if (!container || !htmlContainer) {\n      return false;\n    }\n    if (isStylus(event) || isZoom(event)) {\n      return false;\n    }\n    if (target === container) {\n      return true;\n    }\n    if (!isScrollable(container) && target instanceof HTMLElement && !selfOrParentIsScrollable(target, htmlContainer) &&\n    // #2823\n    target.tagName !== 'INPUT' &&\n    // #1603\n    target.tagName !== 'TEXTAREA' &&\n    // #2266\n    !(isScrollable(htmlContainer) &&\n    // #1944\n    htmlContainer.contains(target))) {\n      return true;\n    }\n    return false;\n  };\n\n  /**\n   * https://github.com/sweetalert2/sweetalert2/issues/1786\n   *\n   * @param {*} event\n   * @returns {boolean}\n   */\n  const isStylus = event => {\n    return event.touches && event.touches.length && event.touches[0].touchType === 'stylus';\n  };\n\n  /**\n   * https://github.com/sweetalert2/sweetalert2/issues/1891\n   *\n   * @param {TouchEvent} event\n   * @returns {boolean}\n   */\n  const isZoom = event => {\n    return event.touches && event.touches.length > 1;\n  };\n  const undoIOSfix = () => {\n    if (hasClass(document.body, swalClasses.iosfix)) {\n      const offset = parseInt(document.body.style.top, 10);\n      removeClass(document.body, swalClasses.iosfix);\n      document.body.style.top = '';\n      document.body.scrollTop = offset * -1;\n    }\n  };\n\n  /**\n   * Measure scrollbar width for padding body during modal show/hide\n   * https://github.com/twbs/bootstrap/blob/master/js/src/modal.js\n   *\n   * @returns {number}\n   */\n  const measureScrollbar = () => {\n    const scrollDiv = document.createElement('div');\n    scrollDiv.className = swalClasses['scrollbar-measure'];\n    document.body.appendChild(scrollDiv);\n    const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth;\n    document.body.removeChild(scrollDiv);\n    return scrollbarWidth;\n  };\n\n  /**\n   * Remember state in cases where opening and handling a modal will fiddle with it.\n   * @type {number | null}\n   */\n  let previousBodyPadding = null;\n\n  /**\n   * @param {string} initialBodyOverflow\n   */\n  const replaceScrollbarWithPadding = initialBodyOverflow => {\n    // for queues, do not do this more than once\n    if (previousBodyPadding !== null) {\n      return;\n    }\n    // if the body has overflow\n    if (document.body.scrollHeight > window.innerHeight || initialBodyOverflow === 'scroll' // https://github.com/sweetalert2/sweetalert2/issues/2663\n    ) {\n      // add padding so the content doesn't shift after removal of scrollbar\n      previousBodyPadding = parseInt(window.getComputedStyle(document.body).getPropertyValue('padding-right'));\n      document.body.style.paddingRight = `${previousBodyPadding + measureScrollbar()}px`;\n    }\n  };\n  const undoReplaceScrollbarWithPadding = () => {\n    if (previousBodyPadding !== null) {\n      document.body.style.paddingRight = `${previousBodyPadding}px`;\n      previousBodyPadding = null;\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {HTMLElement} container\n   * @param {boolean} returnFocus\n   * @param {Function} didClose\n   */\n  function removePopupAndResetState(instance, container, returnFocus, didClose) {\n    if (isToast()) {\n      triggerDidCloseAndDispose(instance, didClose);\n    } else {\n      restoreActiveElement(returnFocus).then(() => triggerDidCloseAndDispose(instance, didClose));\n      removeKeydownHandler(globalState);\n    }\n\n    // workaround for https://github.com/sweetalert2/sweetalert2/issues/2088\n    // for some reason removing the container in Safari will scroll the document to bottom\n    if (isSafariOrIOS) {\n      container.setAttribute('style', 'display:none !important');\n      container.removeAttribute('class');\n      container.innerHTML = '';\n    } else {\n      container.remove();\n    }\n    if (isModal()) {\n      undoReplaceScrollbarWithPadding();\n      undoIOSfix();\n      unsetAriaHidden();\n    }\n    removeBodyClasses();\n  }\n\n  /**\n   * Remove SweetAlert2 classes from body\n   */\n  function removeBodyClasses() {\n    removeClass([document.documentElement, document.body], [swalClasses.shown, swalClasses['height-auto'], swalClasses['no-backdrop'], swalClasses['toast-shown']]);\n  }\n\n  /**\n   * Instance method to close sweetAlert\n   *\n   * @param {any} resolveValue\n   */\n  function close(resolveValue) {\n    resolveValue = prepareResolveValue(resolveValue);\n    const swalPromiseResolve = privateMethods.swalPromiseResolve.get(this);\n    const didClose = triggerClosePopup(this);\n    if (this.isAwaitingPromise) {\n      // A swal awaiting for a promise (after a click on Confirm or Deny) cannot be dismissed anymore #2335\n      if (!resolveValue.isDismissed) {\n        handleAwaitingPromise(this);\n        swalPromiseResolve(resolveValue);\n      }\n    } else if (didClose) {\n      // Resolve Swal promise\n      swalPromiseResolve(resolveValue);\n    }\n  }\n  const triggerClosePopup = instance => {\n    const popup = getPopup();\n    if (!popup) {\n      return false;\n    }\n    const innerParams = privateProps.innerParams.get(instance);\n    if (!innerParams || hasClass(popup, innerParams.hideClass.popup)) {\n      return false;\n    }\n    removeClass(popup, innerParams.showClass.popup);\n    addClass(popup, innerParams.hideClass.popup);\n    const backdrop = getContainer();\n    removeClass(backdrop, innerParams.showClass.backdrop);\n    addClass(backdrop, innerParams.hideClass.backdrop);\n    handlePopupAnimation(instance, popup, innerParams);\n    return true;\n  };\n\n  /**\n   * @param {any} error\n   */\n  function rejectPromise(error) {\n    const rejectPromise = privateMethods.swalPromiseReject.get(this);\n    handleAwaitingPromise(this);\n    if (rejectPromise) {\n      // Reject Swal promise\n      rejectPromise(error);\n    }\n  }\n\n  /**\n   * @param {SweetAlert} instance\n   */\n  const handleAwaitingPromise = instance => {\n    if (instance.isAwaitingPromise) {\n      delete instance.isAwaitingPromise;\n      // The instance might have been previously partly destroyed, we must resume the destroy process in this case #2335\n      if (!privateProps.innerParams.get(instance)) {\n        instance._destroy();\n      }\n    }\n  };\n\n  /**\n   * @param {any} resolveValue\n   * @returns {SweetAlertResult}\n   */\n  const prepareResolveValue = resolveValue => {\n    // When user calls Swal.close()\n    if (typeof resolveValue === 'undefined') {\n      return {\n        isConfirmed: false,\n        isDenied: false,\n        isDismissed: true\n      };\n    }\n    return Object.assign({\n      isConfirmed: false,\n      isDenied: false,\n      isDismissed: false\n    }, resolveValue);\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {HTMLElement} popup\n   * @param {SweetAlertOptions} innerParams\n   */\n  const handlePopupAnimation = (instance, popup, innerParams) => {\n    var _globalState$eventEmi;\n    const container = getContainer();\n    // If animation is supported, animate\n    const animationIsSupported = hasCssAnimation(popup);\n    if (typeof innerParams.willClose === 'function') {\n      innerParams.willClose(popup);\n    }\n    (_globalState$eventEmi = globalState.eventEmitter) === null || _globalState$eventEmi === void 0 || _globalState$eventEmi.emit('willClose', popup);\n    if (animationIsSupported) {\n      animatePopup(instance, popup, container, innerParams.returnFocus, innerParams.didClose);\n    } else {\n      // Otherwise, remove immediately\n      removePopupAndResetState(instance, container, innerParams.returnFocus, innerParams.didClose);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {HTMLElement} popup\n   * @param {HTMLElement} container\n   * @param {boolean} returnFocus\n   * @param {Function} didClose\n   */\n  const animatePopup = (instance, popup, container, returnFocus, didClose) => {\n    globalState.swalCloseEventFinishedCallback = removePopupAndResetState.bind(null, instance, container, returnFocus, didClose);\n    /**\n     * @param {AnimationEvent | TransitionEvent} e\n     */\n    const swalCloseAnimationFinished = function (e) {\n      if (e.target === popup) {\n        var _globalState$swalClos;\n        (_globalState$swalClos = globalState.swalCloseEventFinishedCallback) === null || _globalState$swalClos === void 0 || _globalState$swalClos.call(globalState);\n        delete globalState.swalCloseEventFinishedCallback;\n        popup.removeEventListener('animationend', swalCloseAnimationFinished);\n        popup.removeEventListener('transitionend', swalCloseAnimationFinished);\n      }\n    };\n    popup.addEventListener('animationend', swalCloseAnimationFinished);\n    popup.addEventListener('transitionend', swalCloseAnimationFinished);\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {Function} didClose\n   */\n  const triggerDidCloseAndDispose = (instance, didClose) => {\n    setTimeout(() => {\n      var _globalState$eventEmi2;\n      if (typeof didClose === 'function') {\n        didClose.bind(instance.params)();\n      }\n      (_globalState$eventEmi2 = globalState.eventEmitter) === null || _globalState$eventEmi2 === void 0 || _globalState$eventEmi2.emit('didClose');\n      // instance might have been destroyed already\n      if (instance._destroy) {\n        instance._destroy();\n      }\n    });\n  };\n\n  /**\n   * Shows loader (spinner), this is useful with AJAX requests.\n   * By default the loader be shown instead of the \"Confirm\" button.\n   *\n   * @param {HTMLButtonElement | null} [buttonToReplace]\n   */\n  const showLoading = buttonToReplace => {\n    let popup = getPopup();\n    if (!popup) {\n      new Swal();\n    }\n    popup = getPopup();\n    if (!popup) {\n      return;\n    }\n    const loader = getLoader();\n    if (isToast()) {\n      hide(getIcon());\n    } else {\n      replaceButton(popup, buttonToReplace);\n    }\n    show(loader);\n    popup.setAttribute('data-loading', 'true');\n    popup.setAttribute('aria-busy', 'true');\n    popup.focus();\n  };\n\n  /**\n   * @param {HTMLElement} popup\n   * @param {HTMLButtonElement | null} [buttonToReplace]\n   */\n  const replaceButton = (popup, buttonToReplace) => {\n    const actions = getActions();\n    const loader = getLoader();\n    if (!actions || !loader) {\n      return;\n    }\n    if (!buttonToReplace && isVisible$1(getConfirmButton())) {\n      buttonToReplace = getConfirmButton();\n    }\n    show(actions);\n    if (buttonToReplace) {\n      hide(buttonToReplace);\n      loader.setAttribute('data-button-to-replace', buttonToReplace.className);\n      actions.insertBefore(loader, buttonToReplace);\n    }\n    addClass([popup, actions], swalClasses.loading);\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const handleInputOptionsAndValue = (instance, params) => {\n    if (params.input === 'select' || params.input === 'radio') {\n      handleInputOptions(instance, params);\n    } else if (['text', 'email', 'number', 'tel', 'textarea'].some(i => i === params.input) && (hasToPromiseFn(params.inputValue) || isPromise(params.inputValue))) {\n      showLoading(getConfirmButton());\n      handleInputValue(instance, params);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} innerParams\n   * @returns {SweetAlertInputValue}\n   */\n  const getInputValue = (instance, innerParams) => {\n    const input = instance.getInput();\n    if (!input) {\n      return null;\n    }\n    switch (innerParams.input) {\n      case 'checkbox':\n        return getCheckboxValue(input);\n      case 'radio':\n        return getRadioValue(input);\n      case 'file':\n        return getFileValue(input);\n      default:\n        return innerParams.inputAutoTrim ? input.value.trim() : input.value;\n    }\n  };\n\n  /**\n   * @param {HTMLInputElement} input\n   * @returns {number}\n   */\n  const getCheckboxValue = input => input.checked ? 1 : 0;\n\n  /**\n   * @param {HTMLInputElement} input\n   * @returns {string | null}\n   */\n  const getRadioValue = input => input.checked ? input.value : null;\n\n  /**\n   * @param {HTMLInputElement} input\n   * @returns {FileList | File | null}\n   */\n  const getFileValue = input => input.files && input.files.length ? input.getAttribute('multiple') !== null ? input.files : input.files[0] : null;\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const handleInputOptions = (instance, params) => {\n    const popup = getPopup();\n    if (!popup) {\n      return;\n    }\n    /**\n     * @param {Record<string, any>} inputOptions\n     */\n    const processInputOptions = inputOptions => {\n      if (params.input === 'select') {\n        populateSelectOptions(popup, formatInputOptions(inputOptions), params);\n      } else if (params.input === 'radio') {\n        populateRadioOptions(popup, formatInputOptions(inputOptions), params);\n      }\n    };\n    if (hasToPromiseFn(params.inputOptions) || isPromise(params.inputOptions)) {\n      showLoading(getConfirmButton());\n      asPromise(params.inputOptions).then(inputOptions => {\n        instance.hideLoading();\n        processInputOptions(inputOptions);\n      });\n    } else if (typeof params.inputOptions === 'object') {\n      processInputOptions(params.inputOptions);\n    } else {\n      error(`Unexpected type of inputOptions! Expected object, Map or Promise, got ${typeof params.inputOptions}`);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const handleInputValue = (instance, params) => {\n    const input = instance.getInput();\n    if (!input) {\n      return;\n    }\n    hide(input);\n    asPromise(params.inputValue).then(inputValue => {\n      input.value = params.input === 'number' ? `${parseFloat(inputValue) || 0}` : `${inputValue}`;\n      show(input);\n      input.focus();\n      instance.hideLoading();\n    }).catch(err => {\n      error(`Error in inputValue promise: ${err}`);\n      input.value = '';\n      show(input);\n      input.focus();\n      instance.hideLoading();\n    });\n  };\n\n  /**\n   * @param {HTMLElement} popup\n   * @param {InputOptionFlattened[]} inputOptions\n   * @param {SweetAlertOptions} params\n   */\n  function populateSelectOptions(popup, inputOptions, params) {\n    const select = getDirectChildByClass(popup, swalClasses.select);\n    if (!select) {\n      return;\n    }\n    /**\n     * @param {HTMLElement} parent\n     * @param {string} optionLabel\n     * @param {string} optionValue\n     */\n    const renderOption = (parent, optionLabel, optionValue) => {\n      const option = document.createElement('option');\n      option.value = optionValue;\n      setInnerHtml(option, optionLabel);\n      option.selected = isSelected(optionValue, params.inputValue);\n      parent.appendChild(option);\n    };\n    inputOptions.forEach(inputOption => {\n      const optionValue = inputOption[0];\n      const optionLabel = inputOption[1];\n      // <optgroup> spec:\n      // https://www.w3.org/TR/html401/interact/forms.html#h-17.6\n      // \"...all OPTGROUP elements must be specified directly within a SELECT element (i.e., groups may not be nested)...\"\n      // check whether this is a <optgroup>\n      if (Array.isArray(optionLabel)) {\n        // if it is an array, then it is an <optgroup>\n        const optgroup = document.createElement('optgroup');\n        optgroup.label = optionValue;\n        optgroup.disabled = false; // not configurable for now\n        select.appendChild(optgroup);\n        optionLabel.forEach(o => renderOption(optgroup, o[1], o[0]));\n      } else {\n        // case of <option>\n        renderOption(select, optionLabel, optionValue);\n      }\n    });\n    select.focus();\n  }\n\n  /**\n   * @param {HTMLElement} popup\n   * @param {InputOptionFlattened[]} inputOptions\n   * @param {SweetAlertOptions} params\n   */\n  function populateRadioOptions(popup, inputOptions, params) {\n    const radio = getDirectChildByClass(popup, swalClasses.radio);\n    if (!radio) {\n      return;\n    }\n    inputOptions.forEach(inputOption => {\n      const radioValue = inputOption[0];\n      const radioLabel = inputOption[1];\n      const radioInput = document.createElement('input');\n      const radioLabelElement = document.createElement('label');\n      radioInput.type = 'radio';\n      radioInput.name = swalClasses.radio;\n      radioInput.value = radioValue;\n      if (isSelected(radioValue, params.inputValue)) {\n        radioInput.checked = true;\n      }\n      const label = document.createElement('span');\n      setInnerHtml(label, radioLabel);\n      label.className = swalClasses.label;\n      radioLabelElement.appendChild(radioInput);\n      radioLabelElement.appendChild(label);\n      radio.appendChild(radioLabelElement);\n    });\n    const radios = radio.querySelectorAll('input');\n    if (radios.length) {\n      radios[0].focus();\n    }\n  }\n\n  /**\n   * Converts `inputOptions` into an array of `[value, label]`s\n   *\n   * @param {Record<string, any>} inputOptions\n   * @typedef {string[]} InputOptionFlattened\n   * @returns {InputOptionFlattened[]}\n   */\n  const formatInputOptions = inputOptions => {\n    /** @type {InputOptionFlattened[]} */\n    const result = [];\n    if (inputOptions instanceof Map) {\n      inputOptions.forEach((value, key) => {\n        let valueFormatted = value;\n        if (typeof valueFormatted === 'object') {\n          // case of <optgroup>\n          valueFormatted = formatInputOptions(valueFormatted);\n        }\n        result.push([key, valueFormatted]);\n      });\n    } else {\n      Object.keys(inputOptions).forEach(key => {\n        let valueFormatted = inputOptions[key];\n        if (typeof valueFormatted === 'object') {\n          // case of <optgroup>\n          valueFormatted = formatInputOptions(valueFormatted);\n        }\n        result.push([key, valueFormatted]);\n      });\n    }\n    return result;\n  };\n\n  /**\n   * @param {string} optionValue\n   * @param {SweetAlertInputValue} inputValue\n   * @returns {boolean}\n   */\n  const isSelected = (optionValue, inputValue) => {\n    return !!inputValue && inputValue.toString() === optionValue.toString();\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   */\n  const handleConfirmButtonClick = instance => {\n    const innerParams = privateProps.innerParams.get(instance);\n    instance.disableButtons();\n    if (innerParams.input) {\n      handleConfirmOrDenyWithInput(instance, 'confirm');\n    } else {\n      confirm(instance, true);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   */\n  const handleDenyButtonClick = instance => {\n    const innerParams = privateProps.innerParams.get(instance);\n    instance.disableButtons();\n    if (innerParams.returnInputValueOnDeny) {\n      handleConfirmOrDenyWithInput(instance, 'deny');\n    } else {\n      deny(instance, false);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {Function} dismissWith\n   */\n  const handleCancelButtonClick = (instance, dismissWith) => {\n    instance.disableButtons();\n    dismissWith(DismissReason.cancel);\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {'confirm' | 'deny'} type\n   */\n  const handleConfirmOrDenyWithInput = (instance, type) => {\n    const innerParams = privateProps.innerParams.get(instance);\n    if (!innerParams.input) {\n      error(`The \"input\" parameter is needed to be set when using returnInputValueOn${capitalizeFirstLetter(type)}`);\n      return;\n    }\n    const input = instance.getInput();\n    const inputValue = getInputValue(instance, innerParams);\n    if (innerParams.inputValidator) {\n      handleInputValidator(instance, inputValue, type);\n    } else if (input && !input.checkValidity()) {\n      instance.enableButtons();\n      instance.showValidationMessage(innerParams.validationMessage || input.validationMessage);\n    } else if (type === 'deny') {\n      deny(instance, inputValue);\n    } else {\n      confirm(instance, inputValue);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertInputValue} inputValue\n   * @param {'confirm' | 'deny'} type\n   */\n  const handleInputValidator = (instance, inputValue, type) => {\n    const innerParams = privateProps.innerParams.get(instance);\n    instance.disableInput();\n    const validationPromise = Promise.resolve().then(() => asPromise(innerParams.inputValidator(inputValue, innerParams.validationMessage)));\n    validationPromise.then(validationMessage => {\n      instance.enableButtons();\n      instance.enableInput();\n      if (validationMessage) {\n        instance.showValidationMessage(validationMessage);\n      } else if (type === 'deny') {\n        deny(instance, inputValue);\n      } else {\n        confirm(instance, inputValue);\n      }\n    });\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {any} value\n   */\n  const deny = (instance, value) => {\n    const innerParams = privateProps.innerParams.get(instance || undefined);\n    if (innerParams.showLoaderOnDeny) {\n      showLoading(getDenyButton());\n    }\n    if (innerParams.preDeny) {\n      instance.isAwaitingPromise = true; // Flagging the instance as awaiting a promise so it's own promise's reject/resolve methods doesn't get destroyed until the result from this preDeny's promise is received\n      const preDenyPromise = Promise.resolve().then(() => asPromise(innerParams.preDeny(value, innerParams.validationMessage)));\n      preDenyPromise.then(preDenyValue => {\n        if (preDenyValue === false) {\n          instance.hideLoading();\n          handleAwaitingPromise(instance);\n        } else {\n          instance.close({\n            isDenied: true,\n            value: typeof preDenyValue === 'undefined' ? value : preDenyValue\n          });\n        }\n      }).catch(error => rejectWith(instance || undefined, error));\n    } else {\n      instance.close({\n        isDenied: true,\n        value\n      });\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {any} value\n   */\n  const succeedWith = (instance, value) => {\n    instance.close({\n      isConfirmed: true,\n      value\n    });\n  };\n\n  /**\n   *\n   * @param {SweetAlert} instance\n   * @param {string} error\n   */\n  const rejectWith = (instance, error) => {\n    instance.rejectPromise(error);\n  };\n\n  /**\n   *\n   * @param {SweetAlert} instance\n   * @param {any} value\n   */\n  const confirm = (instance, value) => {\n    const innerParams = privateProps.innerParams.get(instance || undefined);\n    if (innerParams.showLoaderOnConfirm) {\n      showLoading();\n    }\n    if (innerParams.preConfirm) {\n      instance.resetValidationMessage();\n      instance.isAwaitingPromise = true; // Flagging the instance as awaiting a promise so it's own promise's reject/resolve methods doesn't get destroyed until the result from this preConfirm's promise is received\n      const preConfirmPromise = Promise.resolve().then(() => asPromise(innerParams.preConfirm(value, innerParams.validationMessage)));\n      preConfirmPromise.then(preConfirmValue => {\n        if (isVisible$1(getValidationMessage()) || preConfirmValue === false) {\n          instance.hideLoading();\n          handleAwaitingPromise(instance);\n        } else {\n          succeedWith(instance, typeof preConfirmValue === 'undefined' ? value : preConfirmValue);\n        }\n      }).catch(error => rejectWith(instance || undefined, error));\n    } else {\n      succeedWith(instance, value);\n    }\n  };\n\n  /**\n   * Hides loader and shows back the button which was hidden by .showLoading()\n   */\n  function hideLoading() {\n    // do nothing if popup is closed\n    const innerParams = privateProps.innerParams.get(this);\n    if (!innerParams) {\n      return;\n    }\n    const domCache = privateProps.domCache.get(this);\n    hide(domCache.loader);\n    if (isToast()) {\n      if (innerParams.icon) {\n        show(getIcon());\n      }\n    } else {\n      showRelatedButton(domCache);\n    }\n    removeClass([domCache.popup, domCache.actions], swalClasses.loading);\n    domCache.popup.removeAttribute('aria-busy');\n    domCache.popup.removeAttribute('data-loading');\n    domCache.confirmButton.disabled = false;\n    domCache.denyButton.disabled = false;\n    domCache.cancelButton.disabled = false;\n  }\n  const showRelatedButton = domCache => {\n    const buttonToReplace = domCache.popup.getElementsByClassName(domCache.loader.getAttribute('data-button-to-replace'));\n    if (buttonToReplace.length) {\n      show(buttonToReplace[0], 'inline-block');\n    } else if (allButtonsAreHidden()) {\n      hide(domCache.actions);\n    }\n  };\n\n  /**\n   * Gets the input DOM node, this method works with input parameter.\n   *\n   * @returns {HTMLInputElement | null}\n   */\n  function getInput() {\n    const innerParams = privateProps.innerParams.get(this);\n    const domCache = privateProps.domCache.get(this);\n    if (!domCache) {\n      return null;\n    }\n    return getInput$1(domCache.popup, innerParams.input);\n  }\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {string[]} buttons\n   * @param {boolean} disabled\n   */\n  function setButtonsDisabled(instance, buttons, disabled) {\n    const domCache = privateProps.domCache.get(instance);\n    buttons.forEach(button => {\n      domCache[button].disabled = disabled;\n    });\n  }\n\n  /**\n   * @param {HTMLInputElement | null} input\n   * @param {boolean} disabled\n   */\n  function setInputDisabled(input, disabled) {\n    const popup = getPopup();\n    if (!popup || !input) {\n      return;\n    }\n    if (input.type === 'radio') {\n      /** @type {NodeListOf<HTMLInputElement>} */\n      const radios = popup.querySelectorAll(`[name=\"${swalClasses.radio}\"]`);\n      for (let i = 0; i < radios.length; i++) {\n        radios[i].disabled = disabled;\n      }\n    } else {\n      input.disabled = disabled;\n    }\n  }\n\n  /**\n   * Enable all the buttons\n   * @this {SweetAlert}\n   */\n  function enableButtons() {\n    setButtonsDisabled(this, ['confirmButton', 'denyButton', 'cancelButton'], false);\n  }\n\n  /**\n   * Disable all the buttons\n   * @this {SweetAlert}\n   */\n  function disableButtons() {\n    setButtonsDisabled(this, ['confirmButton', 'denyButton', 'cancelButton'], true);\n  }\n\n  /**\n   * Enable the input field\n   * @this {SweetAlert}\n   */\n  function enableInput() {\n    setInputDisabled(this.getInput(), false);\n  }\n\n  /**\n   * Disable the input field\n   * @this {SweetAlert}\n   */\n  function disableInput() {\n    setInputDisabled(this.getInput(), true);\n  }\n\n  /**\n   * Show block with validation message\n   *\n   * @param {string} error\n   * @this {SweetAlert}\n   */\n  function showValidationMessage(error) {\n    const domCache = privateProps.domCache.get(this);\n    const params = privateProps.innerParams.get(this);\n    setInnerHtml(domCache.validationMessage, error);\n    domCache.validationMessage.className = swalClasses['validation-message'];\n    if (params.customClass && params.customClass.validationMessage) {\n      addClass(domCache.validationMessage, params.customClass.validationMessage);\n    }\n    show(domCache.validationMessage);\n    const input = this.getInput();\n    if (input) {\n      input.setAttribute('aria-invalid', 'true');\n      input.setAttribute('aria-describedby', swalClasses['validation-message']);\n      focusInput(input);\n      addClass(input, swalClasses.inputerror);\n    }\n  }\n\n  /**\n   * Hide block with validation message\n   *\n   * @this {SweetAlert}\n   */\n  function resetValidationMessage() {\n    const domCache = privateProps.domCache.get(this);\n    if (domCache.validationMessage) {\n      hide(domCache.validationMessage);\n    }\n    const input = this.getInput();\n    if (input) {\n      input.removeAttribute('aria-invalid');\n      input.removeAttribute('aria-describedby');\n      removeClass(input, swalClasses.inputerror);\n    }\n  }\n  const defaultParams = {\n    title: '',\n    titleText: '',\n    text: '',\n    html: '',\n    footer: '',\n    icon: undefined,\n    iconColor: undefined,\n    iconHtml: undefined,\n    template: undefined,\n    toast: false,\n    draggable: false,\n    animation: true,\n    theme: 'light',\n    showClass: {\n      popup: 'swal2-show',\n      backdrop: 'swal2-backdrop-show',\n      icon: 'swal2-icon-show'\n    },\n    hideClass: {\n      popup: 'swal2-hide',\n      backdrop: 'swal2-backdrop-hide',\n      icon: 'swal2-icon-hide'\n    },\n    customClass: {},\n    target: 'body',\n    color: undefined,\n    backdrop: true,\n    heightAuto: true,\n    allowOutsideClick: true,\n    allowEscapeKey: true,\n    allowEnterKey: true,\n    stopKeydownPropagation: true,\n    keydownListenerCapture: false,\n    showConfirmButton: true,\n    showDenyButton: false,\n    showCancelButton: false,\n    preConfirm: undefined,\n    preDeny: undefined,\n    confirmButtonText: 'OK',\n    confirmButtonAriaLabel: '',\n    confirmButtonColor: undefined,\n    denyButtonText: 'No',\n    denyButtonAriaLabel: '',\n    denyButtonColor: undefined,\n    cancelButtonText: 'Cancel',\n    cancelButtonAriaLabel: '',\n    cancelButtonColor: undefined,\n    buttonsStyling: true,\n    reverseButtons: false,\n    focusConfirm: true,\n    focusDeny: false,\n    focusCancel: false,\n    returnFocus: true,\n    showCloseButton: false,\n    closeButtonHtml: '&times;',\n    closeButtonAriaLabel: 'Close this dialog',\n    loaderHtml: '',\n    showLoaderOnConfirm: false,\n    showLoaderOnDeny: false,\n    imageUrl: undefined,\n    imageWidth: undefined,\n    imageHeight: undefined,\n    imageAlt: '',\n    timer: undefined,\n    timerProgressBar: false,\n    width: undefined,\n    padding: undefined,\n    background: undefined,\n    input: undefined,\n    inputPlaceholder: '',\n    inputLabel: '',\n    inputValue: '',\n    inputOptions: {},\n    inputAutoFocus: true,\n    inputAutoTrim: true,\n    inputAttributes: {},\n    inputValidator: undefined,\n    returnInputValueOnDeny: false,\n    validationMessage: undefined,\n    grow: false,\n    position: 'center',\n    progressSteps: [],\n    currentProgressStep: undefined,\n    progressStepsDistance: undefined,\n    willOpen: undefined,\n    didOpen: undefined,\n    didRender: undefined,\n    willClose: undefined,\n    didClose: undefined,\n    didDestroy: undefined,\n    scrollbarPadding: true,\n    topLayer: false\n  };\n  const updatableParams = ['allowEscapeKey', 'allowOutsideClick', 'background', 'buttonsStyling', 'cancelButtonAriaLabel', 'cancelButtonColor', 'cancelButtonText', 'closeButtonAriaLabel', 'closeButtonHtml', 'color', 'confirmButtonAriaLabel', 'confirmButtonColor', 'confirmButtonText', 'currentProgressStep', 'customClass', 'denyButtonAriaLabel', 'denyButtonColor', 'denyButtonText', 'didClose', 'didDestroy', 'draggable', 'footer', 'hideClass', 'html', 'icon', 'iconColor', 'iconHtml', 'imageAlt', 'imageHeight', 'imageUrl', 'imageWidth', 'preConfirm', 'preDeny', 'progressSteps', 'returnFocus', 'reverseButtons', 'showCancelButton', 'showCloseButton', 'showConfirmButton', 'showDenyButton', 'text', 'title', 'titleText', 'theme', 'willClose'];\n\n  /** @type {Record<string, string | undefined>} */\n  const deprecatedParams = {\n    allowEnterKey: undefined\n  };\n  const toastIncompatibleParams = ['allowOutsideClick', 'allowEnterKey', 'backdrop', 'draggable', 'focusConfirm', 'focusDeny', 'focusCancel', 'returnFocus', 'heightAuto', 'keydownListenerCapture'];\n\n  /**\n   * Is valid parameter\n   *\n   * @param {string} paramName\n   * @returns {boolean}\n   */\n  const isValidParameter = paramName => {\n    return Object.prototype.hasOwnProperty.call(defaultParams, paramName);\n  };\n\n  /**\n   * Is valid parameter for Swal.update() method\n   *\n   * @param {string} paramName\n   * @returns {boolean}\n   */\n  const isUpdatableParameter = paramName => {\n    return updatableParams.indexOf(paramName) !== -1;\n  };\n\n  /**\n   * Is deprecated parameter\n   *\n   * @param {string} paramName\n   * @returns {string | undefined}\n   */\n  const isDeprecatedParameter = paramName => {\n    return deprecatedParams[paramName];\n  };\n\n  /**\n   * @param {string} param\n   */\n  const checkIfParamIsValid = param => {\n    if (!isValidParameter(param)) {\n      warn(`Unknown parameter \"${param}\"`);\n    }\n  };\n\n  /**\n   * @param {string} param\n   */\n  const checkIfToastParamIsValid = param => {\n    if (toastIncompatibleParams.includes(param)) {\n      warn(`The parameter \"${param}\" is incompatible with toasts`);\n    }\n  };\n\n  /**\n   * @param {string} param\n   */\n  const checkIfParamIsDeprecated = param => {\n    const isDeprecated = isDeprecatedParameter(param);\n    if (isDeprecated) {\n      warnAboutDeprecation(param, isDeprecated);\n    }\n  };\n\n  /**\n   * Show relevant warnings for given params\n   *\n   * @param {SweetAlertOptions} params\n   */\n  const showWarningsForParams = params => {\n    if (params.backdrop === false && params.allowOutsideClick) {\n      warn('\"allowOutsideClick\" parameter requires `backdrop` parameter to be set to `true`');\n    }\n    if (params.theme && !['light', 'dark', 'auto', 'minimal', 'borderless', 'embed-iframe', 'bulma', 'bulma-light', 'bulma-dark'].includes(params.theme)) {\n      warn(`Invalid theme \"${params.theme}\"`);\n    }\n    for (const param in params) {\n      checkIfParamIsValid(param);\n      if (params.toast) {\n        checkIfToastParamIsValid(param);\n      }\n      checkIfParamIsDeprecated(param);\n    }\n  };\n\n  /**\n   * Updates popup parameters.\n   *\n   * @param {SweetAlertOptions} params\n   */\n  function update(params) {\n    const container = getContainer();\n    const popup = getPopup();\n    const innerParams = privateProps.innerParams.get(this);\n    if (!popup || hasClass(popup, innerParams.hideClass.popup)) {\n      warn(`You're trying to update the closed or closing popup, that won't work. Use the update() method in preConfirm parameter or show a new popup.`);\n      return;\n    }\n    const validUpdatableParams = filterValidParams(params);\n    const updatedParams = Object.assign({}, innerParams, validUpdatableParams);\n    showWarningsForParams(updatedParams);\n    container.dataset['swal2Theme'] = updatedParams.theme;\n    render(this, updatedParams);\n    privateProps.innerParams.set(this, updatedParams);\n    Object.defineProperties(this, {\n      params: {\n        value: Object.assign({}, this.params, params),\n        writable: false,\n        enumerable: true\n      }\n    });\n  }\n\n  /**\n   * @param {SweetAlertOptions} params\n   * @returns {SweetAlertOptions}\n   */\n  const filterValidParams = params => {\n    const validUpdatableParams = {};\n    Object.keys(params).forEach(param => {\n      if (isUpdatableParameter(param)) {\n        validUpdatableParams[param] = params[param];\n      } else {\n        warn(`Invalid parameter to update: ${param}`);\n      }\n    });\n    return validUpdatableParams;\n  };\n\n  /**\n   * Dispose the current SweetAlert2 instance\n   */\n  function _destroy() {\n    const domCache = privateProps.domCache.get(this);\n    const innerParams = privateProps.innerParams.get(this);\n    if (!innerParams) {\n      disposeWeakMaps(this); // The WeakMaps might have been partly destroyed, we must recall it to dispose any remaining WeakMaps #2335\n      return; // This instance has already been destroyed\n    }\n\n    // Check if there is another Swal closing\n    if (domCache.popup && globalState.swalCloseEventFinishedCallback) {\n      globalState.swalCloseEventFinishedCallback();\n      delete globalState.swalCloseEventFinishedCallback;\n    }\n    if (typeof innerParams.didDestroy === 'function') {\n      innerParams.didDestroy();\n    }\n    globalState.eventEmitter.emit('didDestroy');\n    disposeSwal(this);\n  }\n\n  /**\n   * @param {SweetAlert} instance\n   */\n  const disposeSwal = instance => {\n    disposeWeakMaps(instance);\n    // Unset this.params so GC will dispose it (#1569)\n    delete instance.params;\n    // Unset globalState props so GC will dispose globalState (#1569)\n    delete globalState.keydownHandler;\n    delete globalState.keydownTarget;\n    // Unset currentInstance\n    delete globalState.currentInstance;\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   */\n  const disposeWeakMaps = instance => {\n    // If the current instance is awaiting a promise result, we keep the privateMethods to call them once the promise result is retrieved #2335\n    if (instance.isAwaitingPromise) {\n      unsetWeakMaps(privateProps, instance);\n      instance.isAwaitingPromise = true;\n    } else {\n      unsetWeakMaps(privateMethods, instance);\n      unsetWeakMaps(privateProps, instance);\n      delete instance.isAwaitingPromise;\n      // Unset instance methods\n      delete instance.disableButtons;\n      delete instance.enableButtons;\n      delete instance.getInput;\n      delete instance.disableInput;\n      delete instance.enableInput;\n      delete instance.hideLoading;\n      delete instance.disableLoading;\n      delete instance.showValidationMessage;\n      delete instance.resetValidationMessage;\n      delete instance.close;\n      delete instance.closePopup;\n      delete instance.closeModal;\n      delete instance.closeToast;\n      delete instance.rejectPromise;\n      delete instance.update;\n      delete instance._destroy;\n    }\n  };\n\n  /**\n   * @param {object} obj\n   * @param {SweetAlert} instance\n   */\n  const unsetWeakMaps = (obj, instance) => {\n    for (const i in obj) {\n      obj[i].delete(instance);\n    }\n  };\n  var instanceMethods = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    _destroy: _destroy,\n    close: close,\n    closeModal: close,\n    closePopup: close,\n    closeToast: close,\n    disableButtons: disableButtons,\n    disableInput: disableInput,\n    disableLoading: hideLoading,\n    enableButtons: enableButtons,\n    enableInput: enableInput,\n    getInput: getInput,\n    handleAwaitingPromise: handleAwaitingPromise,\n    hideLoading: hideLoading,\n    rejectPromise: rejectPromise,\n    resetValidationMessage: resetValidationMessage,\n    showValidationMessage: showValidationMessage,\n    update: update\n  });\n\n  /**\n   * @param {SweetAlertOptions} innerParams\n   * @param {DomCache} domCache\n   * @param {Function} dismissWith\n   */\n  const handlePopupClick = (innerParams, domCache, dismissWith) => {\n    if (innerParams.toast) {\n      handleToastClick(innerParams, domCache, dismissWith);\n    } else {\n      // Ignore click events that had mousedown on the popup but mouseup on the container\n      // This can happen when the user drags a slider\n      handleModalMousedown(domCache);\n\n      // Ignore click events that had mousedown on the container but mouseup on the popup\n      handleContainerMousedown(domCache);\n      handleModalClick(innerParams, domCache, dismissWith);\n    }\n  };\n\n  /**\n   * @param {SweetAlertOptions} innerParams\n   * @param {DomCache} domCache\n   * @param {Function} dismissWith\n   */\n  const handleToastClick = (innerParams, domCache, dismissWith) => {\n    // Closing toast by internal click\n    domCache.popup.onclick = () => {\n      if (innerParams && (isAnyButtonShown(innerParams) || innerParams.timer || innerParams.input)) {\n        return;\n      }\n      dismissWith(DismissReason.close);\n    };\n  };\n\n  /**\n   * @param {SweetAlertOptions} innerParams\n   * @returns {boolean}\n   */\n  const isAnyButtonShown = innerParams => {\n    return !!(innerParams.showConfirmButton || innerParams.showDenyButton || innerParams.showCancelButton || innerParams.showCloseButton);\n  };\n  let ignoreOutsideClick = false;\n\n  /**\n   * @param {DomCache} domCache\n   */\n  const handleModalMousedown = domCache => {\n    domCache.popup.onmousedown = () => {\n      domCache.container.onmouseup = function (e) {\n        domCache.container.onmouseup = () => {};\n        // We only check if the mouseup target is the container because usually it doesn't\n        // have any other direct children aside of the popup\n        if (e.target === domCache.container) {\n          ignoreOutsideClick = true;\n        }\n      };\n    };\n  };\n\n  /**\n   * @param {DomCache} domCache\n   */\n  const handleContainerMousedown = domCache => {\n    domCache.container.onmousedown = e => {\n      // prevent the modal text from being selected on double click on the container (allowOutsideClick: false)\n      if (e.target === domCache.container) {\n        e.preventDefault();\n      }\n      domCache.popup.onmouseup = function (e) {\n        domCache.popup.onmouseup = () => {};\n        // We also need to check if the mouseup target is a child of the popup\n        if (e.target === domCache.popup || e.target instanceof HTMLElement && domCache.popup.contains(e.target)) {\n          ignoreOutsideClick = true;\n        }\n      };\n    };\n  };\n\n  /**\n   * @param {SweetAlertOptions} innerParams\n   * @param {DomCache} domCache\n   * @param {Function} dismissWith\n   */\n  const handleModalClick = (innerParams, domCache, dismissWith) => {\n    domCache.container.onclick = e => {\n      if (ignoreOutsideClick) {\n        ignoreOutsideClick = false;\n        return;\n      }\n      if (e.target === domCache.container && callIfFunction(innerParams.allowOutsideClick)) {\n        dismissWith(DismissReason.backdrop);\n      }\n    };\n  };\n  const isJqueryElement = elem => typeof elem === 'object' && elem.jquery;\n  const isElement = elem => elem instanceof Element || isJqueryElement(elem);\n  const argsToParams = args => {\n    const params = {};\n    if (typeof args[0] === 'object' && !isElement(args[0])) {\n      Object.assign(params, args[0]);\n    } else {\n      ['title', 'html', 'icon'].forEach((name, index) => {\n        const arg = args[index];\n        if (typeof arg === 'string' || isElement(arg)) {\n          params[name] = arg;\n        } else if (arg !== undefined) {\n          error(`Unexpected type of ${name}! Expected \"string\" or \"Element\", got ${typeof arg}`);\n        }\n      });\n    }\n    return params;\n  };\n\n  /**\n   * Main method to create a new SweetAlert2 popup\n   *\n   * @param  {...SweetAlertOptions} args\n   * @returns {Promise<SweetAlertResult>}\n   */\n  function fire(...args) {\n    return new this(...args);\n  }\n\n  /**\n   * Returns an extended version of `Swal` containing `params` as defaults.\n   * Useful for reusing Swal configuration.\n   *\n   * For example:\n   *\n   * Before:\n   * const textPromptOptions = { input: 'text', showCancelButton: true }\n   * const {value: firstName} = await Swal.fire({ ...textPromptOptions, title: 'What is your first name?' })\n   * const {value: lastName} = await Swal.fire({ ...textPromptOptions, title: 'What is your last name?' })\n   *\n   * After:\n   * const TextPrompt = Swal.mixin({ input: 'text', showCancelButton: true })\n   * const {value: firstName} = await TextPrompt('What is your first name?')\n   * const {value: lastName} = await TextPrompt('What is your last name?')\n   *\n   * @param {SweetAlertOptions} mixinParams\n   * @returns {SweetAlert}\n   */\n  function mixin(mixinParams) {\n    class MixinSwal extends this {\n      _main(params, priorityMixinParams) {\n        return super._main(params, Object.assign({}, mixinParams, priorityMixinParams));\n      }\n    }\n    // @ts-ignore\n    return MixinSwal;\n  }\n\n  /**\n   * If `timer` parameter is set, returns number of milliseconds of timer remained.\n   * Otherwise, returns undefined.\n   *\n   * @returns {number | undefined}\n   */\n  const getTimerLeft = () => {\n    return globalState.timeout && globalState.timeout.getTimerLeft();\n  };\n\n  /**\n   * Stop timer. Returns number of milliseconds of timer remained.\n   * If `timer` parameter isn't set, returns undefined.\n   *\n   * @returns {number | undefined}\n   */\n  const stopTimer = () => {\n    if (globalState.timeout) {\n      stopTimerProgressBar();\n      return globalState.timeout.stop();\n    }\n  };\n\n  /**\n   * Resume timer. Returns number of milliseconds of timer remained.\n   * If `timer` parameter isn't set, returns undefined.\n   *\n   * @returns {number | undefined}\n   */\n  const resumeTimer = () => {\n    if (globalState.timeout) {\n      const remaining = globalState.timeout.start();\n      animateTimerProgressBar(remaining);\n      return remaining;\n    }\n  };\n\n  /**\n   * Resume timer. Returns number of milliseconds of timer remained.\n   * If `timer` parameter isn't set, returns undefined.\n   *\n   * @returns {number | undefined}\n   */\n  const toggleTimer = () => {\n    const timer = globalState.timeout;\n    return timer && (timer.running ? stopTimer() : resumeTimer());\n  };\n\n  /**\n   * Increase timer. Returns number of milliseconds of an updated timer.\n   * If `timer` parameter isn't set, returns undefined.\n   *\n   * @param {number} ms\n   * @returns {number | undefined}\n   */\n  const increaseTimer = ms => {\n    if (globalState.timeout) {\n      const remaining = globalState.timeout.increase(ms);\n      animateTimerProgressBar(remaining, true);\n      return remaining;\n    }\n  };\n\n  /**\n   * Check if timer is running. Returns true if timer is running\n   * or false if timer is paused or stopped.\n   * If `timer` parameter isn't set, returns undefined\n   *\n   * @returns {boolean}\n   */\n  const isTimerRunning = () => {\n    return !!(globalState.timeout && globalState.timeout.isRunning());\n  };\n  let bodyClickListenerAdded = false;\n  const clickHandlers = {};\n\n  /**\n   * @param {string} attr\n   */\n  function bindClickHandler(attr = 'data-swal-template') {\n    clickHandlers[attr] = this;\n    if (!bodyClickListenerAdded) {\n      document.body.addEventListener('click', bodyClickListener);\n      bodyClickListenerAdded = true;\n    }\n  }\n  const bodyClickListener = event => {\n    for (let el = event.target; el && el !== document; el = el.parentNode) {\n      for (const attr in clickHandlers) {\n        const template = el.getAttribute(attr);\n        if (template) {\n          clickHandlers[attr].fire({\n            template\n          });\n          return;\n        }\n      }\n    }\n  };\n\n  // Source: https://gist.github.com/mudge/5830382?permalink_comment_id=2691957#gistcomment-2691957\n\n  class EventEmitter {\n    constructor() {\n      /** @type {Events} */\n      this.events = {};\n    }\n\n    /**\n     * @param {string} eventName\n     * @returns {EventHandlers}\n     */\n    _getHandlersByEventName(eventName) {\n      if (typeof this.events[eventName] === 'undefined') {\n        // not Set because we need to keep the FIFO order\n        // https://github.com/sweetalert2/sweetalert2/pull/2763#discussion_r1748990334\n        this.events[eventName] = [];\n      }\n      return this.events[eventName];\n    }\n\n    /**\n     * @param {string} eventName\n     * @param {EventHandler} eventHandler\n     */\n    on(eventName, eventHandler) {\n      const currentHandlers = this._getHandlersByEventName(eventName);\n      if (!currentHandlers.includes(eventHandler)) {\n        currentHandlers.push(eventHandler);\n      }\n    }\n\n    /**\n     * @param {string} eventName\n     * @param {EventHandler} eventHandler\n     */\n    once(eventName, eventHandler) {\n      /**\n       * @param {Array} args\n       */\n      const onceFn = (...args) => {\n        this.removeListener(eventName, onceFn);\n        eventHandler.apply(this, args);\n      };\n      this.on(eventName, onceFn);\n    }\n\n    /**\n     * @param {string} eventName\n     * @param {Array} args\n     */\n    emit(eventName, ...args) {\n      this._getHandlersByEventName(eventName).forEach(\n      /**\n       * @param {EventHandler} eventHandler\n       */\n      eventHandler => {\n        try {\n          eventHandler.apply(this, args);\n        } catch (error) {\n          console.error(error);\n        }\n      });\n    }\n\n    /**\n     * @param {string} eventName\n     * @param {EventHandler} eventHandler\n     */\n    removeListener(eventName, eventHandler) {\n      const currentHandlers = this._getHandlersByEventName(eventName);\n      const index = currentHandlers.indexOf(eventHandler);\n      if (index > -1) {\n        currentHandlers.splice(index, 1);\n      }\n    }\n\n    /**\n     * @param {string} eventName\n     */\n    removeAllListeners(eventName) {\n      if (this.events[eventName] !== undefined) {\n        // https://github.com/sweetalert2/sweetalert2/pull/2763#discussion_r1749239222\n        this.events[eventName].length = 0;\n      }\n    }\n    reset() {\n      this.events = {};\n    }\n  }\n  globalState.eventEmitter = new EventEmitter();\n\n  /**\n   * @param {string} eventName\n   * @param {EventHandler} eventHandler\n   */\n  const on = (eventName, eventHandler) => {\n    globalState.eventEmitter.on(eventName, eventHandler);\n  };\n\n  /**\n   * @param {string} eventName\n   * @param {EventHandler} eventHandler\n   */\n  const once = (eventName, eventHandler) => {\n    globalState.eventEmitter.once(eventName, eventHandler);\n  };\n\n  /**\n   * @param {string} [eventName]\n   * @param {EventHandler} [eventHandler]\n   */\n  const off = (eventName, eventHandler) => {\n    // Remove all handlers for all events\n    if (!eventName) {\n      globalState.eventEmitter.reset();\n      return;\n    }\n    if (eventHandler) {\n      // Remove a specific handler\n      globalState.eventEmitter.removeListener(eventName, eventHandler);\n    } else {\n      // Remove all handlers for a specific event\n      globalState.eventEmitter.removeAllListeners(eventName);\n    }\n  };\n  var staticMethods = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    argsToParams: argsToParams,\n    bindClickHandler: bindClickHandler,\n    clickCancel: clickCancel,\n    clickConfirm: clickConfirm,\n    clickDeny: clickDeny,\n    enableLoading: showLoading,\n    fire: fire,\n    getActions: getActions,\n    getCancelButton: getCancelButton,\n    getCloseButton: getCloseButton,\n    getConfirmButton: getConfirmButton,\n    getContainer: getContainer,\n    getDenyButton: getDenyButton,\n    getFocusableElements: getFocusableElements,\n    getFooter: getFooter,\n    getHtmlContainer: getHtmlContainer,\n    getIcon: getIcon,\n    getIconContent: getIconContent,\n    getImage: getImage,\n    getInputLabel: getInputLabel,\n    getLoader: getLoader,\n    getPopup: getPopup,\n    getProgressSteps: getProgressSteps,\n    getTimerLeft: getTimerLeft,\n    getTimerProgressBar: getTimerProgressBar,\n    getTitle: getTitle,\n    getValidationMessage: getValidationMessage,\n    increaseTimer: increaseTimer,\n    isDeprecatedParameter: isDeprecatedParameter,\n    isLoading: isLoading,\n    isTimerRunning: isTimerRunning,\n    isUpdatableParameter: isUpdatableParameter,\n    isValidParameter: isValidParameter,\n    isVisible: isVisible,\n    mixin: mixin,\n    off: off,\n    on: on,\n    once: once,\n    resumeTimer: resumeTimer,\n    showLoading: showLoading,\n    stopTimer: stopTimer,\n    toggleTimer: toggleTimer\n  });\n  class Timer {\n    /**\n     * @param {Function} callback\n     * @param {number} delay\n     */\n    constructor(callback, delay) {\n      this.callback = callback;\n      this.remaining = delay;\n      this.running = false;\n      this.start();\n    }\n\n    /**\n     * @returns {number}\n     */\n    start() {\n      if (!this.running) {\n        this.running = true;\n        this.started = new Date();\n        this.id = setTimeout(this.callback, this.remaining);\n      }\n      return this.remaining;\n    }\n\n    /**\n     * @returns {number}\n     */\n    stop() {\n      if (this.started && this.running) {\n        this.running = false;\n        clearTimeout(this.id);\n        this.remaining -= new Date().getTime() - this.started.getTime();\n      }\n      return this.remaining;\n    }\n\n    /**\n     * @param {number} n\n     * @returns {number}\n     */\n    increase(n) {\n      const running = this.running;\n      if (running) {\n        this.stop();\n      }\n      this.remaining += n;\n      if (running) {\n        this.start();\n      }\n      return this.remaining;\n    }\n\n    /**\n     * @returns {number}\n     */\n    getTimerLeft() {\n      if (this.running) {\n        this.stop();\n        this.start();\n      }\n      return this.remaining;\n    }\n\n    /**\n     * @returns {boolean}\n     */\n    isRunning() {\n      return this.running;\n    }\n  }\n  const swalStringParams = ['swal-title', 'swal-html', 'swal-footer'];\n\n  /**\n   * @param {SweetAlertOptions} params\n   * @returns {SweetAlertOptions}\n   */\n  const getTemplateParams = params => {\n    const template = typeof params.template === 'string' ? (/** @type {HTMLTemplateElement} */document.querySelector(params.template)) : params.template;\n    if (!template) {\n      return {};\n    }\n    /** @type {DocumentFragment} */\n    const templateContent = template.content;\n    showWarningsForElements(templateContent);\n    const result = Object.assign(getSwalParams(templateContent), getSwalFunctionParams(templateContent), getSwalButtons(templateContent), getSwalImage(templateContent), getSwalIcon(templateContent), getSwalInput(templateContent), getSwalStringParams(templateContent, swalStringParams));\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @returns {Record<string, any>}\n   */\n  const getSwalParams = templateContent => {\n    /** @type {Record<string, any>} */\n    const result = {};\n    /** @type {HTMLElement[]} */\n    const swalParams = Array.from(templateContent.querySelectorAll('swal-param'));\n    swalParams.forEach(param => {\n      showWarningsForAttributes(param, ['name', 'value']);\n      const paramName = /** @type {keyof SweetAlertOptions} */param.getAttribute('name');\n      const value = param.getAttribute('value');\n      if (!paramName || !value) {\n        return;\n      }\n      if (typeof defaultParams[paramName] === 'boolean') {\n        result[paramName] = value !== 'false';\n      } else if (typeof defaultParams[paramName] === 'object') {\n        result[paramName] = JSON.parse(value);\n      } else {\n        result[paramName] = value;\n      }\n    });\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @returns {Record<string, any>}\n   */\n  const getSwalFunctionParams = templateContent => {\n    /** @type {Record<string, any>} */\n    const result = {};\n    /** @type {HTMLElement[]} */\n    const swalFunctions = Array.from(templateContent.querySelectorAll('swal-function-param'));\n    swalFunctions.forEach(param => {\n      const paramName = /** @type {keyof SweetAlertOptions} */param.getAttribute('name');\n      const value = param.getAttribute('value');\n      if (!paramName || !value) {\n        return;\n      }\n      result[paramName] = new Function(`return ${value}`)();\n    });\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @returns {Record<string, any>}\n   */\n  const getSwalButtons = templateContent => {\n    /** @type {Record<string, any>} */\n    const result = {};\n    /** @type {HTMLElement[]} */\n    const swalButtons = Array.from(templateContent.querySelectorAll('swal-button'));\n    swalButtons.forEach(button => {\n      showWarningsForAttributes(button, ['type', 'color', 'aria-label']);\n      const type = button.getAttribute('type');\n      if (!type || !['confirm', 'cancel', 'deny'].includes(type)) {\n        return;\n      }\n      result[`${type}ButtonText`] = button.innerHTML;\n      result[`show${capitalizeFirstLetter(type)}Button`] = true;\n      if (button.hasAttribute('color')) {\n        result[`${type}ButtonColor`] = button.getAttribute('color');\n      }\n      if (button.hasAttribute('aria-label')) {\n        result[`${type}ButtonAriaLabel`] = button.getAttribute('aria-label');\n      }\n    });\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @returns {Pick<SweetAlertOptions, 'imageUrl' | 'imageWidth' | 'imageHeight' | 'imageAlt'>}\n   */\n  const getSwalImage = templateContent => {\n    const result = {};\n    /** @type {HTMLElement | null} */\n    const image = templateContent.querySelector('swal-image');\n    if (image) {\n      showWarningsForAttributes(image, ['src', 'width', 'height', 'alt']);\n      if (image.hasAttribute('src')) {\n        result.imageUrl = image.getAttribute('src') || undefined;\n      }\n      if (image.hasAttribute('width')) {\n        result.imageWidth = image.getAttribute('width') || undefined;\n      }\n      if (image.hasAttribute('height')) {\n        result.imageHeight = image.getAttribute('height') || undefined;\n      }\n      if (image.hasAttribute('alt')) {\n        result.imageAlt = image.getAttribute('alt') || undefined;\n      }\n    }\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @returns {Record<string, any>}\n   */\n  const getSwalIcon = templateContent => {\n    const result = {};\n    /** @type {HTMLElement | null} */\n    const icon = templateContent.querySelector('swal-icon');\n    if (icon) {\n      showWarningsForAttributes(icon, ['type', 'color']);\n      if (icon.hasAttribute('type')) {\n        result.icon = icon.getAttribute('type');\n      }\n      if (icon.hasAttribute('color')) {\n        result.iconColor = icon.getAttribute('color');\n      }\n      result.iconHtml = icon.innerHTML;\n    }\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @returns {Record<string, any>}\n   */\n  const getSwalInput = templateContent => {\n    /** @type {Record<string, any>} */\n    const result = {};\n    /** @type {HTMLElement | null} */\n    const input = templateContent.querySelector('swal-input');\n    if (input) {\n      showWarningsForAttributes(input, ['type', 'label', 'placeholder', 'value']);\n      result.input = input.getAttribute('type') || 'text';\n      if (input.hasAttribute('label')) {\n        result.inputLabel = input.getAttribute('label');\n      }\n      if (input.hasAttribute('placeholder')) {\n        result.inputPlaceholder = input.getAttribute('placeholder');\n      }\n      if (input.hasAttribute('value')) {\n        result.inputValue = input.getAttribute('value');\n      }\n    }\n    /** @type {HTMLElement[]} */\n    const inputOptions = Array.from(templateContent.querySelectorAll('swal-input-option'));\n    if (inputOptions.length) {\n      result.inputOptions = {};\n      inputOptions.forEach(option => {\n        showWarningsForAttributes(option, ['value']);\n        const optionValue = option.getAttribute('value');\n        if (!optionValue) {\n          return;\n        }\n        const optionName = option.innerHTML;\n        result.inputOptions[optionValue] = optionName;\n      });\n    }\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @param {string[]} paramNames\n   * @returns {Record<string, any>}\n   */\n  const getSwalStringParams = (templateContent, paramNames) => {\n    /** @type {Record<string, any>} */\n    const result = {};\n    for (const i in paramNames) {\n      const paramName = paramNames[i];\n      /** @type {HTMLElement | null} */\n      const tag = templateContent.querySelector(paramName);\n      if (tag) {\n        showWarningsForAttributes(tag, []);\n        result[paramName.replace(/^swal-/, '')] = tag.innerHTML.trim();\n      }\n    }\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   */\n  const showWarningsForElements = templateContent => {\n    const allowedElements = swalStringParams.concat(['swal-param', 'swal-function-param', 'swal-button', 'swal-image', 'swal-icon', 'swal-input', 'swal-input-option']);\n    Array.from(templateContent.children).forEach(el => {\n      const tagName = el.tagName.toLowerCase();\n      if (!allowedElements.includes(tagName)) {\n        warn(`Unrecognized element <${tagName}>`);\n      }\n    });\n  };\n\n  /**\n   * @param {HTMLElement} el\n   * @param {string[]} allowedAttributes\n   */\n  const showWarningsForAttributes = (el, allowedAttributes) => {\n    Array.from(el.attributes).forEach(attribute => {\n      if (allowedAttributes.indexOf(attribute.name) === -1) {\n        warn([`Unrecognized attribute \"${attribute.name}\" on <${el.tagName.toLowerCase()}>.`, `${allowedAttributes.length ? `Allowed attributes are: ${allowedAttributes.join(', ')}` : 'To set the value, use HTML within the element.'}`]);\n      }\n    });\n  };\n  const SHOW_CLASS_TIMEOUT = 10;\n\n  /**\n   * Open popup, add necessary classes and styles, fix scrollbar\n   *\n   * @param {SweetAlertOptions} params\n   */\n  const openPopup = params => {\n    const container = getContainer();\n    const popup = getPopup();\n    if (typeof params.willOpen === 'function') {\n      params.willOpen(popup);\n    }\n    globalState.eventEmitter.emit('willOpen', popup);\n    const bodyStyles = window.getComputedStyle(document.body);\n    const initialBodyOverflow = bodyStyles.overflowY;\n    addClasses(container, popup, params);\n\n    // scrolling is 'hidden' until animation is done, after that 'auto'\n    setTimeout(() => {\n      setScrollingVisibility(container, popup);\n    }, SHOW_CLASS_TIMEOUT);\n    if (isModal()) {\n      fixScrollContainer(container, params.scrollbarPadding, initialBodyOverflow);\n      setAriaHidden();\n    }\n    if (!isToast() && !globalState.previousActiveElement) {\n      globalState.previousActiveElement = document.activeElement;\n    }\n    if (typeof params.didOpen === 'function') {\n      setTimeout(() => params.didOpen(popup));\n    }\n    globalState.eventEmitter.emit('didOpen', popup);\n    removeClass(container, swalClasses['no-transition']);\n  };\n\n  /**\n   * @param {AnimationEvent} event\n   */\n  const swalOpenAnimationFinished = event => {\n    const popup = getPopup();\n    if (event.target !== popup) {\n      return;\n    }\n    const container = getContainer();\n    popup.removeEventListener('animationend', swalOpenAnimationFinished);\n    popup.removeEventListener('transitionend', swalOpenAnimationFinished);\n    container.style.overflowY = 'auto';\n  };\n\n  /**\n   * @param {HTMLElement} container\n   * @param {HTMLElement} popup\n   */\n  const setScrollingVisibility = (container, popup) => {\n    if (hasCssAnimation(popup)) {\n      container.style.overflowY = 'hidden';\n      popup.addEventListener('animationend', swalOpenAnimationFinished);\n      popup.addEventListener('transitionend', swalOpenAnimationFinished);\n    } else {\n      container.style.overflowY = 'auto';\n    }\n  };\n\n  /**\n   * @param {HTMLElement} container\n   * @param {boolean} scrollbarPadding\n   * @param {string} initialBodyOverflow\n   */\n  const fixScrollContainer = (container, scrollbarPadding, initialBodyOverflow) => {\n    iOSfix();\n    if (scrollbarPadding && initialBodyOverflow !== 'hidden') {\n      replaceScrollbarWithPadding(initialBodyOverflow);\n    }\n\n    // sweetalert2/issues/1247\n    setTimeout(() => {\n      container.scrollTop = 0;\n    });\n  };\n\n  /**\n   * @param {HTMLElement} container\n   * @param {HTMLElement} popup\n   * @param {SweetAlertOptions} params\n   */\n  const addClasses = (container, popup, params) => {\n    addClass(container, params.showClass.backdrop);\n    if (params.animation) {\n      // this workaround with opacity is needed for https://github.com/sweetalert2/sweetalert2/issues/2059\n      popup.style.setProperty('opacity', '0', 'important');\n      show(popup, 'grid');\n      setTimeout(() => {\n        // Animate popup right after showing it\n        addClass(popup, params.showClass.popup);\n        // and remove the opacity workaround\n        popup.style.removeProperty('opacity');\n      }, SHOW_CLASS_TIMEOUT); // 10ms in order to fix #2062\n    } else {\n      show(popup, 'grid');\n    }\n    addClass([document.documentElement, document.body], swalClasses.shown);\n    if (params.heightAuto && params.backdrop && !params.toast) {\n      addClass([document.documentElement, document.body], swalClasses['height-auto']);\n    }\n  };\n  var defaultInputValidators = {\n    /**\n     * @param {string} string\n     * @param {string} [validationMessage]\n     * @returns {Promise<string | void>}\n     */\n    email: (string, validationMessage) => {\n      return /^[a-zA-Z0-9.+_'-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z0-9-]+$/.test(string) ? Promise.resolve() : Promise.resolve(validationMessage || 'Invalid email address');\n    },\n    /**\n     * @param {string} string\n     * @param {string} [validationMessage]\n     * @returns {Promise<string | void>}\n     */\n    url: (string, validationMessage) => {\n      // taken from https://stackoverflow.com/a/3809435 with a small change from #1306 and #2013\n      return /^https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\\.[a-z]{2,63}\\b([-a-zA-Z0-9@:%_+.~#?&/=]*)$/.test(string) ? Promise.resolve() : Promise.resolve(validationMessage || 'Invalid URL');\n    }\n  };\n\n  /**\n   * @param {SweetAlertOptions} params\n   */\n  function setDefaultInputValidators(params) {\n    // Use default `inputValidator` for supported input types if not provided\n    if (params.inputValidator) {\n      return;\n    }\n    if (params.input === 'email') {\n      params.inputValidator = defaultInputValidators['email'];\n    }\n    if (params.input === 'url') {\n      params.inputValidator = defaultInputValidators['url'];\n    }\n  }\n\n  /**\n   * @param {SweetAlertOptions} params\n   */\n  function validateCustomTargetElement(params) {\n    // Determine if the custom target element is valid\n    if (!params.target || typeof params.target === 'string' && !document.querySelector(params.target) || typeof params.target !== 'string' && !params.target.appendChild) {\n      warn('Target parameter is not valid, defaulting to \"body\"');\n      params.target = 'body';\n    }\n  }\n\n  /**\n   * Set type, text and actions on popup\n   *\n   * @param {SweetAlertOptions} params\n   */\n  function setParameters(params) {\n    setDefaultInputValidators(params);\n\n    // showLoaderOnConfirm && preConfirm\n    if (params.showLoaderOnConfirm && !params.preConfirm) {\n      warn('showLoaderOnConfirm is set to true, but preConfirm is not defined.\\n' + 'showLoaderOnConfirm should be used together with preConfirm, see usage example:\\n' + 'https://sweetalert2.github.io/#ajax-request');\n    }\n    validateCustomTargetElement(params);\n\n    // Replace newlines with <br> in title\n    if (typeof params.title === 'string') {\n      params.title = params.title.split('\\n').join('<br />');\n    }\n    init(params);\n  }\n\n  /** @type {SweetAlert} */\n  let currentInstance;\n  var _promise = /*#__PURE__*/new WeakMap();\n  class SweetAlert {\n    /**\n     * @param {...any} args\n     * @this {SweetAlert}\n     */\n    constructor(...args) {\n      /**\n       * @type {Promise<SweetAlertResult>}\n       */\n      _classPrivateFieldInitSpec(this, _promise, void 0);\n      // Prevent run in Node env\n      if (typeof window === 'undefined') {\n        return;\n      }\n      currentInstance = this;\n\n      // @ts-ignore\n      const outerParams = Object.freeze(this.constructor.argsToParams(args));\n\n      /** @type {Readonly<SweetAlertOptions>} */\n      this.params = outerParams;\n\n      /** @type {boolean} */\n      this.isAwaitingPromise = false;\n      _classPrivateFieldSet2(_promise, this, this._main(currentInstance.params));\n    }\n    _main(userParams, mixinParams = {}) {\n      showWarningsForParams(Object.assign({}, mixinParams, userParams));\n      if (globalState.currentInstance) {\n        const swalPromiseResolve = privateMethods.swalPromiseResolve.get(globalState.currentInstance);\n        const {\n          isAwaitingPromise\n        } = globalState.currentInstance;\n        globalState.currentInstance._destroy();\n        if (!isAwaitingPromise) {\n          swalPromiseResolve({\n            isDismissed: true\n          });\n        }\n        if (isModal()) {\n          unsetAriaHidden();\n        }\n      }\n      globalState.currentInstance = currentInstance;\n      const innerParams = prepareParams(userParams, mixinParams);\n      setParameters(innerParams);\n      Object.freeze(innerParams);\n\n      // clear the previous timer\n      if (globalState.timeout) {\n        globalState.timeout.stop();\n        delete globalState.timeout;\n      }\n\n      // clear the restore focus timeout\n      clearTimeout(globalState.restoreFocusTimeout);\n      const domCache = populateDomCache(currentInstance);\n      render(currentInstance, innerParams);\n      privateProps.innerParams.set(currentInstance, innerParams);\n      return swalPromise(currentInstance, domCache, innerParams);\n    }\n\n    // `catch` cannot be the name of a module export, so we define our thenable methods here instead\n    then(onFulfilled) {\n      return _classPrivateFieldGet2(_promise, this).then(onFulfilled);\n    }\n    finally(onFinally) {\n      return _classPrivateFieldGet2(_promise, this).finally(onFinally);\n    }\n  }\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {DomCache} domCache\n   * @param {SweetAlertOptions} innerParams\n   * @returns {Promise}\n   */\n  const swalPromise = (instance, domCache, innerParams) => {\n    return new Promise((resolve, reject) => {\n      // functions to handle all closings/dismissals\n      /**\n       * @param {DismissReason} dismiss\n       */\n      const dismissWith = dismiss => {\n        instance.close({\n          isDismissed: true,\n          dismiss\n        });\n      };\n      privateMethods.swalPromiseResolve.set(instance, resolve);\n      privateMethods.swalPromiseReject.set(instance, reject);\n      domCache.confirmButton.onclick = () => {\n        handleConfirmButtonClick(instance);\n      };\n      domCache.denyButton.onclick = () => {\n        handleDenyButtonClick(instance);\n      };\n      domCache.cancelButton.onclick = () => {\n        handleCancelButtonClick(instance, dismissWith);\n      };\n      domCache.closeButton.onclick = () => {\n        dismissWith(DismissReason.close);\n      };\n      handlePopupClick(innerParams, domCache, dismissWith);\n      addKeydownHandler(globalState, innerParams, dismissWith);\n      handleInputOptionsAndValue(instance, innerParams);\n      openPopup(innerParams);\n      setupTimer(globalState, innerParams, dismissWith);\n      initFocus(domCache, innerParams);\n\n      // Scroll container to top on open (#1247, #1946)\n      setTimeout(() => {\n        domCache.container.scrollTop = 0;\n      });\n    });\n  };\n\n  /**\n   * @param {SweetAlertOptions} userParams\n   * @param {SweetAlertOptions} mixinParams\n   * @returns {SweetAlertOptions}\n   */\n  const prepareParams = (userParams, mixinParams) => {\n    const templateParams = getTemplateParams(userParams);\n    const params = Object.assign({}, defaultParams, mixinParams, templateParams, userParams); // precedence is described in #2131\n    params.showClass = Object.assign({}, defaultParams.showClass, params.showClass);\n    params.hideClass = Object.assign({}, defaultParams.hideClass, params.hideClass);\n    if (params.animation === false) {\n      params.showClass = {\n        backdrop: 'swal2-noanimation'\n      };\n      params.hideClass = {};\n    }\n    return params;\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @returns {DomCache}\n   */\n  const populateDomCache = instance => {\n    const domCache = {\n      popup: getPopup(),\n      container: getContainer(),\n      actions: getActions(),\n      confirmButton: getConfirmButton(),\n      denyButton: getDenyButton(),\n      cancelButton: getCancelButton(),\n      loader: getLoader(),\n      closeButton: getCloseButton(),\n      validationMessage: getValidationMessage(),\n      progressSteps: getProgressSteps()\n    };\n    privateProps.domCache.set(instance, domCache);\n    return domCache;\n  };\n\n  /**\n   * @param {GlobalState} globalState\n   * @param {SweetAlertOptions} innerParams\n   * @param {Function} dismissWith\n   */\n  const setupTimer = (globalState, innerParams, dismissWith) => {\n    const timerProgressBar = getTimerProgressBar();\n    hide(timerProgressBar);\n    if (innerParams.timer) {\n      globalState.timeout = new Timer(() => {\n        dismissWith('timer');\n        delete globalState.timeout;\n      }, innerParams.timer);\n      if (innerParams.timerProgressBar) {\n        show(timerProgressBar);\n        applyCustomClass(timerProgressBar, innerParams, 'timerProgressBar');\n        setTimeout(() => {\n          if (globalState.timeout && globalState.timeout.running) {\n            // timer can be already stopped or unset at this point\n            animateTimerProgressBar(innerParams.timer);\n          }\n        });\n      }\n    }\n  };\n\n  /**\n   * Initialize focus in the popup:\n   *\n   * 1. If `toast` is `true`, don't steal focus from the document.\n   * 2. Else if there is an [autofocus] element, focus it.\n   * 3. Else if `focusConfirm` is `true` and confirm button is visible, focus it.\n   * 4. Else if `focusDeny` is `true` and deny button is visible, focus it.\n   * 5. Else if `focusCancel` is `true` and cancel button is visible, focus it.\n   * 6. Else focus the first focusable element in a popup (if any).\n   *\n   * @param {DomCache} domCache\n   * @param {SweetAlertOptions} innerParams\n   */\n  const initFocus = (domCache, innerParams) => {\n    if (innerParams.toast) {\n      return;\n    }\n    // TODO: this is dumb, remove `allowEnterKey` param in the next major version\n    if (!callIfFunction(innerParams.allowEnterKey)) {\n      warnAboutDeprecation('allowEnterKey');\n      blurActiveElement();\n      return;\n    }\n    if (focusAutofocus(domCache)) {\n      return;\n    }\n    if (focusButton(domCache, innerParams)) {\n      return;\n    }\n    setFocus(-1, 1);\n  };\n\n  /**\n   * @param {DomCache} domCache\n   * @returns {boolean}\n   */\n  const focusAutofocus = domCache => {\n    const autofocusElements = Array.from(domCache.popup.querySelectorAll('[autofocus]'));\n    for (const autofocusElement of autofocusElements) {\n      if (autofocusElement instanceof HTMLElement && isVisible$1(autofocusElement)) {\n        autofocusElement.focus();\n        return true;\n      }\n    }\n    return false;\n  };\n\n  /**\n   * @param {DomCache} domCache\n   * @param {SweetAlertOptions} innerParams\n   * @returns {boolean}\n   */\n  const focusButton = (domCache, innerParams) => {\n    if (innerParams.focusDeny && isVisible$1(domCache.denyButton)) {\n      domCache.denyButton.focus();\n      return true;\n    }\n    if (innerParams.focusCancel && isVisible$1(domCache.cancelButton)) {\n      domCache.cancelButton.focus();\n      return true;\n    }\n    if (innerParams.focusConfirm && isVisible$1(domCache.confirmButton)) {\n      domCache.confirmButton.focus();\n      return true;\n    }\n    return false;\n  };\n  const blurActiveElement = () => {\n    if (document.activeElement instanceof HTMLElement && typeof document.activeElement.blur === 'function') {\n      document.activeElement.blur();\n    }\n  };\n\n  // Dear russian users visiting russian sites. Let's have fun.\n  if (typeof window !== 'undefined' && /^ru\\b/.test(navigator.language) && location.host.match(/\\.(ru|su|by|xn--p1ai)$/)) {\n    const now = new Date();\n    const initiationDate = localStorage.getItem('swal-initiation');\n    if (!initiationDate) {\n      localStorage.setItem('swal-initiation', `${now}`);\n    } else if ((now.getTime() - Date.parse(initiationDate)) / (1000 * 60 * 60 * 24) > 3) {\n      setTimeout(() => {\n        document.body.style.pointerEvents = 'none';\n        const ukrainianAnthem = document.createElement('audio');\n        ukrainianAnthem.src = 'https://flag-gimn.ru/wp-content/uploads/2021/09/Ukraina.mp3';\n        ukrainianAnthem.loop = true;\n        document.body.appendChild(ukrainianAnthem);\n        setTimeout(() => {\n          ukrainianAnthem.play().catch(() => {\n            // ignore\n          });\n        }, 2500);\n      }, 500);\n    }\n  }\n\n  // Assign instance methods from src/instanceMethods/*.js to prototype\n  SweetAlert.prototype.disableButtons = disableButtons;\n  SweetAlert.prototype.enableButtons = enableButtons;\n  SweetAlert.prototype.getInput = getInput;\n  SweetAlert.prototype.disableInput = disableInput;\n  SweetAlert.prototype.enableInput = enableInput;\n  SweetAlert.prototype.hideLoading = hideLoading;\n  SweetAlert.prototype.disableLoading = hideLoading;\n  SweetAlert.prototype.showValidationMessage = showValidationMessage;\n  SweetAlert.prototype.resetValidationMessage = resetValidationMessage;\n  SweetAlert.prototype.close = close;\n  SweetAlert.prototype.closePopup = close;\n  SweetAlert.prototype.closeModal = close;\n  SweetAlert.prototype.closeToast = close;\n  SweetAlert.prototype.rejectPromise = rejectPromise;\n  SweetAlert.prototype.update = update;\n  SweetAlert.prototype._destroy = _destroy;\n\n  // Assign static methods from src/staticMethods/*.js to constructor\n  Object.assign(SweetAlert, staticMethods);\n\n  // Proxy to instance methods to constructor, for now, for backwards compatibility\n  Object.keys(instanceMethods).forEach(key => {\n    /**\n     * @param {...any} args\n     * @returns {any | undefined}\n     */\n    SweetAlert[key] = function (...args) {\n      if (currentInstance && currentInstance[key]) {\n        return currentInstance[key](...args);\n      }\n      return null;\n    };\n  });\n  SweetAlert.DismissReason = DismissReason;\n  SweetAlert.version = '11.22.0';\n  const Swal = SweetAlert;\n  // @ts-ignore\n  Swal.default = Swal;\n  return Swal;\n});\nif (typeof this !== 'undefined' && this.Sweetalert2) {\n  this.swal = this.sweetAlert = this.Swal = this.SweetAlert = this.Sweetalert2;\n}\n\"undefined\" != typeof document && function (e, t) {\n  var n = e.createElement(\"style\");\n  if (e.getElementsByTagName(\"head\")[0].appendChild(n), n.styleSheet) n.styleSheet.disabled || (n.styleSheet.cssText = t);else try {\n    n.innerHTML = t;\n  } catch (e) {\n    n.innerText = t;\n  }\n}(document, \":root{--swal2-outline: 0 0 0 3px rgba(100, 150, 200, 0.5);--swal2-container-padding: 0.625em;--swal2-backdrop: rgba(0, 0, 0, 0.4);--swal2-backdrop-transition: background-color 0.1s;--swal2-width: 32em;--swal2-padding: 0 0 1.25em;--swal2-border: none;--swal2-border-radius: 0.3125rem;--swal2-background: white;--swal2-color: #545454;--swal2-show-animation: swal2-show 0.3s;--swal2-hide-animation: swal2-hide 0.15s forwards;--swal2-icon-zoom: 1;--swal2-icon-animations: true;--swal2-title-padding: 0.8em 1em 0;--swal2-html-container-padding: 1em 1.6em 0.3em;--swal2-input-border: 1px solid #d9d9d9;--swal2-input-border-radius: 0.1875em;--swal2-input-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px transparent;--swal2-input-background: transparent;--swal2-input-transition: border-color 0.2s, box-shadow 0.2s;--swal2-input-hover-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px transparent;--swal2-input-focus-border: 1px solid #b4dbed;--swal2-input-focus-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px $swal2-outline-color;--swal2-progress-step-background: #add8e6;--swal2-validation-message-background: #f0f0f0;--swal2-validation-message-color: #666;--swal2-footer-border-color: #eee;--swal2-footer-background: transparent;--swal2-footer-color: inherit;--swal2-close-button-position: initial;--swal2-close-button-inset: auto;--swal2-close-button-font-size: 2.5em;--swal2-close-button-color: #ccc;--swal2-close-button-transition: color 0.2s, box-shadow 0.2s;--swal2-close-button-outline: initial;--swal2-close-button-box-shadow: inset 0 0 0 3px transparent;--swal2-close-button-focus-box-shadow: inset var(--swal2-outline);--swal2-close-button-hover-transform: none;--swal2-actions-justify-content: center;--swal2-actions-width: auto;--swal2-actions-margin: 1.25em auto 0;--swal2-actions-padding: 0;--swal2-actions-border-radius: 0;--swal2-actions-background: transparent;--swal2-action-button-transition: background-color 0.2s, box-shadow 0.2s;--swal2-action-button-hover: black 10%;--swal2-action-button-active: black 10%;--swal2-confirm-button-box-shadow: none;--swal2-confirm-button-border-radius: 0.25em;--swal2-confirm-button-background-color: #7066e0;--swal2-confirm-button-color: #fff;--swal2-deny-button-box-shadow: none;--swal2-deny-button-border-radius: 0.25em;--swal2-deny-button-background-color: #dc3741;--swal2-deny-button-color: #fff;--swal2-cancel-button-box-shadow: none;--swal2-cancel-button-border-radius: 0.25em;--swal2-cancel-button-background-color: #6e7881;--swal2-cancel-button-color: #fff;--swal2-toast-show-animation: swal2-toast-show 0.5s;--swal2-toast-hide-animation: swal2-toast-hide 0.1s forwards;--swal2-toast-border: none;--swal2-toast-box-shadow: 0 0 1px hsl(0deg 0% 0% / 0.075), 0 1px 2px hsl(0deg 0% 0% / 0.075), 1px 2px 4px hsl(0deg 0% 0% / 0.075), 1px 3px 8px hsl(0deg 0% 0% / 0.075), 2px 4px 16px hsl(0deg 0% 0% / 0.075)}[data-swal2-theme=dark]{--swal2-dark-theme-black: #19191a;--swal2-dark-theme-white: #e1e1e1;--swal2-background: var(--swal2-dark-theme-black);--swal2-color: var(--swal2-dark-theme-white);--swal2-footer-border-color: #555;--swal2-input-background: color-mix(in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10%);--swal2-validation-message-background: color-mix( in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10% );--swal2-validation-message-color: var(--swal2-dark-theme-white)}@media(prefers-color-scheme: dark){[data-swal2-theme=auto]{--swal2-dark-theme-black: #19191a;--swal2-dark-theme-white: #e1e1e1;--swal2-background: var(--swal2-dark-theme-black);--swal2-color: var(--swal2-dark-theme-white);--swal2-footer-border-color: #555;--swal2-input-background: color-mix(in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10%);--swal2-validation-message-background: color-mix( in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10% );--swal2-validation-message-color: var(--swal2-dark-theme-white)}}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown){overflow:hidden}body.swal2-height-auto{height:auto !important}body.swal2-no-backdrop .swal2-container{background-color:rgba(0,0,0,0) !important;pointer-events:none}body.swal2-no-backdrop .swal2-container .swal2-popup{pointer-events:all}body.swal2-no-backdrop .swal2-container .swal2-modal{box-shadow:0 0 10px var(--swal2-backdrop)}body.swal2-toast-shown .swal2-container{box-sizing:border-box;width:360px;max-width:100%;background-color:rgba(0,0,0,0);pointer-events:none}body.swal2-toast-shown .swal2-container.swal2-top{inset:0 auto auto 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-top-end,body.swal2-toast-shown .swal2-container.swal2-top-right{inset:0 0 auto auto}body.swal2-toast-shown .swal2-container.swal2-top-start,body.swal2-toast-shown .swal2-container.swal2-top-left{inset:0 auto auto 0}body.swal2-toast-shown .swal2-container.swal2-center-start,body.swal2-toast-shown .swal2-container.swal2-center-left{inset:50% auto auto 0;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-center{inset:50% auto auto 50%;transform:translate(-50%, -50%)}body.swal2-toast-shown .swal2-container.swal2-center-end,body.swal2-toast-shown .swal2-container.swal2-center-right{inset:50% 0 auto auto;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-start,body.swal2-toast-shown .swal2-container.swal2-bottom-left{inset:auto auto 0 0}body.swal2-toast-shown .swal2-container.swal2-bottom{inset:auto auto 0 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-end,body.swal2-toast-shown .swal2-container.swal2-bottom-right{inset:auto 0 0 auto}@media print{body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown){overflow-y:scroll !important}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown)>[aria-hidden=true]{display:none}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown) .swal2-container{position:static !important}}div:where(.swal2-container){display:grid;position:fixed;z-index:1060;inset:0;box-sizing:border-box;grid-template-areas:\\\"top-start     top            top-end\\\" \\\"center-start  center         center-end\\\" \\\"bottom-start  bottom-center  bottom-end\\\";grid-template-rows:minmax(min-content, auto) minmax(min-content, auto) minmax(min-content, auto);height:100%;padding:var(--swal2-container-padding);overflow-x:hidden;transition:var(--swal2-backdrop-transition);-webkit-overflow-scrolling:touch}div:where(.swal2-container).swal2-backdrop-show,div:where(.swal2-container).swal2-noanimation{background:var(--swal2-backdrop)}div:where(.swal2-container).swal2-backdrop-hide{background:rgba(0,0,0,0) !important}div:where(.swal2-container).swal2-top-start,div:where(.swal2-container).swal2-center-start,div:where(.swal2-container).swal2-bottom-start{grid-template-columns:minmax(0, 1fr) auto auto}div:where(.swal2-container).swal2-top,div:where(.swal2-container).swal2-center,div:where(.swal2-container).swal2-bottom{grid-template-columns:auto minmax(0, 1fr) auto}div:where(.swal2-container).swal2-top-end,div:where(.swal2-container).swal2-center-end,div:where(.swal2-container).swal2-bottom-end{grid-template-columns:auto auto minmax(0, 1fr)}div:where(.swal2-container).swal2-top-start>.swal2-popup{align-self:start}div:where(.swal2-container).swal2-top>.swal2-popup{grid-column:2;place-self:start center}div:where(.swal2-container).swal2-top-end>.swal2-popup,div:where(.swal2-container).swal2-top-right>.swal2-popup{grid-column:3;place-self:start end}div:where(.swal2-container).swal2-center-start>.swal2-popup,div:where(.swal2-container).swal2-center-left>.swal2-popup{grid-row:2;align-self:center}div:where(.swal2-container).swal2-center>.swal2-popup{grid-column:2;grid-row:2;place-self:center center}div:where(.swal2-container).swal2-center-end>.swal2-popup,div:where(.swal2-container).swal2-center-right>.swal2-popup{grid-column:3;grid-row:2;place-self:center end}div:where(.swal2-container).swal2-bottom-start>.swal2-popup,div:where(.swal2-container).swal2-bottom-left>.swal2-popup{grid-column:1;grid-row:3;align-self:end}div:where(.swal2-container).swal2-bottom>.swal2-popup{grid-column:2;grid-row:3;place-self:end center}div:where(.swal2-container).swal2-bottom-end>.swal2-popup,div:where(.swal2-container).swal2-bottom-right>.swal2-popup{grid-column:3;grid-row:3;place-self:end end}div:where(.swal2-container).swal2-grow-row>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-column:1/4;width:100%}div:where(.swal2-container).swal2-grow-column>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-row:1/4;align-self:stretch}div:where(.swal2-container).swal2-no-transition{transition:none !important}div:where(.swal2-container)[popover]{width:auto;border:0}div:where(.swal2-container) div:where(.swal2-popup){display:none;position:relative;box-sizing:border-box;grid-template-columns:minmax(0, 100%);width:var(--swal2-width);max-width:100%;padding:var(--swal2-padding);border:var(--swal2-border);border-radius:var(--swal2-border-radius);background:var(--swal2-background);color:var(--swal2-color);font-family:inherit;font-size:1rem;container-name:swal2-popup}div:where(.swal2-container) div:where(.swal2-popup):focus{outline:none}div:where(.swal2-container) div:where(.swal2-popup).swal2-loading{overflow-y:hidden}div:where(.swal2-container) div:where(.swal2-popup).swal2-draggable{cursor:grab}div:where(.swal2-container) div:where(.swal2-popup).swal2-draggable div:where(.swal2-icon){cursor:grab}div:where(.swal2-container) div:where(.swal2-popup).swal2-dragging{cursor:grabbing}div:where(.swal2-container) div:where(.swal2-popup).swal2-dragging div:where(.swal2-icon){cursor:grabbing}div:where(.swal2-container) h2:where(.swal2-title){position:relative;max-width:100%;margin:0;padding:var(--swal2-title-padding);color:inherit;font-size:1.875em;font-weight:600;text-align:center;text-transform:none;word-wrap:break-word;cursor:initial}div:where(.swal2-container) div:where(.swal2-actions){display:flex;z-index:1;box-sizing:border-box;flex-wrap:wrap;align-items:center;justify-content:var(--swal2-actions-justify-content);width:var(--swal2-actions-width);margin:var(--swal2-actions-margin);padding:var(--swal2-actions-padding);border-radius:var(--swal2-actions-border-radius);background:var(--swal2-actions-background)}div:where(.swal2-container) div:where(.swal2-loader){display:none;align-items:center;justify-content:center;width:2.2em;height:2.2em;margin:0 1.875em;animation:swal2-rotate-loading 1.5s linear 0s infinite normal;border-width:.25em;border-style:solid;border-radius:100%;border-color:#2778c4 rgba(0,0,0,0) #2778c4 rgba(0,0,0,0)}div:where(.swal2-container) button:where(.swal2-styled){margin:.3125em;padding:.625em 1.1em;transition:var(--swal2-action-button-transition);border:none;box-shadow:0 0 0 3px rgba(0,0,0,0);font-weight:500}div:where(.swal2-container) button:where(.swal2-styled):not([disabled]){cursor:pointer}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm){border-radius:var(--swal2-confirm-button-border-radius);background:initial;background-color:var(--swal2-confirm-button-background-color);box-shadow:var(--swal2-confirm-button-box-shadow);color:var(--swal2-confirm-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm):hover{background-color:color-mix(in srgb, var(--swal2-confirm-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm):active{background-color:color-mix(in srgb, var(--swal2-confirm-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny){border-radius:var(--swal2-deny-button-border-radius);background:initial;background-color:var(--swal2-deny-button-background-color);box-shadow:var(--swal2-deny-button-box-shadow);color:var(--swal2-deny-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny):hover{background-color:color-mix(in srgb, var(--swal2-deny-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny):active{background-color:color-mix(in srgb, var(--swal2-deny-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel){border-radius:var(--swal2-cancel-button-border-radius);background:initial;background-color:var(--swal2-cancel-button-background-color);box-shadow:var(--swal2-cancel-button-box-shadow);color:var(--swal2-cancel-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel):hover{background-color:color-mix(in srgb, var(--swal2-cancel-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel):active{background-color:color-mix(in srgb, var(--swal2-cancel-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):focus-visible{outline:none;box-shadow:var(--swal2-action-button-focus-box-shadow)}div:where(.swal2-container) button:where(.swal2-styled)[disabled]:not(.swal2-loading){opacity:.4}div:where(.swal2-container) button:where(.swal2-styled)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-footer){margin:1em 0 0;padding:1em 1em 0;border-top:1px solid var(--swal2-footer-border-color);background:var(--swal2-footer-background);color:var(--swal2-footer-color);font-size:1em;text-align:center;cursor:initial}div:where(.swal2-container) .swal2-timer-progress-bar-container{position:absolute;right:0;bottom:0;left:0;grid-column:auto !important;overflow:hidden;border-bottom-right-radius:var(--swal2-border-radius);border-bottom-left-radius:var(--swal2-border-radius)}div:where(.swal2-container) div:where(.swal2-timer-progress-bar){width:100%;height:.25em;background:rgba(0,0,0,.2)}div:where(.swal2-container) img:where(.swal2-image){max-width:100%;margin:2em auto 1em;cursor:initial}div:where(.swal2-container) button:where(.swal2-close){position:var(--swal2-close-button-position);inset:var(--swal2-close-button-inset);z-index:2;align-items:center;justify-content:center;width:1.2em;height:1.2em;margin-top:0;margin-right:0;margin-bottom:-1.2em;padding:0;overflow:hidden;transition:var(--swal2-close-button-transition);border:none;border-radius:var(--swal2-border-radius);outline:var(--swal2-close-button-outline);background:rgba(0,0,0,0);color:var(--swal2-close-button-color);font-family:monospace;font-size:var(--swal2-close-button-font-size);cursor:pointer;justify-self:end}div:where(.swal2-container) button:where(.swal2-close):hover{transform:var(--swal2-close-button-hover-transform);background:rgba(0,0,0,0);color:#f27474}div:where(.swal2-container) button:where(.swal2-close):focus-visible{outline:none;box-shadow:var(--swal2-close-button-focus-box-shadow)}div:where(.swal2-container) button:where(.swal2-close)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-html-container){z-index:1;justify-content:center;margin:0;padding:var(--swal2-html-container-padding);overflow:auto;color:inherit;font-size:1.125em;font-weight:normal;line-height:normal;text-align:center;word-wrap:break-word;word-break:break-word;cursor:initial}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea),div:where(.swal2-container) select:where(.swal2-select),div:where(.swal2-container) div:where(.swal2-radio),div:where(.swal2-container) label:where(.swal2-checkbox){margin:1em 2em 3px}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea){box-sizing:border-box;width:auto;transition:var(--swal2-input-transition);border:var(--swal2-input-border);border-radius:var(--swal2-input-border-radius);background:var(--swal2-input-background);box-shadow:var(--swal2-input-box-shadow);color:inherit;font-size:1.125em}div:where(.swal2-container) input:where(.swal2-input).swal2-inputerror,div:where(.swal2-container) input:where(.swal2-file).swal2-inputerror,div:where(.swal2-container) textarea:where(.swal2-textarea).swal2-inputerror{border-color:#f27474 !important;box-shadow:0 0 2px #f27474 !important}div:where(.swal2-container) input:where(.swal2-input):hover,div:where(.swal2-container) input:where(.swal2-file):hover,div:where(.swal2-container) textarea:where(.swal2-textarea):hover{box-shadow:var(--swal2-input-hover-box-shadow)}div:where(.swal2-container) input:where(.swal2-input):focus,div:where(.swal2-container) input:where(.swal2-file):focus,div:where(.swal2-container) textarea:where(.swal2-textarea):focus{border:var(--swal2-input-focus-border);outline:none;box-shadow:var(--swal2-input-focus-box-shadow)}div:where(.swal2-container) input:where(.swal2-input)::placeholder,div:where(.swal2-container) input:where(.swal2-file)::placeholder,div:where(.swal2-container) textarea:where(.swal2-textarea)::placeholder{color:#ccc}div:where(.swal2-container) .swal2-range{margin:1em 2em 3px;background:var(--swal2-background)}div:where(.swal2-container) .swal2-range input{width:80%}div:where(.swal2-container) .swal2-range output{width:20%;color:inherit;font-weight:600;text-align:center}div:where(.swal2-container) .swal2-range input,div:where(.swal2-container) .swal2-range output{height:2.625em;padding:0;font-size:1.125em;line-height:2.625em}div:where(.swal2-container) .swal2-input{height:2.625em;padding:0 .75em}div:where(.swal2-container) .swal2-file{width:75%;margin-right:auto;margin-left:auto;background:var(--swal2-input-background);font-size:1.125em}div:where(.swal2-container) .swal2-textarea{height:6.75em;padding:.75em}div:where(.swal2-container) .swal2-select{min-width:50%;max-width:100%;padding:.375em .625em;background:var(--swal2-input-background);color:inherit;font-size:1.125em}div:where(.swal2-container) .swal2-radio,div:where(.swal2-container) .swal2-checkbox{align-items:center;justify-content:center;background:var(--swal2-background);color:inherit}div:where(.swal2-container) .swal2-radio label,div:where(.swal2-container) .swal2-checkbox label{margin:0 .6em;font-size:1.125em}div:where(.swal2-container) .swal2-radio input,div:where(.swal2-container) .swal2-checkbox input{flex-shrink:0;margin:0 .4em}div:where(.swal2-container) label:where(.swal2-input-label){display:flex;justify-content:center;margin:1em auto 0}div:where(.swal2-container) div:where(.swal2-validation-message){align-items:center;justify-content:center;margin:1em 0 0;padding:.625em;overflow:hidden;background:var(--swal2-validation-message-background);color:var(--swal2-validation-message-color);font-size:1em;font-weight:300}div:where(.swal2-container) div:where(.swal2-validation-message)::before{content:\\\"!\\\";display:inline-block;width:1.5em;min-width:1.5em;height:1.5em;margin:0 .625em;border-radius:50%;background-color:#f27474;color:#fff;font-weight:600;line-height:1.5em;text-align:center}div:where(.swal2-container) .swal2-progress-steps{flex-wrap:wrap;align-items:center;max-width:100%;margin:1.25em auto;padding:0;background:rgba(0,0,0,0);font-weight:600}div:where(.swal2-container) .swal2-progress-steps li{display:inline-block;position:relative}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step{z-index:20;flex-shrink:0;width:2em;height:2em;border-radius:2em;background:#2778c4;color:#fff;line-height:2em;text-align:center}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step{background:#2778c4}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step{background:var(--swal2-progress-step-background);color:#fff}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step-line{background:var(--swal2-progress-step-background)}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step-line{z-index:10;flex-shrink:0;width:2.5em;height:.4em;margin:0 -1px;background:#2778c4}div:where(.swal2-icon){position:relative;box-sizing:content-box;justify-content:center;width:5em;height:5em;margin:2.5em auto .6em;zoom:var(--swal2-icon-zoom);border:.25em solid rgba(0,0,0,0);border-radius:50%;border-color:#000;font-family:inherit;line-height:5em;cursor:default;user-select:none}div:where(.swal2-icon) .swal2-icon-content{display:flex;align-items:center;font-size:3.75em}div:where(.swal2-icon).swal2-error{border-color:#f27474;color:#f27474}div:where(.swal2-icon).swal2-error .swal2-x-mark{position:relative;flex-grow:1}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line]{display:block;position:absolute;top:2.3125em;width:2.9375em;height:.3125em;border-radius:.125em;background-color:#f27474}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=left]{left:1.0625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=right]{right:1em;transform:rotate(-45deg)}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-error.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-error.swal2-icon-show .swal2-x-mark{animation:swal2-animate-error-x-mark .5s}}div:where(.swal2-icon).swal2-warning{border-color:#f8bb86;color:#f8bb86}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-warning.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-warning.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .5s}}div:where(.swal2-icon).swal2-info{border-color:#3fc3ee;color:#3fc3ee}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-info.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-info.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .8s}}div:where(.swal2-icon).swal2-question{border-color:#87adbd;color:#87adbd}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-question.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-question.swal2-icon-show .swal2-icon-content{animation:swal2-animate-question-mark .8s}}div:where(.swal2-icon).swal2-success{border-color:#a5dc86;color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line]{position:absolute;width:3.75em;height:7.5em;border-radius:50%}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.4375em;left:-2.0635em;transform:rotate(-45deg);transform-origin:3.75em 3.75em;border-radius:7.5em 0 0 7.5em}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.6875em;left:1.875em;transform:rotate(-45deg);transform-origin:0 3.75em;border-radius:0 7.5em 7.5em 0}div:where(.swal2-icon).swal2-success .swal2-success-ring{position:absolute;z-index:2;top:-0.25em;left:-0.25em;box-sizing:content-box;width:100%;height:100%;border:.25em solid rgba(165,220,134,.3);border-radius:50%}div:where(.swal2-icon).swal2-success .swal2-success-fix{position:absolute;z-index:1;top:.5em;left:1.625em;width:.4375em;height:5.625em;transform:rotate(-45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line]{display:block;position:absolute;z-index:2;height:.3125em;border-radius:.125em;background-color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=tip]{top:2.875em;left:.8125em;width:1.5625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=long]{top:2.375em;right:.5em;width:2.9375em;transform:rotate(-45deg)}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-animate-success-line-tip .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-animate-success-line-long .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-circular-line-right{animation:swal2-rotate-success-circular-line 4.25s ease-in}}[class^=swal2]{-webkit-tap-highlight-color:rgba(0,0,0,0)}.swal2-show{animation:var(--swal2-show-animation)}.swal2-hide{animation:var(--swal2-hide-animation)}.swal2-noanimation{transition:none}.swal2-scrollbar-measure{position:absolute;top:-9999px;width:50px;height:50px;overflow:scroll}.swal2-rtl .swal2-close{margin-right:initial;margin-left:0}.swal2-rtl .swal2-timer-progress-bar{right:0;left:auto}.swal2-toast{box-sizing:border-box;grid-column:1/4 !important;grid-row:1/4 !important;grid-template-columns:min-content auto min-content;padding:1em;overflow-y:hidden;border:var(--swal2-toast-border);background:var(--swal2-background);box-shadow:var(--swal2-toast-box-shadow);pointer-events:all}.swal2-toast>*{grid-column:2}.swal2-toast h2:where(.swal2-title){margin:.5em 1em;padding:0;font-size:1em;text-align:initial}.swal2-toast .swal2-loading{justify-content:center}.swal2-toast input:where(.swal2-input){height:2em;margin:.5em;font-size:1em}.swal2-toast .swal2-validation-message{font-size:1em}.swal2-toast div:where(.swal2-footer){margin:.5em 0 0;padding:.5em 0 0;font-size:.8em}.swal2-toast button:where(.swal2-close){grid-column:3/3;grid-row:1/99;align-self:center;width:.8em;height:.8em;margin:0;font-size:2em}.swal2-toast div:where(.swal2-html-container){margin:.5em 1em;padding:0;overflow:initial;font-size:1em;text-align:initial}.swal2-toast div:where(.swal2-html-container):empty{padding:0}.swal2-toast .swal2-loader{grid-column:1;grid-row:1/99;align-self:center;width:2em;height:2em;margin:.25em}.swal2-toast .swal2-icon{grid-column:1;grid-row:1/99;align-self:center;width:2em;min-width:2em;height:2em;margin:0 .5em 0 0}.swal2-toast .swal2-icon .swal2-icon-content{display:flex;align-items:center;font-size:1.8em;font-weight:bold}.swal2-toast .swal2-icon.swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line]{top:.875em;width:1.375em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left]{left:.3125em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right]{right:.3125em}.swal2-toast div:where(.swal2-actions){justify-content:flex-start;height:auto;margin:0;margin-top:.5em;padding:0 .5em}.swal2-toast button:where(.swal2-styled){margin:.25em .5em;padding:.4em .6em;font-size:1em}.swal2-toast .swal2-success{border-color:#a5dc86}.swal2-toast .swal2-success [class^=swal2-success-circular-line]{position:absolute;width:1.6em;height:3em;border-radius:50%}.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.8em;left:-0.5em;transform:rotate(-45deg);transform-origin:2em 2em;border-radius:4em 0 0 4em}.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.25em;left:.9375em;transform-origin:0 1.5em;border-radius:0 4em 4em 0}.swal2-toast .swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-toast .swal2-success .swal2-success-fix{top:0;left:.4375em;width:.4375em;height:2.6875em}.swal2-toast .swal2-success [class^=swal2-success-line]{height:.3125em}.swal2-toast .swal2-success [class^=swal2-success-line][class$=tip]{top:1.125em;left:.1875em;width:.75em}.swal2-toast .swal2-success [class^=swal2-success-line][class$=long]{top:.9375em;right:.1875em;width:1.375em}@container swal2-popup style(--swal2-icon-animations:true){.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-toast-animate-success-line-tip .75s}.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-toast-animate-success-line-long .75s}}.swal2-toast.swal2-show{animation:var(--swal2-toast-show-animation)}.swal2-toast.swal2-hide{animation:var(--swal2-toast-hide-animation)}@keyframes swal2-show{0%{transform:scale(0.7)}45%{transform:scale(1.05)}80%{transform:scale(0.95)}100%{transform:scale(1)}}@keyframes swal2-hide{0%{transform:scale(1);opacity:1}100%{transform:scale(0.5);opacity:0}}@keyframes swal2-animate-success-line-tip{0%{top:1.1875em;left:.0625em;width:0}54%{top:1.0625em;left:.125em;width:0}70%{top:2.1875em;left:-0.375em;width:3.125em}84%{top:3em;left:1.3125em;width:1.0625em}100%{top:2.8125em;left:.8125em;width:1.5625em}}@keyframes swal2-animate-success-line-long{0%{top:3.375em;right:2.875em;width:0}65%{top:3.375em;right:2.875em;width:0}84%{top:2.1875em;right:0;width:3.4375em}100%{top:2.375em;right:.5em;width:2.9375em}}@keyframes swal2-rotate-success-circular-line{0%{transform:rotate(-45deg)}5%{transform:rotate(-45deg)}12%{transform:rotate(-405deg)}100%{transform:rotate(-405deg)}}@keyframes swal2-animate-error-x-mark{0%{margin-top:1.625em;transform:scale(0.4);opacity:0}50%{margin-top:1.625em;transform:scale(0.4);opacity:0}80%{margin-top:-0.375em;transform:scale(1.15)}100%{margin-top:0;transform:scale(1);opacity:1}}@keyframes swal2-animate-error-icon{0%{transform:rotateX(100deg);opacity:0}100%{transform:rotateX(0deg);opacity:1}}@keyframes swal2-rotate-loading{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes swal2-animate-question-mark{0%{transform:rotateY(-360deg)}100%{transform:rotateY(0)}}@keyframes swal2-animate-i-mark{0%{transform:rotateZ(45deg);opacity:0}25%{transform:rotateZ(-25deg);opacity:.4}50%{transform:rotateZ(15deg);opacity:.8}75%{transform:rotateZ(-5deg);opacity:1}100%{transform:rotateX(0);opacity:1}}@keyframes swal2-toast-show{0%{transform:translateY(-0.625em) rotateZ(2deg)}33%{transform:translateY(0) rotateZ(-2deg)}66%{transform:translateY(0.3125em) rotateZ(2deg)}100%{transform:translateY(0) rotateZ(0deg)}}@keyframes swal2-toast-hide{100%{transform:rotateZ(1deg);opacity:0}}@keyframes swal2-toast-animate-success-line-tip{0%{top:.5625em;left:.0625em;width:0}54%{top:.125em;left:.125em;width:0}70%{top:.625em;left:-0.25em;width:1.625em}84%{top:1.0625em;left:.75em;width:.5em}100%{top:1.125em;left:.1875em;width:.75em}}@keyframes swal2-toast-animate-success-line-long{0%{top:1.625em;right:1.375em;width:0}65%{top:1.25em;right:.9375em;width:0}84%{top:.9375em;right:0;width:1.125em}100%{top:.9375em;right:.1875em;width:1.375em}}\");", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "define", "amd", "globalThis", "self", "Sweetalert2", "_assert<PERSON>lassBrand", "e", "t", "n", "has", "arguments", "length", "TypeError", "_checkPrivateRedeclaration", "_classPrivateFieldGet2", "s", "a", "get", "_classPrivateFieldInitSpec", "set", "_classPrivateFieldSet2", "r", "RESTORE_FOCUS_TIMEOUT", "globalState", "focusPreviousActiveElement", "previousActiveElement", "HTMLElement", "focus", "document", "body", "restoreActiveElement", "returnFocus", "Promise", "resolve", "x", "window", "scrollX", "y", "scrollY", "restoreFocusTimeout", "setTimeout", "scrollTo", "swalPrefix", "classNames", "swalClasses", "reduce", "acc", "className", "icons", "iconTypes", "icon", "consolePrefix", "capitalizeFirstLetter", "str", "char<PERSON>t", "toUpperCase", "slice", "warn", "message", "console", "join", "error", "previousWarnOnceMessages", "warnOnce", "includes", "push", "warnAboutDeprecation", "deprecatedParam", "useInstead", "callIfFunction", "arg", "hasToPromiseFn", "to<PERSON>romise", "<PERSON><PERSON><PERSON><PERSON>", "isPromise", "getContainer", "querySelector", "container", "elementBySelector", "selectorString", "elementByClass", "getPopup", "popup", "getIcon", "getIconContent", "getTitle", "title", "getHtmlContainer", "getImage", "image", "getProgressSteps", "getValidationMessage", "getConfirmButton", "actions", "confirm", "getCancelButton", "cancel", "getDenyButton", "deny", "getInputLabel", "<PERSON><PERSON><PERSON><PERSON>", "loader", "getActions", "getFooter", "footer", "getTimerProgressBar", "getCloseButton", "close", "focusable", "getFocusableElements", "focusableElementsWithTabindex", "querySelectorAll", "focusableElementsWithTabindexSorted", "Array", "from", "sort", "b", "tabindexA", "parseInt", "getAttribute", "tabindexB", "otherFocusableElements", "otherFocusableElementsFiltered", "filter", "el", "Set", "concat", "isVisible$1", "isModal", "hasClass", "shown", "isToast", "toast", "isLoading", "hasAttribute", "setInnerHtml", "elem", "html", "textContent", "parser", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parsed", "parseFromString", "head", "childNodes", "for<PERSON>ach", "child", "append<PERSON><PERSON><PERSON>", "HTMLVideoElement", "HTMLAudioElement", "cloneNode", "classList", "split", "i", "contains", "removeCustomClasses", "params", "Object", "values", "showClass", "remove", "applyCustomClass", "customClass", "addClass", "getInput$1", "inputClass", "checkbox", "radio", "range", "input", "focusInput", "type", "val", "value", "toggleClass", "target", "condition", "Boolean", "isArray", "add", "removeClass", "getDirectChildByClass", "children", "applyNumericalStyle", "property", "style", "setProperty", "removeProperty", "show", "display", "hide", "showWhenInnerHtmlPresent", "MutationObserver", "toggle", "innerHTML", "observe", "childList", "subtree", "setStyle", "parent", "selector", "offsetWidth", "offsetHeight", "getClientRects", "allButtonsAreHidden", "isScrollable", "scrollHeight", "clientHeight", "selfOrParentIsScrollable", "element", "stopElement", "parentElement", "hasCssAnimation", "getComputedStyle", "animDuration", "parseFloat", "getPropertyValue", "transDuration", "animateTimerProgressBar", "timer", "reset", "timerP<PERSON>ressBar", "transition", "width", "stopTimerProgressBar", "timer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "timer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "timerProgressBarPercent", "isNodeEnv", "sweetHTML", "file", "select", "label", "textarea", "replace", "resetOldContainer", "oldContainer", "documentElement", "resetValidationMessage$1", "currentInstance", "resetValidationMessage", "addInputChangeListeners", "rangeOutput", "oninput", "onchange", "get<PERSON><PERSON><PERSON>", "setupAccessibility", "setAttribute", "setupRTL", "targetElement", "direction", "rtl", "init", "oldContainerExisted", "createElement", "dataset", "theme", "topLayer", "showPopover", "parseHtmlToContainer", "param", "handleObject", "j<PERSON>y", "handleJqueryElem", "toString", "renderActions", "instance", "showConfirmButton", "showDenyButton", "showCancelButton", "renderButtons", "loaderHtml", "confirmButton", "denyButton", "cancelButton", "renderButton", "handleButtonsStyling", "reverseButtons", "insertBefore", "buttonsStyling", "styled", "confirmButtonColor", "denyButtonColor", "cancelButtonColor", "applyOutlineColor", "button", "buttonStyle", "outlineColor", "backgroundColor", "buttonType", "buttonName", "renderCloseButton", "closeButton", "closeButtonHtml", "showCloseButton", "closeButtonAriaLabel", "renderContainer", "handleBackdropParam", "backdrop", "handlePositionParam", "position", "handleGrowParam", "grow", "background", "center", "privateProps", "innerParams", "WeakMap", "<PERSON><PERSON><PERSON><PERSON>", "inputClasses", "renderInput", "rerender", "inputContainer", "setAttributes", "inputAttributes", "showInput", "setCustomClass", "renderInputType", "keys", "getInputContainer", "inputAutoFocus", "removeAttributes", "attributes", "attrName", "name", "removeAttribute", "attr", "setInputPlaceholder", "placeholder", "inputPlaceholder", "setInputLabel", "prependTo", "inputLabel", "labelClass", "id", "innerText", "insertAdjacentElement", "inputType", "checkAndSetInputValue", "inputValue", "text", "email", "password", "number", "tel", "url", "search", "date", "time", "week", "month", "rangeInput", "disabled", "selected", "checkboxContainer", "checked", "<PERSON><PERSON><PERSON><PERSON>", "marginLeft", "marginRight", "initialPopupWidth", "textareaResizeHandler", "textarea<PERSON>idth", "attributeFilter", "renderContent", "htmlContainer", "renderFooter", "renderIcon", "<PERSON><PERSON><PERSON><PERSON>", "applyStyles", "iconHtml", "indexOf", "colorSchemeQueryList", "matchMedia", "addEventListener", "adjustSuccessIconBackgroundColor", "iconType", "iconClassName", "entries", "setColor", "popupBackgroundColor", "successIconParts", "successIconHtml", "errorIconHtml", "<PERSON><PERSON><PERSON><PERSON>", "newContent", "iconContent", "defaultIconHtml", "question", "warning", "info", "trim", "iconColor", "color", "borderColor", "sel", "content", "renderImage", "imageUrl", "imageAlt", "imageWidth", "imageHeight", "dragging", "mousedownX", "mousedownY", "initialX", "initialY", "addDraggableListeners", "down", "move", "up", "removeDraggableListeners", "removeEventListener", "event", "clientXY", "getClientXY", "clientX", "clientY", "insetInlineStart", "insetBlockStart", "startsWith", "touches", "renderPopup", "padding", "addClasses$1", "draggable", "modal", "renderProgressSteps", "progressStepsContainer", "progressSteps", "currentProgressStep", "undefined", "step", "index", "stepEl", "createStepElement", "lineEl", "createLineElement", "progressStepsDistance", "renderTitle", "titleText", "render", "<PERSON><PERSON><PERSON>", "eventEmitter", "emit", "isVisible", "clickConfirm", "_dom$getConfirmButton", "click", "clickDeny", "_dom$getDenyButton", "clickCancel", "_dom$getCancelButton", "DismissReason", "freeze", "esc", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "keydownTarget", "keydownHandlerAdded", "keydownHandler", "capture", "keydownListenerCapture", "add<PERSON><PERSON>downHandler", "dismissWith", "setFocus", "increment", "_dom$getPopup", "focusableElements", "arrowKeysNextButton", "arrowKeysPreviousButton", "isComposing", "keyCode", "stopKeydownPropagation", "stopPropagation", "key", "handleEnter", "handleTab", "handleArrows", "handleEsc", "allowEnterKey", "outerHTML", "preventDefault", "btnIndex", "shift<PERSON>ey", "buttons", "activeElement", "sibling", "buttonToFocus", "HTMLButtonElement", "allowEscapeKey", "privateMethods", "swalPromiseResolve", "swalPromiseReject", "setAriaHidden", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unsetAriaH<PERSON>den", "isSafariOrIOS", "GestureEvent", "iOSfix", "iosfix", "offset", "scrollTop", "top", "lockBodyScroll", "preventTouchMove", "ontouchstart", "shouldPreventTouchMove", "ontouchmove", "isStylus", "isZoom", "tagName", "touchType", "undoIOSfix", "measureScrollbar", "scrollDiv", "scrollbarWidth", "getBoundingClientRect", "clientWidth", "<PERSON><PERSON><PERSON><PERSON>", "previousBodyPadding", "replaceScrollbarWithPadding", "initialBodyOverflow", "innerHeight", "paddingRight", "undoReplaceScrollbarWithPadding", "removePopupAndResetState", "didClose", "triggerDidCloseAndDispose", "then", "removeBodyClasses", "resolveValue", "prepareResolveValue", "triggerClosePopup", "isAwaitingPromise", "isDismissed", "handleAwaitingPromise", "hideClass", "handlePopupAnimation", "rejectPromise", "_destroy", "isConfirmed", "isDenied", "assign", "_globalState$eventEmi", "animationIsSupported", "willClose", "animatePopup", "swalCloseEventFinishedCallback", "bind", "swalCloseAnimationFinished", "_globalState$swalClos", "call", "_globalState$eventEmi2", "showLoading", "buttonToReplace", "<PERSON><PERSON>", "replaceButton", "loading", "handleInputOptionsAndValue", "handleInputOptions", "some", "handleInputValue", "getInputValue", "getInput", "getCheckboxValue", "getRadioValue", "getFileValue", "inputAutoTrim", "files", "processInputOptions", "inputOptions", "populateSelectOptions", "formatInputOptions", "populateRadioOptions", "hideLoading", "catch", "err", "renderOption", "optionLabel", "optionValue", "option", "isSelected", "inputOption", "optgroup", "o", "radioValue", "radioLabel", "radioInput", "radioLabelElement", "radios", "result", "Map", "valueFormatted", "handleConfirmButtonClick", "disableButtons", "handleConfirmOrDenyWithInput", "handleDenyButtonClick", "returnInputValueOnDeny", "handleCancelButtonClick", "inputValidator", "handleInputValidator", "checkValidity", "enableButtons", "showValidationMessage", "validationMessage", "disableInput", "validationPromise", "enableInput", "showLoaderOnDeny", "preDeny", "preDenyPromise", "preDenyValue", "rejectWith", "<PERSON><PERSON><PERSON>", "showLoaderOnConfirm", "preConfirm", "preConfirmPromise", "preConfirmValue", "showRelatedButton", "getElementsByClassName", "setButtonsDisabled", "setInputDisabled", "inputerror", "defaultParams", "template", "animation", "heightAuto", "allowOutsideClick", "confirmButtonText", "confirmButtonAriaLabel", "denyButtonText", "denyButtonAriaLabel", "cancelButtonText", "cancelButtonAriaLabel", "focusConfirm", "focusDeny", "focusCancel", "<PERSON><PERSON><PERSON>", "did<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "scrollbarPadding", "updatableParams", "deprecatedParams", "toastIncompatibleParams", "isValidParameter", "paramName", "prototype", "hasOwnProperty", "isUpdatableParameter", "isDeprecatedParameter", "checkIfParamIsValid", "checkIfToastParamIsValid", "checkIfParamIsDeprecated", "isDeprecated", "showWarningsForParams", "update", "validUpdatableParams", "filterValidParams", "updatedParams", "defineProperties", "writable", "enumerable", "disposeWeakMaps", "dispose<PERSON>wal", "unsetWeakMaps", "disableLoading", "closePopup", "closeModal", "closeToast", "obj", "delete", "instanceMethods", "__proto__", "handlePopupClick", "handleToastClick", "handleModalMousedown", "handleContainerMousedown", "handleModalClick", "onclick", "isAnyButtonShown", "ignoreOutsideClick", "onmousedown", "onmouseup", "isJqueryElement", "isElement", "Element", "argsToParams", "args", "fire", "mixin", "mixinParams", "MixinSwal", "_main", "priorityMixinParams", "getTimerLeft", "timeout", "stopTimer", "stop", "resumeTimer", "remaining", "start", "toggleTimer", "running", "increaseTimer", "ms", "increase", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isRunning", "bodyClickListenerAdded", "clickHandlers", "bindClickHandler", "bodyClickListener", "parentNode", "EventEmitter", "constructor", "events", "_getHandlersByEventName", "eventName", "on", "<PERSON><PERSON><PERSON><PERSON>", "currentHandlers", "once", "onceFn", "removeListener", "apply", "splice", "removeAllListeners", "off", "staticMethods", "enableLoading", "Timer", "callback", "delay", "started", "Date", "clearTimeout", "getTime", "swalStringParams", "getTemplateParams", "templateContent", "showWarningsForElements", "getSwalParams", "getSwalFunctionParams", "getSwalButtons", "getSwalImage", "getSwalIcon", "getSwalInput", "getSwalStringParams", "swalParams", "showWarningsForAttributes", "JSON", "parse", "swalFunctions", "Function", "swalButtons", "optionName", "paramNames", "tag", "allowedElements", "toLowerCase", "allowedAttributes", "attribute", "SHOW_CLASS_TIMEOUT", "openPopup", "bodyStyles", "overflowY", "addClasses", "setScrollingVisibility", "fixScrollContainer", "swalOpenAnimationFinished", "defaultInputValidators", "string", "test", "setDefaultInputValidators", "validateCustomTargetElement", "setParameters", "_promise", "<PERSON><PERSON><PERSON><PERSON>", "outerParams", "userParams", "prepareParams", "populateDomCache", "swalP<PERSON><PERSON>", "onFulfilled", "finally", "onFinally", "reject", "dismiss", "setupTimer", "initFocus", "templateParams", "blurActiveElement", "focusAutofocus", "focusButton", "autofocusElements", "autofocusElement", "blur", "navigator", "language", "location", "host", "match", "now", "initiationDate", "localStorage", "getItem", "setItem", "pointerEvents", "ukrainianAnthem", "src", "loop", "play", "version", "default", "swal", "<PERSON><PERSON><PERSON><PERSON>", "getElementsByTagName", "styleSheet", "cssText"], "sources": ["C:/Users/<USER>/Documents/augment-projects/coop/frontend/node_modules/sweetalert2/dist/sweetalert2.all.js"], "sourcesContent": ["/*!\n* sweetalert2 v11.22.0\n* Released under the MIT License.\n*/\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :\n  typeof define === 'function' && define.amd ? define(factory) :\n  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global.Sweetalert2 = factory());\n})(this, (function () { 'use strict';\n\n  function _assertClassBrand(e, t, n) {\n    if (\"function\" == typeof e ? e === t : e.has(t)) return arguments.length < 3 ? t : n;\n    throw new TypeError(\"Private element is not present on this object\");\n  }\n  function _checkPrivateRedeclaration(e, t) {\n    if (t.has(e)) throw new TypeError(\"Cannot initialize the same private elements twice on an object\");\n  }\n  function _classPrivateFieldGet2(s, a) {\n    return s.get(_assertClassBrand(s, a));\n  }\n  function _classPrivateFieldInitSpec(e, t, a) {\n    _checkPrivateRedeclaration(e, t), t.set(e, a);\n  }\n  function _classPrivateFieldSet2(s, a, r) {\n    return s.set(_assertClassBrand(s, a), r), r;\n  }\n\n  const RESTORE_FOCUS_TIMEOUT = 100;\n\n  /** @type {GlobalState} */\n  const globalState = {};\n  const focusPreviousActiveElement = () => {\n    if (globalState.previousActiveElement instanceof HTMLElement) {\n      globalState.previousActiveElement.focus();\n      globalState.previousActiveElement = null;\n    } else if (document.body) {\n      document.body.focus();\n    }\n  };\n\n  /**\n   * Restore previous active (focused) element\n   *\n   * @param {boolean} returnFocus\n   * @returns {Promise<void>}\n   */\n  const restoreActiveElement = returnFocus => {\n    return new Promise(resolve => {\n      if (!returnFocus) {\n        return resolve();\n      }\n      const x = window.scrollX;\n      const y = window.scrollY;\n      globalState.restoreFocusTimeout = setTimeout(() => {\n        focusPreviousActiveElement();\n        resolve();\n      }, RESTORE_FOCUS_TIMEOUT); // issues/900\n\n      window.scrollTo(x, y);\n    });\n  };\n\n  const swalPrefix = 'swal2-';\n\n  /**\n   * @typedef {Record<SwalClass, string>} SwalClasses\n   */\n\n  /**\n   * @typedef {'success' | 'warning' | 'info' | 'question' | 'error'} SwalIcon\n   * @typedef {Record<SwalIcon, string>} SwalIcons\n   */\n\n  /** @type {SwalClass[]} */\n  const classNames = ['container', 'shown', 'height-auto', 'iosfix', 'popup', 'modal', 'no-backdrop', 'no-transition', 'toast', 'toast-shown', 'show', 'hide', 'close', 'title', 'html-container', 'actions', 'confirm', 'deny', 'cancel', 'footer', 'icon', 'icon-content', 'image', 'input', 'file', 'range', 'select', 'radio', 'checkbox', 'label', 'textarea', 'inputerror', 'input-label', 'validation-message', 'progress-steps', 'active-progress-step', 'progress-step', 'progress-step-line', 'loader', 'loading', 'styled', 'top', 'top-start', 'top-end', 'top-left', 'top-right', 'center', 'center-start', 'center-end', 'center-left', 'center-right', 'bottom', 'bottom-start', 'bottom-end', 'bottom-left', 'bottom-right', 'grow-row', 'grow-column', 'grow-fullscreen', 'rtl', 'timer-progress-bar', 'timer-progress-bar-container', 'scrollbar-measure', 'icon-success', 'icon-warning', 'icon-info', 'icon-question', 'icon-error', 'draggable', 'dragging'];\n  const swalClasses = classNames.reduce((acc, className) => {\n    acc[className] = swalPrefix + className;\n    return acc;\n  }, /** @type {SwalClasses} */{});\n\n  /** @type {SwalIcon[]} */\n  const icons = ['success', 'warning', 'info', 'question', 'error'];\n  const iconTypes = icons.reduce((acc, icon) => {\n    acc[icon] = swalPrefix + icon;\n    return acc;\n  }, /** @type {SwalIcons} */{});\n\n  const consolePrefix = 'SweetAlert2:';\n\n  /**\n   * Capitalize the first letter of a string\n   *\n   * @param {string} str\n   * @returns {string}\n   */\n  const capitalizeFirstLetter = str => str.charAt(0).toUpperCase() + str.slice(1);\n\n  /**\n   * Standardize console warnings\n   *\n   * @param {string | string[]} message\n   */\n  const warn = message => {\n    console.warn(`${consolePrefix} ${typeof message === 'object' ? message.join(' ') : message}`);\n  };\n\n  /**\n   * Standardize console errors\n   *\n   * @param {string} message\n   */\n  const error = message => {\n    console.error(`${consolePrefix} ${message}`);\n  };\n\n  /**\n   * Private global state for `warnOnce`\n   *\n   * @type {string[]}\n   * @private\n   */\n  const previousWarnOnceMessages = [];\n\n  /**\n   * Show a console warning, but only if it hasn't already been shown\n   *\n   * @param {string} message\n   */\n  const warnOnce = message => {\n    if (!previousWarnOnceMessages.includes(message)) {\n      previousWarnOnceMessages.push(message);\n      warn(message);\n    }\n  };\n\n  /**\n   * Show a one-time console warning about deprecated params/methods\n   *\n   * @param {string} deprecatedParam\n   * @param {string?} useInstead\n   */\n  const warnAboutDeprecation = (deprecatedParam, useInstead = null) => {\n    warnOnce(`\"${deprecatedParam}\" is deprecated and will be removed in the next major release.${useInstead ? ` Use \"${useInstead}\" instead.` : ''}`);\n  };\n\n  /**\n   * If `arg` is a function, call it (with no arguments or context) and return the result.\n   * Otherwise, just pass the value through\n   *\n   * @param {Function | any} arg\n   * @returns {any}\n   */\n  const callIfFunction = arg => typeof arg === 'function' ? arg() : arg;\n\n  /**\n   * @param {any} arg\n   * @returns {boolean}\n   */\n  const hasToPromiseFn = arg => arg && typeof arg.toPromise === 'function';\n\n  /**\n   * @param {any} arg\n   * @returns {Promise<any>}\n   */\n  const asPromise = arg => hasToPromiseFn(arg) ? arg.toPromise() : Promise.resolve(arg);\n\n  /**\n   * @param {any} arg\n   * @returns {boolean}\n   */\n  const isPromise = arg => arg && Promise.resolve(arg) === arg;\n\n  /**\n   * Gets the popup container which contains the backdrop and the popup itself.\n   *\n   * @returns {HTMLElement | null}\n   */\n  const getContainer = () => document.body.querySelector(`.${swalClasses.container}`);\n\n  /**\n   * @param {string} selectorString\n   * @returns {HTMLElement | null}\n   */\n  const elementBySelector = selectorString => {\n    const container = getContainer();\n    return container ? container.querySelector(selectorString) : null;\n  };\n\n  /**\n   * @param {string} className\n   * @returns {HTMLElement | null}\n   */\n  const elementByClass = className => {\n    return elementBySelector(`.${className}`);\n  };\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getPopup = () => elementByClass(swalClasses.popup);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getIcon = () => elementByClass(swalClasses.icon);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getIconContent = () => elementByClass(swalClasses['icon-content']);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getTitle = () => elementByClass(swalClasses.title);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getHtmlContainer = () => elementByClass(swalClasses['html-container']);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getImage = () => elementByClass(swalClasses.image);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getProgressSteps = () => elementByClass(swalClasses['progress-steps']);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getValidationMessage = () => elementByClass(swalClasses['validation-message']);\n\n  /**\n   * @returns {HTMLButtonElement | null}\n   */\n  const getConfirmButton = () => (/** @type {HTMLButtonElement} */elementBySelector(`.${swalClasses.actions} .${swalClasses.confirm}`));\n\n  /**\n   * @returns {HTMLButtonElement | null}\n   */\n  const getCancelButton = () => (/** @type {HTMLButtonElement} */elementBySelector(`.${swalClasses.actions} .${swalClasses.cancel}`));\n\n  /**\n   * @returns {HTMLButtonElement | null}\n   */\n  const getDenyButton = () => (/** @type {HTMLButtonElement} */elementBySelector(`.${swalClasses.actions} .${swalClasses.deny}`));\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getInputLabel = () => elementByClass(swalClasses['input-label']);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getLoader = () => elementBySelector(`.${swalClasses.loader}`);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getActions = () => elementByClass(swalClasses.actions);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getFooter = () => elementByClass(swalClasses.footer);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getTimerProgressBar = () => elementByClass(swalClasses['timer-progress-bar']);\n\n  /**\n   * @returns {HTMLElement | null}\n   */\n  const getCloseButton = () => elementByClass(swalClasses.close);\n\n  // https://github.com/jkup/focusable/blob/master/index.js\n  const focusable = `\n  a[href],\n  area[href],\n  input:not([disabled]),\n  select:not([disabled]),\n  textarea:not([disabled]),\n  button:not([disabled]),\n  iframe,\n  object,\n  embed,\n  [tabindex=\"0\"],\n  [contenteditable],\n  audio[controls],\n  video[controls],\n  summary\n`;\n  /**\n   * @returns {HTMLElement[]}\n   */\n  const getFocusableElements = () => {\n    const popup = getPopup();\n    if (!popup) {\n      return [];\n    }\n    /** @type {NodeListOf<HTMLElement>} */\n    const focusableElementsWithTabindex = popup.querySelectorAll('[tabindex]:not([tabindex=\"-1\"]):not([tabindex=\"0\"])');\n    const focusableElementsWithTabindexSorted = Array.from(focusableElementsWithTabindex)\n    // sort according to tabindex\n    .sort((a, b) => {\n      const tabindexA = parseInt(a.getAttribute('tabindex') || '0');\n      const tabindexB = parseInt(b.getAttribute('tabindex') || '0');\n      if (tabindexA > tabindexB) {\n        return 1;\n      } else if (tabindexA < tabindexB) {\n        return -1;\n      }\n      return 0;\n    });\n\n    /** @type {NodeListOf<HTMLElement>} */\n    const otherFocusableElements = popup.querySelectorAll(focusable);\n    const otherFocusableElementsFiltered = Array.from(otherFocusableElements).filter(el => el.getAttribute('tabindex') !== '-1');\n    return [...new Set(focusableElementsWithTabindexSorted.concat(otherFocusableElementsFiltered))].filter(el => isVisible$1(el));\n  };\n\n  /**\n   * @returns {boolean}\n   */\n  const isModal = () => {\n    return hasClass(document.body, swalClasses.shown) && !hasClass(document.body, swalClasses['toast-shown']) && !hasClass(document.body, swalClasses['no-backdrop']);\n  };\n\n  /**\n   * @returns {boolean}\n   */\n  const isToast = () => {\n    const popup = getPopup();\n    if (!popup) {\n      return false;\n    }\n    return hasClass(popup, swalClasses.toast);\n  };\n\n  /**\n   * @returns {boolean}\n   */\n  const isLoading = () => {\n    const popup = getPopup();\n    if (!popup) {\n      return false;\n    }\n    return popup.hasAttribute('data-loading');\n  };\n\n  /**\n   * Securely set innerHTML of an element\n   * https://github.com/sweetalert2/sweetalert2/issues/1926\n   *\n   * @param {HTMLElement} elem\n   * @param {string} html\n   */\n  const setInnerHtml = (elem, html) => {\n    elem.textContent = '';\n    if (html) {\n      const parser = new DOMParser();\n      const parsed = parser.parseFromString(html, `text/html`);\n      const head = parsed.querySelector('head');\n      if (head) {\n        Array.from(head.childNodes).forEach(child => {\n          elem.appendChild(child);\n        });\n      }\n      const body = parsed.querySelector('body');\n      if (body) {\n        Array.from(body.childNodes).forEach(child => {\n          if (child instanceof HTMLVideoElement || child instanceof HTMLAudioElement) {\n            elem.appendChild(child.cloneNode(true)); // https://github.com/sweetalert2/sweetalert2/issues/2507\n          } else {\n            elem.appendChild(child);\n          }\n        });\n      }\n    }\n  };\n\n  /**\n   * @param {HTMLElement} elem\n   * @param {string} className\n   * @returns {boolean}\n   */\n  const hasClass = (elem, className) => {\n    if (!className) {\n      return false;\n    }\n    const classList = className.split(/\\s+/);\n    for (let i = 0; i < classList.length; i++) {\n      if (!elem.classList.contains(classList[i])) {\n        return false;\n      }\n    }\n    return true;\n  };\n\n  /**\n   * @param {HTMLElement} elem\n   * @param {SweetAlertOptions} params\n   */\n  const removeCustomClasses = (elem, params) => {\n    Array.from(elem.classList).forEach(className => {\n      if (!Object.values(swalClasses).includes(className) && !Object.values(iconTypes).includes(className) && !Object.values(params.showClass || {}).includes(className)) {\n        elem.classList.remove(className);\n      }\n    });\n  };\n\n  /**\n   * @param {HTMLElement} elem\n   * @param {SweetAlertOptions} params\n   * @param {string} className\n   */\n  const applyCustomClass = (elem, params, className) => {\n    removeCustomClasses(elem, params);\n    if (!params.customClass) {\n      return;\n    }\n    const customClass = params.customClass[(/** @type {keyof SweetAlertCustomClass} */className)];\n    if (!customClass) {\n      return;\n    }\n    if (typeof customClass !== 'string' && !customClass.forEach) {\n      warn(`Invalid type of customClass.${className}! Expected string or iterable object, got \"${typeof customClass}\"`);\n      return;\n    }\n    addClass(elem, customClass);\n  };\n\n  /**\n   * @param {HTMLElement} popup\n   * @param {import('./renderers/renderInput').InputClass | SweetAlertInput} inputClass\n   * @returns {HTMLInputElement | null}\n   */\n  const getInput$1 = (popup, inputClass) => {\n    if (!inputClass) {\n      return null;\n    }\n    switch (inputClass) {\n      case 'select':\n      case 'textarea':\n      case 'file':\n        return popup.querySelector(`.${swalClasses.popup} > .${swalClasses[inputClass]}`);\n      case 'checkbox':\n        return popup.querySelector(`.${swalClasses.popup} > .${swalClasses.checkbox} input`);\n      case 'radio':\n        return popup.querySelector(`.${swalClasses.popup} > .${swalClasses.radio} input:checked`) || popup.querySelector(`.${swalClasses.popup} > .${swalClasses.radio} input:first-child`);\n      case 'range':\n        return popup.querySelector(`.${swalClasses.popup} > .${swalClasses.range} input`);\n      default:\n        return popup.querySelector(`.${swalClasses.popup} > .${swalClasses.input}`);\n    }\n  };\n\n  /**\n   * @param {HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement} input\n   */\n  const focusInput = input => {\n    input.focus();\n\n    // place cursor at end of text in text input\n    if (input.type !== 'file') {\n      // http://stackoverflow.com/a/2345915\n      const val = input.value;\n      input.value = '';\n      input.value = val;\n    }\n  };\n\n  /**\n   * @param {HTMLElement | HTMLElement[] | null} target\n   * @param {string | string[] | readonly string[] | undefined} classList\n   * @param {boolean} condition\n   */\n  const toggleClass = (target, classList, condition) => {\n    if (!target || !classList) {\n      return;\n    }\n    if (typeof classList === 'string') {\n      classList = classList.split(/\\s+/).filter(Boolean);\n    }\n    classList.forEach(className => {\n      if (Array.isArray(target)) {\n        target.forEach(elem => {\n          if (condition) {\n            elem.classList.add(className);\n          } else {\n            elem.classList.remove(className);\n          }\n        });\n      } else {\n        if (condition) {\n          target.classList.add(className);\n        } else {\n          target.classList.remove(className);\n        }\n      }\n    });\n  };\n\n  /**\n   * @param {HTMLElement | HTMLElement[] | null} target\n   * @param {string | string[] | readonly string[] | undefined} classList\n   */\n  const addClass = (target, classList) => {\n    toggleClass(target, classList, true);\n  };\n\n  /**\n   * @param {HTMLElement | HTMLElement[] | null} target\n   * @param {string | string[] | readonly string[] | undefined} classList\n   */\n  const removeClass = (target, classList) => {\n    toggleClass(target, classList, false);\n  };\n\n  /**\n   * Get direct child of an element by class name\n   *\n   * @param {HTMLElement} elem\n   * @param {string} className\n   * @returns {HTMLElement | undefined}\n   */\n  const getDirectChildByClass = (elem, className) => {\n    const children = Array.from(elem.children);\n    for (let i = 0; i < children.length; i++) {\n      const child = children[i];\n      if (child instanceof HTMLElement && hasClass(child, className)) {\n        return child;\n      }\n    }\n  };\n\n  /**\n   * @param {HTMLElement} elem\n   * @param {string} property\n   * @param {*} value\n   */\n  const applyNumericalStyle = (elem, property, value) => {\n    if (value === `${parseInt(value)}`) {\n      value = parseInt(value);\n    }\n    if (value || parseInt(value) === 0) {\n      elem.style.setProperty(property, typeof value === 'number' ? `${value}px` : value);\n    } else {\n      elem.style.removeProperty(property);\n    }\n  };\n\n  /**\n   * @param {HTMLElement | null} elem\n   * @param {string} display\n   */\n  const show = (elem, display = 'flex') => {\n    if (!elem) {\n      return;\n    }\n    elem.style.display = display;\n  };\n\n  /**\n   * @param {HTMLElement | null} elem\n   */\n  const hide = elem => {\n    if (!elem) {\n      return;\n    }\n    elem.style.display = 'none';\n  };\n\n  /**\n   * @param {HTMLElement | null} elem\n   * @param {string} display\n   */\n  const showWhenInnerHtmlPresent = (elem, display = 'block') => {\n    if (!elem) {\n      return;\n    }\n    new MutationObserver(() => {\n      toggle(elem, elem.innerHTML, display);\n    }).observe(elem, {\n      childList: true,\n      subtree: true\n    });\n  };\n\n  /**\n   * @param {HTMLElement} parent\n   * @param {string} selector\n   * @param {string} property\n   * @param {string} value\n   */\n  const setStyle = (parent, selector, property, value) => {\n    /** @type {HTMLElement | null} */\n    const el = parent.querySelector(selector);\n    if (el) {\n      el.style.setProperty(property, value);\n    }\n  };\n\n  /**\n   * @param {HTMLElement} elem\n   * @param {any} condition\n   * @param {string} display\n   */\n  const toggle = (elem, condition, display = 'flex') => {\n    if (condition) {\n      show(elem, display);\n    } else {\n      hide(elem);\n    }\n  };\n\n  /**\n   * borrowed from jquery $(elem).is(':visible') implementation\n   *\n   * @param {HTMLElement | null} elem\n   * @returns {boolean}\n   */\n  const isVisible$1 = elem => !!(elem && (elem.offsetWidth || elem.offsetHeight || elem.getClientRects().length));\n\n  /**\n   * @returns {boolean}\n   */\n  const allButtonsAreHidden = () => !isVisible$1(getConfirmButton()) && !isVisible$1(getDenyButton()) && !isVisible$1(getCancelButton());\n\n  /**\n   * @param {HTMLElement} elem\n   * @returns {boolean}\n   */\n  const isScrollable = elem => !!(elem.scrollHeight > elem.clientHeight);\n\n  /**\n   * @param {HTMLElement} element\n   * @param {HTMLElement} stopElement\n   * @returns {boolean}\n   */\n  const selfOrParentIsScrollable = (element, stopElement) => {\n    let parent = element;\n    while (parent && parent !== stopElement) {\n      if (isScrollable(parent)) {\n        return true;\n      }\n      parent = parent.parentElement;\n    }\n    return false;\n  };\n\n  /**\n   * borrowed from https://stackoverflow.com/a/46352119\n   *\n   * @param {HTMLElement} elem\n   * @returns {boolean}\n   */\n  const hasCssAnimation = elem => {\n    const style = window.getComputedStyle(elem);\n    const animDuration = parseFloat(style.getPropertyValue('animation-duration') || '0');\n    const transDuration = parseFloat(style.getPropertyValue('transition-duration') || '0');\n    return animDuration > 0 || transDuration > 0;\n  };\n\n  /**\n   * @param {number} timer\n   * @param {boolean} reset\n   */\n  const animateTimerProgressBar = (timer, reset = false) => {\n    const timerProgressBar = getTimerProgressBar();\n    if (!timerProgressBar) {\n      return;\n    }\n    if (isVisible$1(timerProgressBar)) {\n      if (reset) {\n        timerProgressBar.style.transition = 'none';\n        timerProgressBar.style.width = '100%';\n      }\n      setTimeout(() => {\n        timerProgressBar.style.transition = `width ${timer / 1000}s linear`;\n        timerProgressBar.style.width = '0%';\n      }, 10);\n    }\n  };\n  const stopTimerProgressBar = () => {\n    const timerProgressBar = getTimerProgressBar();\n    if (!timerProgressBar) {\n      return;\n    }\n    const timerProgressBarWidth = parseInt(window.getComputedStyle(timerProgressBar).width);\n    timerProgressBar.style.removeProperty('transition');\n    timerProgressBar.style.width = '100%';\n    const timerProgressBarFullWidth = parseInt(window.getComputedStyle(timerProgressBar).width);\n    const timerProgressBarPercent = timerProgressBarWidth / timerProgressBarFullWidth * 100;\n    timerProgressBar.style.width = `${timerProgressBarPercent}%`;\n  };\n\n  /**\n   * Detect Node env\n   *\n   * @returns {boolean}\n   */\n  const isNodeEnv = () => typeof window === 'undefined' || typeof document === 'undefined';\n\n  const sweetHTML = `\n <div aria-labelledby=\"${swalClasses.title}\" aria-describedby=\"${swalClasses['html-container']}\" class=\"${swalClasses.popup}\" tabindex=\"-1\">\n   <button type=\"button\" class=\"${swalClasses.close}\"></button>\n   <ul class=\"${swalClasses['progress-steps']}\"></ul>\n   <div class=\"${swalClasses.icon}\"></div>\n   <img class=\"${swalClasses.image}\" />\n   <h2 class=\"${swalClasses.title}\" id=\"${swalClasses.title}\"></h2>\n   <div class=\"${swalClasses['html-container']}\" id=\"${swalClasses['html-container']}\"></div>\n   <input class=\"${swalClasses.input}\" id=\"${swalClasses.input}\" />\n   <input type=\"file\" class=\"${swalClasses.file}\" />\n   <div class=\"${swalClasses.range}\">\n     <input type=\"range\" />\n     <output></output>\n   </div>\n   <select class=\"${swalClasses.select}\" id=\"${swalClasses.select}\"></select>\n   <div class=\"${swalClasses.radio}\"></div>\n   <label class=\"${swalClasses.checkbox}\">\n     <input type=\"checkbox\" id=\"${swalClasses.checkbox}\" />\n     <span class=\"${swalClasses.label}\"></span>\n   </label>\n   <textarea class=\"${swalClasses.textarea}\" id=\"${swalClasses.textarea}\"></textarea>\n   <div class=\"${swalClasses['validation-message']}\" id=\"${swalClasses['validation-message']}\"></div>\n   <div class=\"${swalClasses.actions}\">\n     <div class=\"${swalClasses.loader}\"></div>\n     <button type=\"button\" class=\"${swalClasses.confirm}\"></button>\n     <button type=\"button\" class=\"${swalClasses.deny}\"></button>\n     <button type=\"button\" class=\"${swalClasses.cancel}\"></button>\n   </div>\n   <div class=\"${swalClasses.footer}\"></div>\n   <div class=\"${swalClasses['timer-progress-bar-container']}\">\n     <div class=\"${swalClasses['timer-progress-bar']}\"></div>\n   </div>\n </div>\n`.replace(/(^|\\n)\\s*/g, '');\n\n  /**\n   * @returns {boolean}\n   */\n  const resetOldContainer = () => {\n    const oldContainer = getContainer();\n    if (!oldContainer) {\n      return false;\n    }\n    oldContainer.remove();\n    removeClass([document.documentElement, document.body], [swalClasses['no-backdrop'], swalClasses['toast-shown'], swalClasses['has-column']]);\n    return true;\n  };\n  const resetValidationMessage$1 = () => {\n    globalState.currentInstance.resetValidationMessage();\n  };\n  const addInputChangeListeners = () => {\n    const popup = getPopup();\n    const input = getDirectChildByClass(popup, swalClasses.input);\n    const file = getDirectChildByClass(popup, swalClasses.file);\n    /** @type {HTMLInputElement} */\n    const range = popup.querySelector(`.${swalClasses.range} input`);\n    /** @type {HTMLOutputElement} */\n    const rangeOutput = popup.querySelector(`.${swalClasses.range} output`);\n    const select = getDirectChildByClass(popup, swalClasses.select);\n    /** @type {HTMLInputElement} */\n    const checkbox = popup.querySelector(`.${swalClasses.checkbox} input`);\n    const textarea = getDirectChildByClass(popup, swalClasses.textarea);\n    input.oninput = resetValidationMessage$1;\n    file.onchange = resetValidationMessage$1;\n    select.onchange = resetValidationMessage$1;\n    checkbox.onchange = resetValidationMessage$1;\n    textarea.oninput = resetValidationMessage$1;\n    range.oninput = () => {\n      resetValidationMessage$1();\n      rangeOutput.value = range.value;\n    };\n    range.onchange = () => {\n      resetValidationMessage$1();\n      rangeOutput.value = range.value;\n    };\n  };\n\n  /**\n   * @param {string | HTMLElement} target\n   * @returns {HTMLElement}\n   */\n  const getTarget = target => typeof target === 'string' ? document.querySelector(target) : target;\n\n  /**\n   * @param {SweetAlertOptions} params\n   */\n  const setupAccessibility = params => {\n    const popup = getPopup();\n    popup.setAttribute('role', params.toast ? 'alert' : 'dialog');\n    popup.setAttribute('aria-live', params.toast ? 'polite' : 'assertive');\n    if (!params.toast) {\n      popup.setAttribute('aria-modal', 'true');\n    }\n  };\n\n  /**\n   * @param {HTMLElement} targetElement\n   */\n  const setupRTL = targetElement => {\n    if (window.getComputedStyle(targetElement).direction === 'rtl') {\n      addClass(getContainer(), swalClasses.rtl);\n    }\n  };\n\n  /**\n   * Add modal + backdrop + no-war message for Russians to DOM\n   *\n   * @param {SweetAlertOptions} params\n   */\n  const init = params => {\n    // Clean up the old popup container if it exists\n    const oldContainerExisted = resetOldContainer();\n    if (isNodeEnv()) {\n      error('SweetAlert2 requires document to initialize');\n      return;\n    }\n    const container = document.createElement('div');\n    container.className = swalClasses.container;\n    if (oldContainerExisted) {\n      addClass(container, swalClasses['no-transition']);\n    }\n    setInnerHtml(container, sweetHTML);\n    container.dataset['swal2Theme'] = params.theme;\n    const targetElement = getTarget(params.target);\n    targetElement.appendChild(container);\n    if (params.topLayer) {\n      container.setAttribute('popover', '');\n      container.showPopover();\n    }\n    setupAccessibility(params);\n    setupRTL(targetElement);\n    addInputChangeListeners();\n  };\n\n  /**\n   * @param {HTMLElement | object | string} param\n   * @param {HTMLElement} target\n   */\n  const parseHtmlToContainer = (param, target) => {\n    // DOM element\n    if (param instanceof HTMLElement) {\n      target.appendChild(param);\n    }\n\n    // Object\n    else if (typeof param === 'object') {\n      handleObject(param, target);\n    }\n\n    // Plain string\n    else if (param) {\n      setInnerHtml(target, param);\n    }\n  };\n\n  /**\n   * @param {any} param\n   * @param {HTMLElement} target\n   */\n  const handleObject = (param, target) => {\n    // JQuery element(s)\n    if (param.jquery) {\n      handleJqueryElem(target, param);\n    }\n\n    // For other objects use their string representation\n    else {\n      setInnerHtml(target, param.toString());\n    }\n  };\n\n  /**\n   * @param {HTMLElement} target\n   * @param {any} elem\n   */\n  const handleJqueryElem = (target, elem) => {\n    target.textContent = '';\n    if (0 in elem) {\n      for (let i = 0; i in elem; i++) {\n        target.appendChild(elem[i].cloneNode(true));\n      }\n    } else {\n      target.appendChild(elem.cloneNode(true));\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderActions = (instance, params) => {\n    const actions = getActions();\n    const loader = getLoader();\n    if (!actions || !loader) {\n      return;\n    }\n\n    // Actions (buttons) wrapper\n    if (!params.showConfirmButton && !params.showDenyButton && !params.showCancelButton) {\n      hide(actions);\n    } else {\n      show(actions);\n    }\n\n    // Custom class\n    applyCustomClass(actions, params, 'actions');\n\n    // Render all the buttons\n    renderButtons(actions, loader, params);\n\n    // Loader\n    setInnerHtml(loader, params.loaderHtml || '');\n    applyCustomClass(loader, params, 'loader');\n  };\n\n  /**\n   * @param {HTMLElement} actions\n   * @param {HTMLElement} loader\n   * @param {SweetAlertOptions} params\n   */\n  function renderButtons(actions, loader, params) {\n    const confirmButton = getConfirmButton();\n    const denyButton = getDenyButton();\n    const cancelButton = getCancelButton();\n    if (!confirmButton || !denyButton || !cancelButton) {\n      return;\n    }\n\n    // Render buttons\n    renderButton(confirmButton, 'confirm', params);\n    renderButton(denyButton, 'deny', params);\n    renderButton(cancelButton, 'cancel', params);\n    handleButtonsStyling(confirmButton, denyButton, cancelButton, params);\n    if (params.reverseButtons) {\n      if (params.toast) {\n        actions.insertBefore(cancelButton, confirmButton);\n        actions.insertBefore(denyButton, confirmButton);\n      } else {\n        actions.insertBefore(cancelButton, loader);\n        actions.insertBefore(denyButton, loader);\n        actions.insertBefore(confirmButton, loader);\n      }\n    }\n  }\n\n  /**\n   * @param {HTMLElement} confirmButton\n   * @param {HTMLElement} denyButton\n   * @param {HTMLElement} cancelButton\n   * @param {SweetAlertOptions} params\n   */\n  function handleButtonsStyling(confirmButton, denyButton, cancelButton, params) {\n    if (!params.buttonsStyling) {\n      removeClass([confirmButton, denyButton, cancelButton], swalClasses.styled);\n      return;\n    }\n    addClass([confirmButton, denyButton, cancelButton], swalClasses.styled);\n\n    // Apply custom background colors to action buttons\n    if (params.confirmButtonColor) {\n      confirmButton.style.setProperty('--swal2-confirm-button-background-color', params.confirmButtonColor);\n    }\n    if (params.denyButtonColor) {\n      denyButton.style.setProperty('--swal2-deny-button-background-color', params.denyButtonColor);\n    }\n    if (params.cancelButtonColor) {\n      cancelButton.style.setProperty('--swal2-cancel-button-background-color', params.cancelButtonColor);\n    }\n\n    // Apply the outline color to action buttons\n    applyOutlineColor(confirmButton);\n    applyOutlineColor(denyButton);\n    applyOutlineColor(cancelButton);\n  }\n\n  /**\n   * @param {HTMLElement} button\n   */\n  function applyOutlineColor(button) {\n    const buttonStyle = window.getComputedStyle(button);\n    if (buttonStyle.getPropertyValue('--swal2-action-button-focus-box-shadow')) {\n      // If the button already has a custom outline color, no need to change it\n      return;\n    }\n    const outlineColor = buttonStyle.backgroundColor.replace(/rgba?\\((\\d+), (\\d+), (\\d+).*/, 'rgba($1, $2, $3, 0.5)');\n    button.style.setProperty('--swal2-action-button-focus-box-shadow', buttonStyle.getPropertyValue('--swal2-outline').replace(/ rgba\\(.*/, ` ${outlineColor}`));\n  }\n\n  /**\n   * @param {HTMLElement} button\n   * @param {'confirm' | 'deny' | 'cancel'} buttonType\n   * @param {SweetAlertOptions} params\n   */\n  function renderButton(button, buttonType, params) {\n    const buttonName = /** @type {'Confirm' | 'Deny' | 'Cancel'} */capitalizeFirstLetter(buttonType);\n    toggle(button, params[`show${buttonName}Button`], 'inline-block');\n    setInnerHtml(button, params[`${buttonType}ButtonText`] || ''); // Set caption text\n    button.setAttribute('aria-label', params[`${buttonType}ButtonAriaLabel`] || ''); // ARIA label\n\n    // Add buttons custom classes\n    button.className = swalClasses[buttonType];\n    applyCustomClass(button, params, `${buttonType}Button`);\n  }\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderCloseButton = (instance, params) => {\n    const closeButton = getCloseButton();\n    if (!closeButton) {\n      return;\n    }\n    setInnerHtml(closeButton, params.closeButtonHtml || '');\n\n    // Custom class\n    applyCustomClass(closeButton, params, 'closeButton');\n    toggle(closeButton, params.showCloseButton);\n    closeButton.setAttribute('aria-label', params.closeButtonAriaLabel || '');\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderContainer = (instance, params) => {\n    const container = getContainer();\n    if (!container) {\n      return;\n    }\n    handleBackdropParam(container, params.backdrop);\n    handlePositionParam(container, params.position);\n    handleGrowParam(container, params.grow);\n\n    // Custom class\n    applyCustomClass(container, params, 'container');\n  };\n\n  /**\n   * @param {HTMLElement} container\n   * @param {SweetAlertOptions['backdrop']} backdrop\n   */\n  function handleBackdropParam(container, backdrop) {\n    if (typeof backdrop === 'string') {\n      container.style.background = backdrop;\n    } else if (!backdrop) {\n      addClass([document.documentElement, document.body], swalClasses['no-backdrop']);\n    }\n  }\n\n  /**\n   * @param {HTMLElement} container\n   * @param {SweetAlertOptions['position']} position\n   */\n  function handlePositionParam(container, position) {\n    if (!position) {\n      return;\n    }\n    if (position in swalClasses) {\n      addClass(container, swalClasses[position]);\n    } else {\n      warn('The \"position\" parameter is not valid, defaulting to \"center\"');\n      addClass(container, swalClasses.center);\n    }\n  }\n\n  /**\n   * @param {HTMLElement} container\n   * @param {SweetAlertOptions['grow']} grow\n   */\n  function handleGrowParam(container, grow) {\n    if (!grow) {\n      return;\n    }\n    addClass(container, swalClasses[`grow-${grow}`]);\n  }\n\n  /**\n   * This module contains `WeakMap`s for each effectively-\"private  property\" that a `Swal` has.\n   * For example, to set the private property \"foo\" of `this` to \"bar\", you can `privateProps.foo.set(this, 'bar')`\n   * This is the approach that Babel will probably take to implement private methods/fields\n   *   https://github.com/tc39/proposal-private-methods\n   *   https://github.com/babel/babel/pull/7555\n   * Once we have the changes from that PR in Babel, and our core class fits reasonable in *one module*\n   *   then we can use that language feature.\n   */\n\n  var privateProps = {\n    innerParams: new WeakMap(),\n    domCache: new WeakMap()\n  };\n\n  /// <reference path=\"../../../../sweetalert2.d.ts\"/>\n\n\n  /** @type {InputClass[]} */\n  const inputClasses = ['input', 'file', 'range', 'select', 'radio', 'checkbox', 'textarea'];\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderInput = (instance, params) => {\n    const popup = getPopup();\n    if (!popup) {\n      return;\n    }\n    const innerParams = privateProps.innerParams.get(instance);\n    const rerender = !innerParams || params.input !== innerParams.input;\n    inputClasses.forEach(inputClass => {\n      const inputContainer = getDirectChildByClass(popup, swalClasses[inputClass]);\n      if (!inputContainer) {\n        return;\n      }\n\n      // set attributes\n      setAttributes(inputClass, params.inputAttributes);\n\n      // set class\n      inputContainer.className = swalClasses[inputClass];\n      if (rerender) {\n        hide(inputContainer);\n      }\n    });\n    if (params.input) {\n      if (rerender) {\n        showInput(params);\n      }\n      // set custom class\n      setCustomClass(params);\n    }\n  };\n\n  /**\n   * @param {SweetAlertOptions} params\n   */\n  const showInput = params => {\n    if (!params.input) {\n      return;\n    }\n    if (!renderInputType[params.input]) {\n      error(`Unexpected type of input! Expected ${Object.keys(renderInputType).join(' | ')}, got \"${params.input}\"`);\n      return;\n    }\n    const inputContainer = getInputContainer(params.input);\n    if (!inputContainer) {\n      return;\n    }\n    const input = renderInputType[params.input](inputContainer, params);\n    show(inputContainer);\n\n    // input autofocus\n    if (params.inputAutoFocus) {\n      setTimeout(() => {\n        focusInput(input);\n      });\n    }\n  };\n\n  /**\n   * @param {HTMLInputElement} input\n   */\n  const removeAttributes = input => {\n    for (let i = 0; i < input.attributes.length; i++) {\n      const attrName = input.attributes[i].name;\n      if (!['id', 'type', 'value', 'style'].includes(attrName)) {\n        input.removeAttribute(attrName);\n      }\n    }\n  };\n\n  /**\n   * @param {InputClass} inputClass\n   * @param {SweetAlertOptions['inputAttributes']} inputAttributes\n   */\n  const setAttributes = (inputClass, inputAttributes) => {\n    const popup = getPopup();\n    if (!popup) {\n      return;\n    }\n    const input = getInput$1(popup, inputClass);\n    if (!input) {\n      return;\n    }\n    removeAttributes(input);\n    for (const attr in inputAttributes) {\n      input.setAttribute(attr, inputAttributes[attr]);\n    }\n  };\n\n  /**\n   * @param {SweetAlertOptions} params\n   */\n  const setCustomClass = params => {\n    if (!params.input) {\n      return;\n    }\n    const inputContainer = getInputContainer(params.input);\n    if (inputContainer) {\n      applyCustomClass(inputContainer, params, 'input');\n    }\n  };\n\n  /**\n   * @param {HTMLInputElement | HTMLTextAreaElement} input\n   * @param {SweetAlertOptions} params\n   */\n  const setInputPlaceholder = (input, params) => {\n    if (!input.placeholder && params.inputPlaceholder) {\n      input.placeholder = params.inputPlaceholder;\n    }\n  };\n\n  /**\n   * @param {Input} input\n   * @param {Input} prependTo\n   * @param {SweetAlertOptions} params\n   */\n  const setInputLabel = (input, prependTo, params) => {\n    if (params.inputLabel) {\n      const label = document.createElement('label');\n      const labelClass = swalClasses['input-label'];\n      label.setAttribute('for', input.id);\n      label.className = labelClass;\n      if (typeof params.customClass === 'object') {\n        addClass(label, params.customClass.inputLabel);\n      }\n      label.innerText = params.inputLabel;\n      prependTo.insertAdjacentElement('beforebegin', label);\n    }\n  };\n\n  /**\n   * @param {SweetAlertInput} inputType\n   * @returns {HTMLElement | undefined}\n   */\n  const getInputContainer = inputType => {\n    const popup = getPopup();\n    if (!popup) {\n      return;\n    }\n    return getDirectChildByClass(popup, swalClasses[(/** @type {SwalClass} */inputType)] || swalClasses.input);\n  };\n\n  /**\n   * @param {HTMLInputElement | HTMLOutputElement | HTMLTextAreaElement} input\n   * @param {SweetAlertOptions['inputValue']} inputValue\n   */\n  const checkAndSetInputValue = (input, inputValue) => {\n    if (['string', 'number'].includes(typeof inputValue)) {\n      input.value = `${inputValue}`;\n    } else if (!isPromise(inputValue)) {\n      warn(`Unexpected type of inputValue! Expected \"string\", \"number\" or \"Promise\", got \"${typeof inputValue}\"`);\n    }\n  };\n\n  /** @type {Record<SweetAlertInput, (input: Input | HTMLElement, params: SweetAlertOptions) => Input>} */\n  const renderInputType = {};\n\n  /**\n   * @param {HTMLInputElement} input\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLInputElement}\n   */\n  renderInputType.text = renderInputType.email = renderInputType.password = renderInputType.number = renderInputType.tel = renderInputType.url = renderInputType.search = renderInputType.date = renderInputType['datetime-local'] = renderInputType.time = renderInputType.week = renderInputType.month = /** @type {(input: Input | HTMLElement, params: SweetAlertOptions) => Input} */\n  (input, params) => {\n    checkAndSetInputValue(input, params.inputValue);\n    setInputLabel(input, input, params);\n    setInputPlaceholder(input, params);\n    input.type = params.input;\n    return input;\n  };\n\n  /**\n   * @param {HTMLInputElement} input\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLInputElement}\n   */\n  renderInputType.file = (input, params) => {\n    setInputLabel(input, input, params);\n    setInputPlaceholder(input, params);\n    return input;\n  };\n\n  /**\n   * @param {HTMLInputElement} range\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLInputElement}\n   */\n  renderInputType.range = (range, params) => {\n    const rangeInput = range.querySelector('input');\n    const rangeOutput = range.querySelector('output');\n    checkAndSetInputValue(rangeInput, params.inputValue);\n    rangeInput.type = params.input;\n    checkAndSetInputValue(rangeOutput, params.inputValue);\n    setInputLabel(rangeInput, range, params);\n    return range;\n  };\n\n  /**\n   * @param {HTMLSelectElement} select\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLSelectElement}\n   */\n  renderInputType.select = (select, params) => {\n    select.textContent = '';\n    if (params.inputPlaceholder) {\n      const placeholder = document.createElement('option');\n      setInnerHtml(placeholder, params.inputPlaceholder);\n      placeholder.value = '';\n      placeholder.disabled = true;\n      placeholder.selected = true;\n      select.appendChild(placeholder);\n    }\n    setInputLabel(select, select, params);\n    return select;\n  };\n\n  /**\n   * @param {HTMLInputElement} radio\n   * @returns {HTMLInputElement}\n   */\n  renderInputType.radio = radio => {\n    radio.textContent = '';\n    return radio;\n  };\n\n  /**\n   * @param {HTMLLabelElement} checkboxContainer\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLInputElement}\n   */\n  renderInputType.checkbox = (checkboxContainer, params) => {\n    const checkbox = getInput$1(getPopup(), 'checkbox');\n    checkbox.value = '1';\n    checkbox.checked = Boolean(params.inputValue);\n    const label = checkboxContainer.querySelector('span');\n    setInnerHtml(label, params.inputPlaceholder || params.inputLabel);\n    return checkbox;\n  };\n\n  /**\n   * @param {HTMLTextAreaElement} textarea\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLTextAreaElement}\n   */\n  renderInputType.textarea = (textarea, params) => {\n    checkAndSetInputValue(textarea, params.inputValue);\n    setInputPlaceholder(textarea, params);\n    setInputLabel(textarea, textarea, params);\n\n    /**\n     * @param {HTMLElement} el\n     * @returns {number}\n     */\n    const getMargin = el => parseInt(window.getComputedStyle(el).marginLeft) + parseInt(window.getComputedStyle(el).marginRight);\n\n    // https://github.com/sweetalert2/sweetalert2/issues/2291\n    setTimeout(() => {\n      // https://github.com/sweetalert2/sweetalert2/issues/1699\n      if ('MutationObserver' in window) {\n        const initialPopupWidth = parseInt(window.getComputedStyle(getPopup()).width);\n        const textareaResizeHandler = () => {\n          // check if texarea is still in document (i.e. popup wasn't closed in the meantime)\n          if (!document.body.contains(textarea)) {\n            return;\n          }\n          const textareaWidth = textarea.offsetWidth + getMargin(textarea);\n          if (textareaWidth > initialPopupWidth) {\n            getPopup().style.width = `${textareaWidth}px`;\n          } else {\n            applyNumericalStyle(getPopup(), 'width', params.width);\n          }\n        };\n        new MutationObserver(textareaResizeHandler).observe(textarea, {\n          attributes: true,\n          attributeFilter: ['style']\n        });\n      }\n    });\n    return textarea;\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderContent = (instance, params) => {\n    const htmlContainer = getHtmlContainer();\n    if (!htmlContainer) {\n      return;\n    }\n    showWhenInnerHtmlPresent(htmlContainer);\n    applyCustomClass(htmlContainer, params, 'htmlContainer');\n\n    // Content as HTML\n    if (params.html) {\n      parseHtmlToContainer(params.html, htmlContainer);\n      show(htmlContainer, 'block');\n    }\n\n    // Content as plain text\n    else if (params.text) {\n      htmlContainer.textContent = params.text;\n      show(htmlContainer, 'block');\n    }\n\n    // No content\n    else {\n      hide(htmlContainer);\n    }\n    renderInput(instance, params);\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderFooter = (instance, params) => {\n    const footer = getFooter();\n    if (!footer) {\n      return;\n    }\n    showWhenInnerHtmlPresent(footer);\n    toggle(footer, params.footer, 'block');\n    if (params.footer) {\n      parseHtmlToContainer(params.footer, footer);\n    }\n\n    // Custom class\n    applyCustomClass(footer, params, 'footer');\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderIcon = (instance, params) => {\n    const innerParams = privateProps.innerParams.get(instance);\n    const icon = getIcon();\n    if (!icon) {\n      return;\n    }\n\n    // if the given icon already rendered, apply the styling without re-rendering the icon\n    if (innerParams && params.icon === innerParams.icon) {\n      // Custom or default content\n      setContent(icon, params);\n      applyStyles(icon, params);\n      return;\n    }\n    if (!params.icon && !params.iconHtml) {\n      hide(icon);\n      return;\n    }\n    if (params.icon && Object.keys(iconTypes).indexOf(params.icon) === -1) {\n      error(`Unknown icon! Expected \"success\", \"error\", \"warning\", \"info\" or \"question\", got \"${params.icon}\"`);\n      hide(icon);\n      return;\n    }\n    show(icon);\n\n    // Custom or default content\n    setContent(icon, params);\n    applyStyles(icon, params);\n\n    // Animate icon\n    addClass(icon, params.showClass && params.showClass.icon);\n\n    // Re-adjust the success icon on system theme change\n    const colorSchemeQueryList = window.matchMedia('(prefers-color-scheme: dark)');\n    colorSchemeQueryList.addEventListener('change', adjustSuccessIconBackgroundColor);\n  };\n\n  /**\n   * @param {HTMLElement} icon\n   * @param {SweetAlertOptions} params\n   */\n  const applyStyles = (icon, params) => {\n    for (const [iconType, iconClassName] of Object.entries(iconTypes)) {\n      if (params.icon !== iconType) {\n        removeClass(icon, iconClassName);\n      }\n    }\n    addClass(icon, params.icon && iconTypes[params.icon]);\n\n    // Icon color\n    setColor(icon, params);\n\n    // Success icon background color\n    adjustSuccessIconBackgroundColor();\n\n    // Custom class\n    applyCustomClass(icon, params, 'icon');\n  };\n\n  // Adjust success icon background color to match the popup background color\n  const adjustSuccessIconBackgroundColor = () => {\n    const popup = getPopup();\n    if (!popup) {\n      return;\n    }\n    const popupBackgroundColor = window.getComputedStyle(popup).getPropertyValue('background-color');\n    /** @type {NodeListOf<HTMLElement>} */\n    const successIconParts = popup.querySelectorAll('[class^=swal2-success-circular-line], .swal2-success-fix');\n    for (let i = 0; i < successIconParts.length; i++) {\n      successIconParts[i].style.backgroundColor = popupBackgroundColor;\n    }\n  };\n  const successIconHtml = `\n  <div class=\"swal2-success-circular-line-left\"></div>\n  <span class=\"swal2-success-line-tip\"></span> <span class=\"swal2-success-line-long\"></span>\n  <div class=\"swal2-success-ring\"></div> <div class=\"swal2-success-fix\"></div>\n  <div class=\"swal2-success-circular-line-right\"></div>\n`;\n  const errorIconHtml = `\n  <span class=\"swal2-x-mark\">\n    <span class=\"swal2-x-mark-line-left\"></span>\n    <span class=\"swal2-x-mark-line-right\"></span>\n  </span>\n`;\n\n  /**\n   * @param {HTMLElement} icon\n   * @param {SweetAlertOptions} params\n   */\n  const setContent = (icon, params) => {\n    if (!params.icon && !params.iconHtml) {\n      return;\n    }\n    let oldContent = icon.innerHTML;\n    let newContent = '';\n    if (params.iconHtml) {\n      newContent = iconContent(params.iconHtml);\n    } else if (params.icon === 'success') {\n      newContent = successIconHtml;\n      oldContent = oldContent.replace(/ style=\".*?\"/g, ''); // undo adjustSuccessIconBackgroundColor()\n    } else if (params.icon === 'error') {\n      newContent = errorIconHtml;\n    } else if (params.icon) {\n      const defaultIconHtml = {\n        question: '?',\n        warning: '!',\n        info: 'i'\n      };\n      newContent = iconContent(defaultIconHtml[params.icon]);\n    }\n    if (oldContent.trim() !== newContent.trim()) {\n      setInnerHtml(icon, newContent);\n    }\n  };\n\n  /**\n   * @param {HTMLElement} icon\n   * @param {SweetAlertOptions} params\n   */\n  const setColor = (icon, params) => {\n    if (!params.iconColor) {\n      return;\n    }\n    icon.style.color = params.iconColor;\n    icon.style.borderColor = params.iconColor;\n    for (const sel of ['.swal2-success-line-tip', '.swal2-success-line-long', '.swal2-x-mark-line-left', '.swal2-x-mark-line-right']) {\n      setStyle(icon, sel, 'background-color', params.iconColor);\n    }\n    setStyle(icon, '.swal2-success-ring', 'border-color', params.iconColor);\n  };\n\n  /**\n   * @param {string} content\n   * @returns {string}\n   */\n  const iconContent = content => `<div class=\"${swalClasses['icon-content']}\">${content}</div>`;\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderImage = (instance, params) => {\n    const image = getImage();\n    if (!image) {\n      return;\n    }\n    if (!params.imageUrl) {\n      hide(image);\n      return;\n    }\n    show(image, '');\n\n    // Src, alt\n    image.setAttribute('src', params.imageUrl);\n    image.setAttribute('alt', params.imageAlt || '');\n\n    // Width, height\n    applyNumericalStyle(image, 'width', params.imageWidth);\n    applyNumericalStyle(image, 'height', params.imageHeight);\n\n    // Class\n    image.className = swalClasses.image;\n    applyCustomClass(image, params, 'image');\n  };\n\n  let dragging = false;\n  let mousedownX = 0;\n  let mousedownY = 0;\n  let initialX = 0;\n  let initialY = 0;\n\n  /**\n   * @param {HTMLElement} popup\n   */\n  const addDraggableListeners = popup => {\n    popup.addEventListener('mousedown', down);\n    document.body.addEventListener('mousemove', move);\n    popup.addEventListener('mouseup', up);\n    popup.addEventListener('touchstart', down);\n    document.body.addEventListener('touchmove', move);\n    popup.addEventListener('touchend', up);\n  };\n\n  /**\n   * @param {HTMLElement} popup\n   */\n  const removeDraggableListeners = popup => {\n    popup.removeEventListener('mousedown', down);\n    document.body.removeEventListener('mousemove', move);\n    popup.removeEventListener('mouseup', up);\n    popup.removeEventListener('touchstart', down);\n    document.body.removeEventListener('touchmove', move);\n    popup.removeEventListener('touchend', up);\n  };\n\n  /**\n   * @param {MouseEvent | TouchEvent} event\n   */\n  const down = event => {\n    const popup = getPopup();\n    if (event.target === popup || getIcon().contains(/** @type {HTMLElement} */event.target)) {\n      dragging = true;\n      const clientXY = getClientXY(event);\n      mousedownX = clientXY.clientX;\n      mousedownY = clientXY.clientY;\n      initialX = parseInt(popup.style.insetInlineStart) || 0;\n      initialY = parseInt(popup.style.insetBlockStart) || 0;\n      addClass(popup, 'swal2-dragging');\n    }\n  };\n\n  /**\n   * @param {MouseEvent | TouchEvent} event\n   */\n  const move = event => {\n    const popup = getPopup();\n    if (dragging) {\n      let {\n        clientX,\n        clientY\n      } = getClientXY(event);\n      popup.style.insetInlineStart = `${initialX + (clientX - mousedownX)}px`;\n      popup.style.insetBlockStart = `${initialY + (clientY - mousedownY)}px`;\n    }\n  };\n  const up = () => {\n    const popup = getPopup();\n    dragging = false;\n    removeClass(popup, 'swal2-dragging');\n  };\n\n  /**\n   * @param {MouseEvent | TouchEvent} event\n   * @returns {{ clientX: number, clientY: number }}\n   */\n  const getClientXY = event => {\n    let clientX = 0,\n      clientY = 0;\n    if (event.type.startsWith('mouse')) {\n      clientX = /** @type {MouseEvent} */event.clientX;\n      clientY = /** @type {MouseEvent} */event.clientY;\n    } else if (event.type.startsWith('touch')) {\n      clientX = /** @type {TouchEvent} */event.touches[0].clientX;\n      clientY = /** @type {TouchEvent} */event.touches[0].clientY;\n    }\n    return {\n      clientX,\n      clientY\n    };\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderPopup = (instance, params) => {\n    const container = getContainer();\n    const popup = getPopup();\n    if (!container || !popup) {\n      return;\n    }\n\n    // Width\n    // https://github.com/sweetalert2/sweetalert2/issues/2170\n    if (params.toast) {\n      applyNumericalStyle(container, 'width', params.width);\n      popup.style.width = '100%';\n      const loader = getLoader();\n      if (loader) {\n        popup.insertBefore(loader, getIcon());\n      }\n    } else {\n      applyNumericalStyle(popup, 'width', params.width);\n    }\n\n    // Padding\n    applyNumericalStyle(popup, 'padding', params.padding);\n\n    // Color\n    if (params.color) {\n      popup.style.color = params.color;\n    }\n\n    // Background\n    if (params.background) {\n      popup.style.background = params.background;\n    }\n    hide(getValidationMessage());\n\n    // Classes\n    addClasses$1(popup, params);\n    if (params.draggable && !params.toast) {\n      addClass(popup, swalClasses.draggable);\n      addDraggableListeners(popup);\n    } else {\n      removeClass(popup, swalClasses.draggable);\n      removeDraggableListeners(popup);\n    }\n  };\n\n  /**\n   * @param {HTMLElement} popup\n   * @param {SweetAlertOptions} params\n   */\n  const addClasses$1 = (popup, params) => {\n    const showClass = params.showClass || {};\n    // Default Class + showClass when updating Swal.update({})\n    popup.className = `${swalClasses.popup} ${isVisible$1(popup) ? showClass.popup : ''}`;\n    if (params.toast) {\n      addClass([document.documentElement, document.body], swalClasses['toast-shown']);\n      addClass(popup, swalClasses.toast);\n    } else {\n      addClass(popup, swalClasses.modal);\n    }\n\n    // Custom class\n    applyCustomClass(popup, params, 'popup');\n    // TODO: remove in the next major\n    if (typeof params.customClass === 'string') {\n      addClass(popup, params.customClass);\n    }\n\n    // Icon class (#1842)\n    if (params.icon) {\n      addClass(popup, swalClasses[`icon-${params.icon}`]);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderProgressSteps = (instance, params) => {\n    const progressStepsContainer = getProgressSteps();\n    if (!progressStepsContainer) {\n      return;\n    }\n    const {\n      progressSteps,\n      currentProgressStep\n    } = params;\n    if (!progressSteps || progressSteps.length === 0 || currentProgressStep === undefined) {\n      hide(progressStepsContainer);\n      return;\n    }\n    show(progressStepsContainer);\n    progressStepsContainer.textContent = '';\n    if (currentProgressStep >= progressSteps.length) {\n      warn('Invalid currentProgressStep parameter, it should be less than progressSteps.length ' + '(currentProgressStep like JS arrays starts from 0)');\n    }\n    progressSteps.forEach((step, index) => {\n      const stepEl = createStepElement(step);\n      progressStepsContainer.appendChild(stepEl);\n      if (index === currentProgressStep) {\n        addClass(stepEl, swalClasses['active-progress-step']);\n      }\n      if (index !== progressSteps.length - 1) {\n        const lineEl = createLineElement(params);\n        progressStepsContainer.appendChild(lineEl);\n      }\n    });\n  };\n\n  /**\n   * @param {string} step\n   * @returns {HTMLLIElement}\n   */\n  const createStepElement = step => {\n    const stepEl = document.createElement('li');\n    addClass(stepEl, swalClasses['progress-step']);\n    setInnerHtml(stepEl, step);\n    return stepEl;\n  };\n\n  /**\n   * @param {SweetAlertOptions} params\n   * @returns {HTMLLIElement}\n   */\n  const createLineElement = params => {\n    const lineEl = document.createElement('li');\n    addClass(lineEl, swalClasses['progress-step-line']);\n    if (params.progressStepsDistance) {\n      applyNumericalStyle(lineEl, 'width', params.progressStepsDistance);\n    }\n    return lineEl;\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const renderTitle = (instance, params) => {\n    const title = getTitle();\n    if (!title) {\n      return;\n    }\n    showWhenInnerHtmlPresent(title);\n    toggle(title, params.title || params.titleText, 'block');\n    if (params.title) {\n      parseHtmlToContainer(params.title, title);\n    }\n    if (params.titleText) {\n      title.innerText = params.titleText;\n    }\n\n    // Custom class\n    applyCustomClass(title, params, 'title');\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const render = (instance, params) => {\n    renderPopup(instance, params);\n    renderContainer(instance, params);\n    renderProgressSteps(instance, params);\n    renderIcon(instance, params);\n    renderImage(instance, params);\n    renderTitle(instance, params);\n    renderCloseButton(instance, params);\n    renderContent(instance, params);\n    renderActions(instance, params);\n    renderFooter(instance, params);\n    const popup = getPopup();\n    if (typeof params.didRender === 'function' && popup) {\n      params.didRender(popup);\n    }\n    globalState.eventEmitter.emit('didRender', popup);\n  };\n\n  /*\n   * Global function to determine if SweetAlert2 popup is shown\n   */\n  const isVisible = () => {\n    return isVisible$1(getPopup());\n  };\n\n  /*\n   * Global function to click 'Confirm' button\n   */\n  const clickConfirm = () => {\n    var _dom$getConfirmButton;\n    return (_dom$getConfirmButton = getConfirmButton()) === null || _dom$getConfirmButton === void 0 ? void 0 : _dom$getConfirmButton.click();\n  };\n\n  /*\n   * Global function to click 'Deny' button\n   */\n  const clickDeny = () => {\n    var _dom$getDenyButton;\n    return (_dom$getDenyButton = getDenyButton()) === null || _dom$getDenyButton === void 0 ? void 0 : _dom$getDenyButton.click();\n  };\n\n  /*\n   * Global function to click 'Cancel' button\n   */\n  const clickCancel = () => {\n    var _dom$getCancelButton;\n    return (_dom$getCancelButton = getCancelButton()) === null || _dom$getCancelButton === void 0 ? void 0 : _dom$getCancelButton.click();\n  };\n\n  /** @typedef {'cancel' | 'backdrop' | 'close' | 'esc' | 'timer'} DismissReason */\n\n  /** @type {Record<DismissReason, DismissReason>} */\n  const DismissReason = Object.freeze({\n    cancel: 'cancel',\n    backdrop: 'backdrop',\n    close: 'close',\n    esc: 'esc',\n    timer: 'timer'\n  });\n\n  /**\n   * @param {GlobalState} globalState\n   */\n  const removeKeydownHandler = globalState => {\n    if (globalState.keydownTarget && globalState.keydownHandlerAdded) {\n      globalState.keydownTarget.removeEventListener('keydown', globalState.keydownHandler, {\n        capture: globalState.keydownListenerCapture\n      });\n      globalState.keydownHandlerAdded = false;\n    }\n  };\n\n  /**\n   * @param {GlobalState} globalState\n   * @param {SweetAlertOptions} innerParams\n   * @param {*} dismissWith\n   */\n  const addKeydownHandler = (globalState, innerParams, dismissWith) => {\n    removeKeydownHandler(globalState);\n    if (!innerParams.toast) {\n      globalState.keydownHandler = e => keydownHandler(innerParams, e, dismissWith);\n      globalState.keydownTarget = innerParams.keydownListenerCapture ? window : getPopup();\n      globalState.keydownListenerCapture = innerParams.keydownListenerCapture;\n      globalState.keydownTarget.addEventListener('keydown', globalState.keydownHandler, {\n        capture: globalState.keydownListenerCapture\n      });\n      globalState.keydownHandlerAdded = true;\n    }\n  };\n\n  /**\n   * @param {number} index\n   * @param {number} increment\n   */\n  const setFocus = (index, increment) => {\n    var _dom$getPopup;\n    const focusableElements = getFocusableElements();\n    // search for visible elements and select the next possible match\n    if (focusableElements.length) {\n      index = index + increment;\n\n      // shift + tab when .swal2-popup is focused\n      if (index === -2) {\n        index = focusableElements.length - 1;\n      }\n\n      // rollover to first item\n      if (index === focusableElements.length) {\n        index = 0;\n\n        // go to last item\n      } else if (index === -1) {\n        index = focusableElements.length - 1;\n      }\n      focusableElements[index].focus();\n      return;\n    }\n    // no visible focusable elements, focus the popup\n    (_dom$getPopup = getPopup()) === null || _dom$getPopup === void 0 || _dom$getPopup.focus();\n  };\n  const arrowKeysNextButton = ['ArrowRight', 'ArrowDown'];\n  const arrowKeysPreviousButton = ['ArrowLeft', 'ArrowUp'];\n\n  /**\n   * @param {SweetAlertOptions} innerParams\n   * @param {KeyboardEvent} event\n   * @param {Function} dismissWith\n   */\n  const keydownHandler = (innerParams, event, dismissWith) => {\n    if (!innerParams) {\n      return; // This instance has already been destroyed\n    }\n\n    // Ignore keydown during IME composition\n    // https://developer.mozilla.org/en-US/docs/Web/API/Document/keydown_event#ignoring_keydown_during_ime_composition\n    // https://github.com/sweetalert2/sweetalert2/issues/720\n    // https://github.com/sweetalert2/sweetalert2/issues/2406\n    if (event.isComposing || event.keyCode === 229) {\n      return;\n    }\n    if (innerParams.stopKeydownPropagation) {\n      event.stopPropagation();\n    }\n\n    // ENTER\n    if (event.key === 'Enter') {\n      handleEnter(event, innerParams);\n    }\n\n    // TAB\n    else if (event.key === 'Tab') {\n      handleTab(event);\n    }\n\n    // ARROWS - switch focus between buttons\n    else if ([...arrowKeysNextButton, ...arrowKeysPreviousButton].includes(event.key)) {\n      handleArrows(event.key);\n    }\n\n    // ESC\n    else if (event.key === 'Escape') {\n      handleEsc(event, innerParams, dismissWith);\n    }\n  };\n\n  /**\n   * @param {KeyboardEvent} event\n   * @param {SweetAlertOptions} innerParams\n   */\n  const handleEnter = (event, innerParams) => {\n    // https://github.com/sweetalert2/sweetalert2/issues/2386\n    if (!callIfFunction(innerParams.allowEnterKey)) {\n      return;\n    }\n    const input = getInput$1(getPopup(), innerParams.input);\n    if (event.target && input && event.target instanceof HTMLElement && event.target.outerHTML === input.outerHTML) {\n      if (['textarea', 'file'].includes(innerParams.input)) {\n        return; // do not submit\n      }\n      clickConfirm();\n      event.preventDefault();\n    }\n  };\n\n  /**\n   * @param {KeyboardEvent} event\n   */\n  const handleTab = event => {\n    const targetElement = event.target;\n    const focusableElements = getFocusableElements();\n    let btnIndex = -1;\n    for (let i = 0; i < focusableElements.length; i++) {\n      if (targetElement === focusableElements[i]) {\n        btnIndex = i;\n        break;\n      }\n    }\n\n    // Cycle to the next button\n    if (!event.shiftKey) {\n      setFocus(btnIndex, 1);\n    }\n\n    // Cycle to the prev button\n    else {\n      setFocus(btnIndex, -1);\n    }\n    event.stopPropagation();\n    event.preventDefault();\n  };\n\n  /**\n   * @param {string} key\n   */\n  const handleArrows = key => {\n    const actions = getActions();\n    const confirmButton = getConfirmButton();\n    const denyButton = getDenyButton();\n    const cancelButton = getCancelButton();\n    if (!actions || !confirmButton || !denyButton || !cancelButton) {\n      return;\n    }\n    /** @type HTMLElement[] */\n    const buttons = [confirmButton, denyButton, cancelButton];\n    if (document.activeElement instanceof HTMLElement && !buttons.includes(document.activeElement)) {\n      return;\n    }\n    const sibling = arrowKeysNextButton.includes(key) ? 'nextElementSibling' : 'previousElementSibling';\n    let buttonToFocus = document.activeElement;\n    if (!buttonToFocus) {\n      return;\n    }\n    for (let i = 0; i < actions.children.length; i++) {\n      buttonToFocus = buttonToFocus[sibling];\n      if (!buttonToFocus) {\n        return;\n      }\n      if (buttonToFocus instanceof HTMLButtonElement && isVisible$1(buttonToFocus)) {\n        break;\n      }\n    }\n    if (buttonToFocus instanceof HTMLButtonElement) {\n      buttonToFocus.focus();\n    }\n  };\n\n  /**\n   * @param {KeyboardEvent} event\n   * @param {SweetAlertOptions} innerParams\n   * @param {Function} dismissWith\n   */\n  const handleEsc = (event, innerParams, dismissWith) => {\n    if (callIfFunction(innerParams.allowEscapeKey)) {\n      event.preventDefault();\n      dismissWith(DismissReason.esc);\n    }\n  };\n\n  /**\n   * This module contains `WeakMap`s for each effectively-\"private  property\" that a `Swal` has.\n   * For example, to set the private property \"foo\" of `this` to \"bar\", you can `privateProps.foo.set(this, 'bar')`\n   * This is the approach that Babel will probably take to implement private methods/fields\n   *   https://github.com/tc39/proposal-private-methods\n   *   https://github.com/babel/babel/pull/7555\n   * Once we have the changes from that PR in Babel, and our core class fits reasonable in *one module*\n   *   then we can use that language feature.\n   */\n\n  var privateMethods = {\n    swalPromiseResolve: new WeakMap(),\n    swalPromiseReject: new WeakMap()\n  };\n\n  // From https://developer.paciellogroup.com/blog/2018/06/the-current-state-of-modal-dialog-accessibility/\n  // Adding aria-hidden=\"true\" to elements outside of the active modal dialog ensures that\n  // elements not within the active modal dialog will not be surfaced if a user opens a screen\n  // reader’s list of elements (headings, form controls, landmarks, etc.) in the document.\n\n  const setAriaHidden = () => {\n    const container = getContainer();\n    const bodyChildren = Array.from(document.body.children);\n    bodyChildren.forEach(el => {\n      if (el.contains(container)) {\n        return;\n      }\n      if (el.hasAttribute('aria-hidden')) {\n        el.setAttribute('data-previous-aria-hidden', el.getAttribute('aria-hidden') || '');\n      }\n      el.setAttribute('aria-hidden', 'true');\n    });\n  };\n  const unsetAriaHidden = () => {\n    const bodyChildren = Array.from(document.body.children);\n    bodyChildren.forEach(el => {\n      if (el.hasAttribute('data-previous-aria-hidden')) {\n        el.setAttribute('aria-hidden', el.getAttribute('data-previous-aria-hidden') || '');\n        el.removeAttribute('data-previous-aria-hidden');\n      } else {\n        el.removeAttribute('aria-hidden');\n      }\n    });\n  };\n\n  // @ts-ignore\n  const isSafariOrIOS = typeof window !== 'undefined' && !!window.GestureEvent; // true for Safari desktop + all iOS browsers https://stackoverflow.com/a/70585394\n\n  /**\n   * Fix iOS scrolling\n   * http://stackoverflow.com/q/39626302\n   */\n  const iOSfix = () => {\n    if (isSafariOrIOS && !hasClass(document.body, swalClasses.iosfix)) {\n      const offset = document.body.scrollTop;\n      document.body.style.top = `${offset * -1}px`;\n      addClass(document.body, swalClasses.iosfix);\n      lockBodyScroll();\n    }\n  };\n\n  /**\n   * https://github.com/sweetalert2/sweetalert2/issues/1246\n   */\n  const lockBodyScroll = () => {\n    const container = getContainer();\n    if (!container) {\n      return;\n    }\n    /** @type {boolean} */\n    let preventTouchMove;\n    /**\n     * @param {TouchEvent} event\n     */\n    container.ontouchstart = event => {\n      preventTouchMove = shouldPreventTouchMove(event);\n    };\n    /**\n     * @param {TouchEvent} event\n     */\n    container.ontouchmove = event => {\n      if (preventTouchMove) {\n        event.preventDefault();\n        event.stopPropagation();\n      }\n    };\n  };\n\n  /**\n   * @param {TouchEvent} event\n   * @returns {boolean}\n   */\n  const shouldPreventTouchMove = event => {\n    const target = event.target;\n    const container = getContainer();\n    const htmlContainer = getHtmlContainer();\n    if (!container || !htmlContainer) {\n      return false;\n    }\n    if (isStylus(event) || isZoom(event)) {\n      return false;\n    }\n    if (target === container) {\n      return true;\n    }\n    if (!isScrollable(container) && target instanceof HTMLElement && !selfOrParentIsScrollable(target, htmlContainer) &&\n    // #2823\n    target.tagName !== 'INPUT' &&\n    // #1603\n    target.tagName !== 'TEXTAREA' &&\n    // #2266\n    !(isScrollable(htmlContainer) &&\n    // #1944\n    htmlContainer.contains(target))) {\n      return true;\n    }\n    return false;\n  };\n\n  /**\n   * https://github.com/sweetalert2/sweetalert2/issues/1786\n   *\n   * @param {*} event\n   * @returns {boolean}\n   */\n  const isStylus = event => {\n    return event.touches && event.touches.length && event.touches[0].touchType === 'stylus';\n  };\n\n  /**\n   * https://github.com/sweetalert2/sweetalert2/issues/1891\n   *\n   * @param {TouchEvent} event\n   * @returns {boolean}\n   */\n  const isZoom = event => {\n    return event.touches && event.touches.length > 1;\n  };\n  const undoIOSfix = () => {\n    if (hasClass(document.body, swalClasses.iosfix)) {\n      const offset = parseInt(document.body.style.top, 10);\n      removeClass(document.body, swalClasses.iosfix);\n      document.body.style.top = '';\n      document.body.scrollTop = offset * -1;\n    }\n  };\n\n  /**\n   * Measure scrollbar width for padding body during modal show/hide\n   * https://github.com/twbs/bootstrap/blob/master/js/src/modal.js\n   *\n   * @returns {number}\n   */\n  const measureScrollbar = () => {\n    const scrollDiv = document.createElement('div');\n    scrollDiv.className = swalClasses['scrollbar-measure'];\n    document.body.appendChild(scrollDiv);\n    const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth;\n    document.body.removeChild(scrollDiv);\n    return scrollbarWidth;\n  };\n\n  /**\n   * Remember state in cases where opening and handling a modal will fiddle with it.\n   * @type {number | null}\n   */\n  let previousBodyPadding = null;\n\n  /**\n   * @param {string} initialBodyOverflow\n   */\n  const replaceScrollbarWithPadding = initialBodyOverflow => {\n    // for queues, do not do this more than once\n    if (previousBodyPadding !== null) {\n      return;\n    }\n    // if the body has overflow\n    if (document.body.scrollHeight > window.innerHeight || initialBodyOverflow === 'scroll' // https://github.com/sweetalert2/sweetalert2/issues/2663\n    ) {\n      // add padding so the content doesn't shift after removal of scrollbar\n      previousBodyPadding = parseInt(window.getComputedStyle(document.body).getPropertyValue('padding-right'));\n      document.body.style.paddingRight = `${previousBodyPadding + measureScrollbar()}px`;\n    }\n  };\n  const undoReplaceScrollbarWithPadding = () => {\n    if (previousBodyPadding !== null) {\n      document.body.style.paddingRight = `${previousBodyPadding}px`;\n      previousBodyPadding = null;\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {HTMLElement} container\n   * @param {boolean} returnFocus\n   * @param {Function} didClose\n   */\n  function removePopupAndResetState(instance, container, returnFocus, didClose) {\n    if (isToast()) {\n      triggerDidCloseAndDispose(instance, didClose);\n    } else {\n      restoreActiveElement(returnFocus).then(() => triggerDidCloseAndDispose(instance, didClose));\n      removeKeydownHandler(globalState);\n    }\n\n    // workaround for https://github.com/sweetalert2/sweetalert2/issues/2088\n    // for some reason removing the container in Safari will scroll the document to bottom\n    if (isSafariOrIOS) {\n      container.setAttribute('style', 'display:none !important');\n      container.removeAttribute('class');\n      container.innerHTML = '';\n    } else {\n      container.remove();\n    }\n    if (isModal()) {\n      undoReplaceScrollbarWithPadding();\n      undoIOSfix();\n      unsetAriaHidden();\n    }\n    removeBodyClasses();\n  }\n\n  /**\n   * Remove SweetAlert2 classes from body\n   */\n  function removeBodyClasses() {\n    removeClass([document.documentElement, document.body], [swalClasses.shown, swalClasses['height-auto'], swalClasses['no-backdrop'], swalClasses['toast-shown']]);\n  }\n\n  /**\n   * Instance method to close sweetAlert\n   *\n   * @param {any} resolveValue\n   */\n  function close(resolveValue) {\n    resolveValue = prepareResolveValue(resolveValue);\n    const swalPromiseResolve = privateMethods.swalPromiseResolve.get(this);\n    const didClose = triggerClosePopup(this);\n    if (this.isAwaitingPromise) {\n      // A swal awaiting for a promise (after a click on Confirm or Deny) cannot be dismissed anymore #2335\n      if (!resolveValue.isDismissed) {\n        handleAwaitingPromise(this);\n        swalPromiseResolve(resolveValue);\n      }\n    } else if (didClose) {\n      // Resolve Swal promise\n      swalPromiseResolve(resolveValue);\n    }\n  }\n  const triggerClosePopup = instance => {\n    const popup = getPopup();\n    if (!popup) {\n      return false;\n    }\n    const innerParams = privateProps.innerParams.get(instance);\n    if (!innerParams || hasClass(popup, innerParams.hideClass.popup)) {\n      return false;\n    }\n    removeClass(popup, innerParams.showClass.popup);\n    addClass(popup, innerParams.hideClass.popup);\n    const backdrop = getContainer();\n    removeClass(backdrop, innerParams.showClass.backdrop);\n    addClass(backdrop, innerParams.hideClass.backdrop);\n    handlePopupAnimation(instance, popup, innerParams);\n    return true;\n  };\n\n  /**\n   * @param {any} error\n   */\n  function rejectPromise(error) {\n    const rejectPromise = privateMethods.swalPromiseReject.get(this);\n    handleAwaitingPromise(this);\n    if (rejectPromise) {\n      // Reject Swal promise\n      rejectPromise(error);\n    }\n  }\n\n  /**\n   * @param {SweetAlert} instance\n   */\n  const handleAwaitingPromise = instance => {\n    if (instance.isAwaitingPromise) {\n      delete instance.isAwaitingPromise;\n      // The instance might have been previously partly destroyed, we must resume the destroy process in this case #2335\n      if (!privateProps.innerParams.get(instance)) {\n        instance._destroy();\n      }\n    }\n  };\n\n  /**\n   * @param {any} resolveValue\n   * @returns {SweetAlertResult}\n   */\n  const prepareResolveValue = resolveValue => {\n    // When user calls Swal.close()\n    if (typeof resolveValue === 'undefined') {\n      return {\n        isConfirmed: false,\n        isDenied: false,\n        isDismissed: true\n      };\n    }\n    return Object.assign({\n      isConfirmed: false,\n      isDenied: false,\n      isDismissed: false\n    }, resolveValue);\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {HTMLElement} popup\n   * @param {SweetAlertOptions} innerParams\n   */\n  const handlePopupAnimation = (instance, popup, innerParams) => {\n    var _globalState$eventEmi;\n    const container = getContainer();\n    // If animation is supported, animate\n    const animationIsSupported = hasCssAnimation(popup);\n    if (typeof innerParams.willClose === 'function') {\n      innerParams.willClose(popup);\n    }\n    (_globalState$eventEmi = globalState.eventEmitter) === null || _globalState$eventEmi === void 0 || _globalState$eventEmi.emit('willClose', popup);\n    if (animationIsSupported) {\n      animatePopup(instance, popup, container, innerParams.returnFocus, innerParams.didClose);\n    } else {\n      // Otherwise, remove immediately\n      removePopupAndResetState(instance, container, innerParams.returnFocus, innerParams.didClose);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {HTMLElement} popup\n   * @param {HTMLElement} container\n   * @param {boolean} returnFocus\n   * @param {Function} didClose\n   */\n  const animatePopup = (instance, popup, container, returnFocus, didClose) => {\n    globalState.swalCloseEventFinishedCallback = removePopupAndResetState.bind(null, instance, container, returnFocus, didClose);\n    /**\n     * @param {AnimationEvent | TransitionEvent} e\n     */\n    const swalCloseAnimationFinished = function (e) {\n      if (e.target === popup) {\n        var _globalState$swalClos;\n        (_globalState$swalClos = globalState.swalCloseEventFinishedCallback) === null || _globalState$swalClos === void 0 || _globalState$swalClos.call(globalState);\n        delete globalState.swalCloseEventFinishedCallback;\n        popup.removeEventListener('animationend', swalCloseAnimationFinished);\n        popup.removeEventListener('transitionend', swalCloseAnimationFinished);\n      }\n    };\n    popup.addEventListener('animationend', swalCloseAnimationFinished);\n    popup.addEventListener('transitionend', swalCloseAnimationFinished);\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {Function} didClose\n   */\n  const triggerDidCloseAndDispose = (instance, didClose) => {\n    setTimeout(() => {\n      var _globalState$eventEmi2;\n      if (typeof didClose === 'function') {\n        didClose.bind(instance.params)();\n      }\n      (_globalState$eventEmi2 = globalState.eventEmitter) === null || _globalState$eventEmi2 === void 0 || _globalState$eventEmi2.emit('didClose');\n      // instance might have been destroyed already\n      if (instance._destroy) {\n        instance._destroy();\n      }\n    });\n  };\n\n  /**\n   * Shows loader (spinner), this is useful with AJAX requests.\n   * By default the loader be shown instead of the \"Confirm\" button.\n   *\n   * @param {HTMLButtonElement | null} [buttonToReplace]\n   */\n  const showLoading = buttonToReplace => {\n    let popup = getPopup();\n    if (!popup) {\n      new Swal();\n    }\n    popup = getPopup();\n    if (!popup) {\n      return;\n    }\n    const loader = getLoader();\n    if (isToast()) {\n      hide(getIcon());\n    } else {\n      replaceButton(popup, buttonToReplace);\n    }\n    show(loader);\n    popup.setAttribute('data-loading', 'true');\n    popup.setAttribute('aria-busy', 'true');\n    popup.focus();\n  };\n\n  /**\n   * @param {HTMLElement} popup\n   * @param {HTMLButtonElement | null} [buttonToReplace]\n   */\n  const replaceButton = (popup, buttonToReplace) => {\n    const actions = getActions();\n    const loader = getLoader();\n    if (!actions || !loader) {\n      return;\n    }\n    if (!buttonToReplace && isVisible$1(getConfirmButton())) {\n      buttonToReplace = getConfirmButton();\n    }\n    show(actions);\n    if (buttonToReplace) {\n      hide(buttonToReplace);\n      loader.setAttribute('data-button-to-replace', buttonToReplace.className);\n      actions.insertBefore(loader, buttonToReplace);\n    }\n    addClass([popup, actions], swalClasses.loading);\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const handleInputOptionsAndValue = (instance, params) => {\n    if (params.input === 'select' || params.input === 'radio') {\n      handleInputOptions(instance, params);\n    } else if (['text', 'email', 'number', 'tel', 'textarea'].some(i => i === params.input) && (hasToPromiseFn(params.inputValue) || isPromise(params.inputValue))) {\n      showLoading(getConfirmButton());\n      handleInputValue(instance, params);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} innerParams\n   * @returns {SweetAlertInputValue}\n   */\n  const getInputValue = (instance, innerParams) => {\n    const input = instance.getInput();\n    if (!input) {\n      return null;\n    }\n    switch (innerParams.input) {\n      case 'checkbox':\n        return getCheckboxValue(input);\n      case 'radio':\n        return getRadioValue(input);\n      case 'file':\n        return getFileValue(input);\n      default:\n        return innerParams.inputAutoTrim ? input.value.trim() : input.value;\n    }\n  };\n\n  /**\n   * @param {HTMLInputElement} input\n   * @returns {number}\n   */\n  const getCheckboxValue = input => input.checked ? 1 : 0;\n\n  /**\n   * @param {HTMLInputElement} input\n   * @returns {string | null}\n   */\n  const getRadioValue = input => input.checked ? input.value : null;\n\n  /**\n   * @param {HTMLInputElement} input\n   * @returns {FileList | File | null}\n   */\n  const getFileValue = input => input.files && input.files.length ? input.getAttribute('multiple') !== null ? input.files : input.files[0] : null;\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const handleInputOptions = (instance, params) => {\n    const popup = getPopup();\n    if (!popup) {\n      return;\n    }\n    /**\n     * @param {Record<string, any>} inputOptions\n     */\n    const processInputOptions = inputOptions => {\n      if (params.input === 'select') {\n        populateSelectOptions(popup, formatInputOptions(inputOptions), params);\n      } else if (params.input === 'radio') {\n        populateRadioOptions(popup, formatInputOptions(inputOptions), params);\n      }\n    };\n    if (hasToPromiseFn(params.inputOptions) || isPromise(params.inputOptions)) {\n      showLoading(getConfirmButton());\n      asPromise(params.inputOptions).then(inputOptions => {\n        instance.hideLoading();\n        processInputOptions(inputOptions);\n      });\n    } else if (typeof params.inputOptions === 'object') {\n      processInputOptions(params.inputOptions);\n    } else {\n      error(`Unexpected type of inputOptions! Expected object, Map or Promise, got ${typeof params.inputOptions}`);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertOptions} params\n   */\n  const handleInputValue = (instance, params) => {\n    const input = instance.getInput();\n    if (!input) {\n      return;\n    }\n    hide(input);\n    asPromise(params.inputValue).then(inputValue => {\n      input.value = params.input === 'number' ? `${parseFloat(inputValue) || 0}` : `${inputValue}`;\n      show(input);\n      input.focus();\n      instance.hideLoading();\n    }).catch(err => {\n      error(`Error in inputValue promise: ${err}`);\n      input.value = '';\n      show(input);\n      input.focus();\n      instance.hideLoading();\n    });\n  };\n\n  /**\n   * @param {HTMLElement} popup\n   * @param {InputOptionFlattened[]} inputOptions\n   * @param {SweetAlertOptions} params\n   */\n  function populateSelectOptions(popup, inputOptions, params) {\n    const select = getDirectChildByClass(popup, swalClasses.select);\n    if (!select) {\n      return;\n    }\n    /**\n     * @param {HTMLElement} parent\n     * @param {string} optionLabel\n     * @param {string} optionValue\n     */\n    const renderOption = (parent, optionLabel, optionValue) => {\n      const option = document.createElement('option');\n      option.value = optionValue;\n      setInnerHtml(option, optionLabel);\n      option.selected = isSelected(optionValue, params.inputValue);\n      parent.appendChild(option);\n    };\n    inputOptions.forEach(inputOption => {\n      const optionValue = inputOption[0];\n      const optionLabel = inputOption[1];\n      // <optgroup> spec:\n      // https://www.w3.org/TR/html401/interact/forms.html#h-17.6\n      // \"...all OPTGROUP elements must be specified directly within a SELECT element (i.e., groups may not be nested)...\"\n      // check whether this is a <optgroup>\n      if (Array.isArray(optionLabel)) {\n        // if it is an array, then it is an <optgroup>\n        const optgroup = document.createElement('optgroup');\n        optgroup.label = optionValue;\n        optgroup.disabled = false; // not configurable for now\n        select.appendChild(optgroup);\n        optionLabel.forEach(o => renderOption(optgroup, o[1], o[0]));\n      } else {\n        // case of <option>\n        renderOption(select, optionLabel, optionValue);\n      }\n    });\n    select.focus();\n  }\n\n  /**\n   * @param {HTMLElement} popup\n   * @param {InputOptionFlattened[]} inputOptions\n   * @param {SweetAlertOptions} params\n   */\n  function populateRadioOptions(popup, inputOptions, params) {\n    const radio = getDirectChildByClass(popup, swalClasses.radio);\n    if (!radio) {\n      return;\n    }\n    inputOptions.forEach(inputOption => {\n      const radioValue = inputOption[0];\n      const radioLabel = inputOption[1];\n      const radioInput = document.createElement('input');\n      const radioLabelElement = document.createElement('label');\n      radioInput.type = 'radio';\n      radioInput.name = swalClasses.radio;\n      radioInput.value = radioValue;\n      if (isSelected(radioValue, params.inputValue)) {\n        radioInput.checked = true;\n      }\n      const label = document.createElement('span');\n      setInnerHtml(label, radioLabel);\n      label.className = swalClasses.label;\n      radioLabelElement.appendChild(radioInput);\n      radioLabelElement.appendChild(label);\n      radio.appendChild(radioLabelElement);\n    });\n    const radios = radio.querySelectorAll('input');\n    if (radios.length) {\n      radios[0].focus();\n    }\n  }\n\n  /**\n   * Converts `inputOptions` into an array of `[value, label]`s\n   *\n   * @param {Record<string, any>} inputOptions\n   * @typedef {string[]} InputOptionFlattened\n   * @returns {InputOptionFlattened[]}\n   */\n  const formatInputOptions = inputOptions => {\n    /** @type {InputOptionFlattened[]} */\n    const result = [];\n    if (inputOptions instanceof Map) {\n      inputOptions.forEach((value, key) => {\n        let valueFormatted = value;\n        if (typeof valueFormatted === 'object') {\n          // case of <optgroup>\n          valueFormatted = formatInputOptions(valueFormatted);\n        }\n        result.push([key, valueFormatted]);\n      });\n    } else {\n      Object.keys(inputOptions).forEach(key => {\n        let valueFormatted = inputOptions[key];\n        if (typeof valueFormatted === 'object') {\n          // case of <optgroup>\n          valueFormatted = formatInputOptions(valueFormatted);\n        }\n        result.push([key, valueFormatted]);\n      });\n    }\n    return result;\n  };\n\n  /**\n   * @param {string} optionValue\n   * @param {SweetAlertInputValue} inputValue\n   * @returns {boolean}\n   */\n  const isSelected = (optionValue, inputValue) => {\n    return !!inputValue && inputValue.toString() === optionValue.toString();\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   */\n  const handleConfirmButtonClick = instance => {\n    const innerParams = privateProps.innerParams.get(instance);\n    instance.disableButtons();\n    if (innerParams.input) {\n      handleConfirmOrDenyWithInput(instance, 'confirm');\n    } else {\n      confirm(instance, true);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   */\n  const handleDenyButtonClick = instance => {\n    const innerParams = privateProps.innerParams.get(instance);\n    instance.disableButtons();\n    if (innerParams.returnInputValueOnDeny) {\n      handleConfirmOrDenyWithInput(instance, 'deny');\n    } else {\n      deny(instance, false);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {Function} dismissWith\n   */\n  const handleCancelButtonClick = (instance, dismissWith) => {\n    instance.disableButtons();\n    dismissWith(DismissReason.cancel);\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {'confirm' | 'deny'} type\n   */\n  const handleConfirmOrDenyWithInput = (instance, type) => {\n    const innerParams = privateProps.innerParams.get(instance);\n    if (!innerParams.input) {\n      error(`The \"input\" parameter is needed to be set when using returnInputValueOn${capitalizeFirstLetter(type)}`);\n      return;\n    }\n    const input = instance.getInput();\n    const inputValue = getInputValue(instance, innerParams);\n    if (innerParams.inputValidator) {\n      handleInputValidator(instance, inputValue, type);\n    } else if (input && !input.checkValidity()) {\n      instance.enableButtons();\n      instance.showValidationMessage(innerParams.validationMessage || input.validationMessage);\n    } else if (type === 'deny') {\n      deny(instance, inputValue);\n    } else {\n      confirm(instance, inputValue);\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {SweetAlertInputValue} inputValue\n   * @param {'confirm' | 'deny'} type\n   */\n  const handleInputValidator = (instance, inputValue, type) => {\n    const innerParams = privateProps.innerParams.get(instance);\n    instance.disableInput();\n    const validationPromise = Promise.resolve().then(() => asPromise(innerParams.inputValidator(inputValue, innerParams.validationMessage)));\n    validationPromise.then(validationMessage => {\n      instance.enableButtons();\n      instance.enableInput();\n      if (validationMessage) {\n        instance.showValidationMessage(validationMessage);\n      } else if (type === 'deny') {\n        deny(instance, inputValue);\n      } else {\n        confirm(instance, inputValue);\n      }\n    });\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {any} value\n   */\n  const deny = (instance, value) => {\n    const innerParams = privateProps.innerParams.get(instance || undefined);\n    if (innerParams.showLoaderOnDeny) {\n      showLoading(getDenyButton());\n    }\n    if (innerParams.preDeny) {\n      instance.isAwaitingPromise = true; // Flagging the instance as awaiting a promise so it's own promise's reject/resolve methods doesn't get destroyed until the result from this preDeny's promise is received\n      const preDenyPromise = Promise.resolve().then(() => asPromise(innerParams.preDeny(value, innerParams.validationMessage)));\n      preDenyPromise.then(preDenyValue => {\n        if (preDenyValue === false) {\n          instance.hideLoading();\n          handleAwaitingPromise(instance);\n        } else {\n          instance.close({\n            isDenied: true,\n            value: typeof preDenyValue === 'undefined' ? value : preDenyValue\n          });\n        }\n      }).catch(error => rejectWith(instance || undefined, error));\n    } else {\n      instance.close({\n        isDenied: true,\n        value\n      });\n    }\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {any} value\n   */\n  const succeedWith = (instance, value) => {\n    instance.close({\n      isConfirmed: true,\n      value\n    });\n  };\n\n  /**\n   *\n   * @param {SweetAlert} instance\n   * @param {string} error\n   */\n  const rejectWith = (instance, error) => {\n    instance.rejectPromise(error);\n  };\n\n  /**\n   *\n   * @param {SweetAlert} instance\n   * @param {any} value\n   */\n  const confirm = (instance, value) => {\n    const innerParams = privateProps.innerParams.get(instance || undefined);\n    if (innerParams.showLoaderOnConfirm) {\n      showLoading();\n    }\n    if (innerParams.preConfirm) {\n      instance.resetValidationMessage();\n      instance.isAwaitingPromise = true; // Flagging the instance as awaiting a promise so it's own promise's reject/resolve methods doesn't get destroyed until the result from this preConfirm's promise is received\n      const preConfirmPromise = Promise.resolve().then(() => asPromise(innerParams.preConfirm(value, innerParams.validationMessage)));\n      preConfirmPromise.then(preConfirmValue => {\n        if (isVisible$1(getValidationMessage()) || preConfirmValue === false) {\n          instance.hideLoading();\n          handleAwaitingPromise(instance);\n        } else {\n          succeedWith(instance, typeof preConfirmValue === 'undefined' ? value : preConfirmValue);\n        }\n      }).catch(error => rejectWith(instance || undefined, error));\n    } else {\n      succeedWith(instance, value);\n    }\n  };\n\n  /**\n   * Hides loader and shows back the button which was hidden by .showLoading()\n   */\n  function hideLoading() {\n    // do nothing if popup is closed\n    const innerParams = privateProps.innerParams.get(this);\n    if (!innerParams) {\n      return;\n    }\n    const domCache = privateProps.domCache.get(this);\n    hide(domCache.loader);\n    if (isToast()) {\n      if (innerParams.icon) {\n        show(getIcon());\n      }\n    } else {\n      showRelatedButton(domCache);\n    }\n    removeClass([domCache.popup, domCache.actions], swalClasses.loading);\n    domCache.popup.removeAttribute('aria-busy');\n    domCache.popup.removeAttribute('data-loading');\n    domCache.confirmButton.disabled = false;\n    domCache.denyButton.disabled = false;\n    domCache.cancelButton.disabled = false;\n  }\n  const showRelatedButton = domCache => {\n    const buttonToReplace = domCache.popup.getElementsByClassName(domCache.loader.getAttribute('data-button-to-replace'));\n    if (buttonToReplace.length) {\n      show(buttonToReplace[0], 'inline-block');\n    } else if (allButtonsAreHidden()) {\n      hide(domCache.actions);\n    }\n  };\n\n  /**\n   * Gets the input DOM node, this method works with input parameter.\n   *\n   * @returns {HTMLInputElement | null}\n   */\n  function getInput() {\n    const innerParams = privateProps.innerParams.get(this);\n    const domCache = privateProps.domCache.get(this);\n    if (!domCache) {\n      return null;\n    }\n    return getInput$1(domCache.popup, innerParams.input);\n  }\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {string[]} buttons\n   * @param {boolean} disabled\n   */\n  function setButtonsDisabled(instance, buttons, disabled) {\n    const domCache = privateProps.domCache.get(instance);\n    buttons.forEach(button => {\n      domCache[button].disabled = disabled;\n    });\n  }\n\n  /**\n   * @param {HTMLInputElement | null} input\n   * @param {boolean} disabled\n   */\n  function setInputDisabled(input, disabled) {\n    const popup = getPopup();\n    if (!popup || !input) {\n      return;\n    }\n    if (input.type === 'radio') {\n      /** @type {NodeListOf<HTMLInputElement>} */\n      const radios = popup.querySelectorAll(`[name=\"${swalClasses.radio}\"]`);\n      for (let i = 0; i < radios.length; i++) {\n        radios[i].disabled = disabled;\n      }\n    } else {\n      input.disabled = disabled;\n    }\n  }\n\n  /**\n   * Enable all the buttons\n   * @this {SweetAlert}\n   */\n  function enableButtons() {\n    setButtonsDisabled(this, ['confirmButton', 'denyButton', 'cancelButton'], false);\n  }\n\n  /**\n   * Disable all the buttons\n   * @this {SweetAlert}\n   */\n  function disableButtons() {\n    setButtonsDisabled(this, ['confirmButton', 'denyButton', 'cancelButton'], true);\n  }\n\n  /**\n   * Enable the input field\n   * @this {SweetAlert}\n   */\n  function enableInput() {\n    setInputDisabled(this.getInput(), false);\n  }\n\n  /**\n   * Disable the input field\n   * @this {SweetAlert}\n   */\n  function disableInput() {\n    setInputDisabled(this.getInput(), true);\n  }\n\n  /**\n   * Show block with validation message\n   *\n   * @param {string} error\n   * @this {SweetAlert}\n   */\n  function showValidationMessage(error) {\n    const domCache = privateProps.domCache.get(this);\n    const params = privateProps.innerParams.get(this);\n    setInnerHtml(domCache.validationMessage, error);\n    domCache.validationMessage.className = swalClasses['validation-message'];\n    if (params.customClass && params.customClass.validationMessage) {\n      addClass(domCache.validationMessage, params.customClass.validationMessage);\n    }\n    show(domCache.validationMessage);\n    const input = this.getInput();\n    if (input) {\n      input.setAttribute('aria-invalid', 'true');\n      input.setAttribute('aria-describedby', swalClasses['validation-message']);\n      focusInput(input);\n      addClass(input, swalClasses.inputerror);\n    }\n  }\n\n  /**\n   * Hide block with validation message\n   *\n   * @this {SweetAlert}\n   */\n  function resetValidationMessage() {\n    const domCache = privateProps.domCache.get(this);\n    if (domCache.validationMessage) {\n      hide(domCache.validationMessage);\n    }\n    const input = this.getInput();\n    if (input) {\n      input.removeAttribute('aria-invalid');\n      input.removeAttribute('aria-describedby');\n      removeClass(input, swalClasses.inputerror);\n    }\n  }\n\n  const defaultParams = {\n    title: '',\n    titleText: '',\n    text: '',\n    html: '',\n    footer: '',\n    icon: undefined,\n    iconColor: undefined,\n    iconHtml: undefined,\n    template: undefined,\n    toast: false,\n    draggable: false,\n    animation: true,\n    theme: 'light',\n    showClass: {\n      popup: 'swal2-show',\n      backdrop: 'swal2-backdrop-show',\n      icon: 'swal2-icon-show'\n    },\n    hideClass: {\n      popup: 'swal2-hide',\n      backdrop: 'swal2-backdrop-hide',\n      icon: 'swal2-icon-hide'\n    },\n    customClass: {},\n    target: 'body',\n    color: undefined,\n    backdrop: true,\n    heightAuto: true,\n    allowOutsideClick: true,\n    allowEscapeKey: true,\n    allowEnterKey: true,\n    stopKeydownPropagation: true,\n    keydownListenerCapture: false,\n    showConfirmButton: true,\n    showDenyButton: false,\n    showCancelButton: false,\n    preConfirm: undefined,\n    preDeny: undefined,\n    confirmButtonText: 'OK',\n    confirmButtonAriaLabel: '',\n    confirmButtonColor: undefined,\n    denyButtonText: 'No',\n    denyButtonAriaLabel: '',\n    denyButtonColor: undefined,\n    cancelButtonText: 'Cancel',\n    cancelButtonAriaLabel: '',\n    cancelButtonColor: undefined,\n    buttonsStyling: true,\n    reverseButtons: false,\n    focusConfirm: true,\n    focusDeny: false,\n    focusCancel: false,\n    returnFocus: true,\n    showCloseButton: false,\n    closeButtonHtml: '&times;',\n    closeButtonAriaLabel: 'Close this dialog',\n    loaderHtml: '',\n    showLoaderOnConfirm: false,\n    showLoaderOnDeny: false,\n    imageUrl: undefined,\n    imageWidth: undefined,\n    imageHeight: undefined,\n    imageAlt: '',\n    timer: undefined,\n    timerProgressBar: false,\n    width: undefined,\n    padding: undefined,\n    background: undefined,\n    input: undefined,\n    inputPlaceholder: '',\n    inputLabel: '',\n    inputValue: '',\n    inputOptions: {},\n    inputAutoFocus: true,\n    inputAutoTrim: true,\n    inputAttributes: {},\n    inputValidator: undefined,\n    returnInputValueOnDeny: false,\n    validationMessage: undefined,\n    grow: false,\n    position: 'center',\n    progressSteps: [],\n    currentProgressStep: undefined,\n    progressStepsDistance: undefined,\n    willOpen: undefined,\n    didOpen: undefined,\n    didRender: undefined,\n    willClose: undefined,\n    didClose: undefined,\n    didDestroy: undefined,\n    scrollbarPadding: true,\n    topLayer: false\n  };\n  const updatableParams = ['allowEscapeKey', 'allowOutsideClick', 'background', 'buttonsStyling', 'cancelButtonAriaLabel', 'cancelButtonColor', 'cancelButtonText', 'closeButtonAriaLabel', 'closeButtonHtml', 'color', 'confirmButtonAriaLabel', 'confirmButtonColor', 'confirmButtonText', 'currentProgressStep', 'customClass', 'denyButtonAriaLabel', 'denyButtonColor', 'denyButtonText', 'didClose', 'didDestroy', 'draggable', 'footer', 'hideClass', 'html', 'icon', 'iconColor', 'iconHtml', 'imageAlt', 'imageHeight', 'imageUrl', 'imageWidth', 'preConfirm', 'preDeny', 'progressSteps', 'returnFocus', 'reverseButtons', 'showCancelButton', 'showCloseButton', 'showConfirmButton', 'showDenyButton', 'text', 'title', 'titleText', 'theme', 'willClose'];\n\n  /** @type {Record<string, string | undefined>} */\n  const deprecatedParams = {\n    allowEnterKey: undefined\n  };\n  const toastIncompatibleParams = ['allowOutsideClick', 'allowEnterKey', 'backdrop', 'draggable', 'focusConfirm', 'focusDeny', 'focusCancel', 'returnFocus', 'heightAuto', 'keydownListenerCapture'];\n\n  /**\n   * Is valid parameter\n   *\n   * @param {string} paramName\n   * @returns {boolean}\n   */\n  const isValidParameter = paramName => {\n    return Object.prototype.hasOwnProperty.call(defaultParams, paramName);\n  };\n\n  /**\n   * Is valid parameter for Swal.update() method\n   *\n   * @param {string} paramName\n   * @returns {boolean}\n   */\n  const isUpdatableParameter = paramName => {\n    return updatableParams.indexOf(paramName) !== -1;\n  };\n\n  /**\n   * Is deprecated parameter\n   *\n   * @param {string} paramName\n   * @returns {string | undefined}\n   */\n  const isDeprecatedParameter = paramName => {\n    return deprecatedParams[paramName];\n  };\n\n  /**\n   * @param {string} param\n   */\n  const checkIfParamIsValid = param => {\n    if (!isValidParameter(param)) {\n      warn(`Unknown parameter \"${param}\"`);\n    }\n  };\n\n  /**\n   * @param {string} param\n   */\n  const checkIfToastParamIsValid = param => {\n    if (toastIncompatibleParams.includes(param)) {\n      warn(`The parameter \"${param}\" is incompatible with toasts`);\n    }\n  };\n\n  /**\n   * @param {string} param\n   */\n  const checkIfParamIsDeprecated = param => {\n    const isDeprecated = isDeprecatedParameter(param);\n    if (isDeprecated) {\n      warnAboutDeprecation(param, isDeprecated);\n    }\n  };\n\n  /**\n   * Show relevant warnings for given params\n   *\n   * @param {SweetAlertOptions} params\n   */\n  const showWarningsForParams = params => {\n    if (params.backdrop === false && params.allowOutsideClick) {\n      warn('\"allowOutsideClick\" parameter requires `backdrop` parameter to be set to `true`');\n    }\n    if (params.theme && !['light', 'dark', 'auto', 'minimal', 'borderless', 'embed-iframe', 'bulma', 'bulma-light', 'bulma-dark'].includes(params.theme)) {\n      warn(`Invalid theme \"${params.theme}\"`);\n    }\n    for (const param in params) {\n      checkIfParamIsValid(param);\n      if (params.toast) {\n        checkIfToastParamIsValid(param);\n      }\n      checkIfParamIsDeprecated(param);\n    }\n  };\n\n  /**\n   * Updates popup parameters.\n   *\n   * @param {SweetAlertOptions} params\n   */\n  function update(params) {\n    const container = getContainer();\n    const popup = getPopup();\n    const innerParams = privateProps.innerParams.get(this);\n    if (!popup || hasClass(popup, innerParams.hideClass.popup)) {\n      warn(`You're trying to update the closed or closing popup, that won't work. Use the update() method in preConfirm parameter or show a new popup.`);\n      return;\n    }\n    const validUpdatableParams = filterValidParams(params);\n    const updatedParams = Object.assign({}, innerParams, validUpdatableParams);\n    showWarningsForParams(updatedParams);\n    container.dataset['swal2Theme'] = updatedParams.theme;\n    render(this, updatedParams);\n    privateProps.innerParams.set(this, updatedParams);\n    Object.defineProperties(this, {\n      params: {\n        value: Object.assign({}, this.params, params),\n        writable: false,\n        enumerable: true\n      }\n    });\n  }\n\n  /**\n   * @param {SweetAlertOptions} params\n   * @returns {SweetAlertOptions}\n   */\n  const filterValidParams = params => {\n    const validUpdatableParams = {};\n    Object.keys(params).forEach(param => {\n      if (isUpdatableParameter(param)) {\n        validUpdatableParams[param] = params[param];\n      } else {\n        warn(`Invalid parameter to update: ${param}`);\n      }\n    });\n    return validUpdatableParams;\n  };\n\n  /**\n   * Dispose the current SweetAlert2 instance\n   */\n  function _destroy() {\n    const domCache = privateProps.domCache.get(this);\n    const innerParams = privateProps.innerParams.get(this);\n    if (!innerParams) {\n      disposeWeakMaps(this); // The WeakMaps might have been partly destroyed, we must recall it to dispose any remaining WeakMaps #2335\n      return; // This instance has already been destroyed\n    }\n\n    // Check if there is another Swal closing\n    if (domCache.popup && globalState.swalCloseEventFinishedCallback) {\n      globalState.swalCloseEventFinishedCallback();\n      delete globalState.swalCloseEventFinishedCallback;\n    }\n    if (typeof innerParams.didDestroy === 'function') {\n      innerParams.didDestroy();\n    }\n    globalState.eventEmitter.emit('didDestroy');\n    disposeSwal(this);\n  }\n\n  /**\n   * @param {SweetAlert} instance\n   */\n  const disposeSwal = instance => {\n    disposeWeakMaps(instance);\n    // Unset this.params so GC will dispose it (#1569)\n    delete instance.params;\n    // Unset globalState props so GC will dispose globalState (#1569)\n    delete globalState.keydownHandler;\n    delete globalState.keydownTarget;\n    // Unset currentInstance\n    delete globalState.currentInstance;\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   */\n  const disposeWeakMaps = instance => {\n    // If the current instance is awaiting a promise result, we keep the privateMethods to call them once the promise result is retrieved #2335\n    if (instance.isAwaitingPromise) {\n      unsetWeakMaps(privateProps, instance);\n      instance.isAwaitingPromise = true;\n    } else {\n      unsetWeakMaps(privateMethods, instance);\n      unsetWeakMaps(privateProps, instance);\n      delete instance.isAwaitingPromise;\n      // Unset instance methods\n      delete instance.disableButtons;\n      delete instance.enableButtons;\n      delete instance.getInput;\n      delete instance.disableInput;\n      delete instance.enableInput;\n      delete instance.hideLoading;\n      delete instance.disableLoading;\n      delete instance.showValidationMessage;\n      delete instance.resetValidationMessage;\n      delete instance.close;\n      delete instance.closePopup;\n      delete instance.closeModal;\n      delete instance.closeToast;\n      delete instance.rejectPromise;\n      delete instance.update;\n      delete instance._destroy;\n    }\n  };\n\n  /**\n   * @param {object} obj\n   * @param {SweetAlert} instance\n   */\n  const unsetWeakMaps = (obj, instance) => {\n    for (const i in obj) {\n      obj[i].delete(instance);\n    }\n  };\n\n  var instanceMethods = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    _destroy: _destroy,\n    close: close,\n    closeModal: close,\n    closePopup: close,\n    closeToast: close,\n    disableButtons: disableButtons,\n    disableInput: disableInput,\n    disableLoading: hideLoading,\n    enableButtons: enableButtons,\n    enableInput: enableInput,\n    getInput: getInput,\n    handleAwaitingPromise: handleAwaitingPromise,\n    hideLoading: hideLoading,\n    rejectPromise: rejectPromise,\n    resetValidationMessage: resetValidationMessage,\n    showValidationMessage: showValidationMessage,\n    update: update\n  });\n\n  /**\n   * @param {SweetAlertOptions} innerParams\n   * @param {DomCache} domCache\n   * @param {Function} dismissWith\n   */\n  const handlePopupClick = (innerParams, domCache, dismissWith) => {\n    if (innerParams.toast) {\n      handleToastClick(innerParams, domCache, dismissWith);\n    } else {\n      // Ignore click events that had mousedown on the popup but mouseup on the container\n      // This can happen when the user drags a slider\n      handleModalMousedown(domCache);\n\n      // Ignore click events that had mousedown on the container but mouseup on the popup\n      handleContainerMousedown(domCache);\n      handleModalClick(innerParams, domCache, dismissWith);\n    }\n  };\n\n  /**\n   * @param {SweetAlertOptions} innerParams\n   * @param {DomCache} domCache\n   * @param {Function} dismissWith\n   */\n  const handleToastClick = (innerParams, domCache, dismissWith) => {\n    // Closing toast by internal click\n    domCache.popup.onclick = () => {\n      if (innerParams && (isAnyButtonShown(innerParams) || innerParams.timer || innerParams.input)) {\n        return;\n      }\n      dismissWith(DismissReason.close);\n    };\n  };\n\n  /**\n   * @param {SweetAlertOptions} innerParams\n   * @returns {boolean}\n   */\n  const isAnyButtonShown = innerParams => {\n    return !!(innerParams.showConfirmButton || innerParams.showDenyButton || innerParams.showCancelButton || innerParams.showCloseButton);\n  };\n  let ignoreOutsideClick = false;\n\n  /**\n   * @param {DomCache} domCache\n   */\n  const handleModalMousedown = domCache => {\n    domCache.popup.onmousedown = () => {\n      domCache.container.onmouseup = function (e) {\n        domCache.container.onmouseup = () => {};\n        // We only check if the mouseup target is the container because usually it doesn't\n        // have any other direct children aside of the popup\n        if (e.target === domCache.container) {\n          ignoreOutsideClick = true;\n        }\n      };\n    };\n  };\n\n  /**\n   * @param {DomCache} domCache\n   */\n  const handleContainerMousedown = domCache => {\n    domCache.container.onmousedown = e => {\n      // prevent the modal text from being selected on double click on the container (allowOutsideClick: false)\n      if (e.target === domCache.container) {\n        e.preventDefault();\n      }\n      domCache.popup.onmouseup = function (e) {\n        domCache.popup.onmouseup = () => {};\n        // We also need to check if the mouseup target is a child of the popup\n        if (e.target === domCache.popup || e.target instanceof HTMLElement && domCache.popup.contains(e.target)) {\n          ignoreOutsideClick = true;\n        }\n      };\n    };\n  };\n\n  /**\n   * @param {SweetAlertOptions} innerParams\n   * @param {DomCache} domCache\n   * @param {Function} dismissWith\n   */\n  const handleModalClick = (innerParams, domCache, dismissWith) => {\n    domCache.container.onclick = e => {\n      if (ignoreOutsideClick) {\n        ignoreOutsideClick = false;\n        return;\n      }\n      if (e.target === domCache.container && callIfFunction(innerParams.allowOutsideClick)) {\n        dismissWith(DismissReason.backdrop);\n      }\n    };\n  };\n\n  const isJqueryElement = elem => typeof elem === 'object' && elem.jquery;\n  const isElement = elem => elem instanceof Element || isJqueryElement(elem);\n  const argsToParams = args => {\n    const params = {};\n    if (typeof args[0] === 'object' && !isElement(args[0])) {\n      Object.assign(params, args[0]);\n    } else {\n      ['title', 'html', 'icon'].forEach((name, index) => {\n        const arg = args[index];\n        if (typeof arg === 'string' || isElement(arg)) {\n          params[name] = arg;\n        } else if (arg !== undefined) {\n          error(`Unexpected type of ${name}! Expected \"string\" or \"Element\", got ${typeof arg}`);\n        }\n      });\n    }\n    return params;\n  };\n\n  /**\n   * Main method to create a new SweetAlert2 popup\n   *\n   * @param  {...SweetAlertOptions} args\n   * @returns {Promise<SweetAlertResult>}\n   */\n  function fire(...args) {\n    return new this(...args);\n  }\n\n  /**\n   * Returns an extended version of `Swal` containing `params` as defaults.\n   * Useful for reusing Swal configuration.\n   *\n   * For example:\n   *\n   * Before:\n   * const textPromptOptions = { input: 'text', showCancelButton: true }\n   * const {value: firstName} = await Swal.fire({ ...textPromptOptions, title: 'What is your first name?' })\n   * const {value: lastName} = await Swal.fire({ ...textPromptOptions, title: 'What is your last name?' })\n   *\n   * After:\n   * const TextPrompt = Swal.mixin({ input: 'text', showCancelButton: true })\n   * const {value: firstName} = await TextPrompt('What is your first name?')\n   * const {value: lastName} = await TextPrompt('What is your last name?')\n   *\n   * @param {SweetAlertOptions} mixinParams\n   * @returns {SweetAlert}\n   */\n  function mixin(mixinParams) {\n    class MixinSwal extends this {\n      _main(params, priorityMixinParams) {\n        return super._main(params, Object.assign({}, mixinParams, priorityMixinParams));\n      }\n    }\n    // @ts-ignore\n    return MixinSwal;\n  }\n\n  /**\n   * If `timer` parameter is set, returns number of milliseconds of timer remained.\n   * Otherwise, returns undefined.\n   *\n   * @returns {number | undefined}\n   */\n  const getTimerLeft = () => {\n    return globalState.timeout && globalState.timeout.getTimerLeft();\n  };\n\n  /**\n   * Stop timer. Returns number of milliseconds of timer remained.\n   * If `timer` parameter isn't set, returns undefined.\n   *\n   * @returns {number | undefined}\n   */\n  const stopTimer = () => {\n    if (globalState.timeout) {\n      stopTimerProgressBar();\n      return globalState.timeout.stop();\n    }\n  };\n\n  /**\n   * Resume timer. Returns number of milliseconds of timer remained.\n   * If `timer` parameter isn't set, returns undefined.\n   *\n   * @returns {number | undefined}\n   */\n  const resumeTimer = () => {\n    if (globalState.timeout) {\n      const remaining = globalState.timeout.start();\n      animateTimerProgressBar(remaining);\n      return remaining;\n    }\n  };\n\n  /**\n   * Resume timer. Returns number of milliseconds of timer remained.\n   * If `timer` parameter isn't set, returns undefined.\n   *\n   * @returns {number | undefined}\n   */\n  const toggleTimer = () => {\n    const timer = globalState.timeout;\n    return timer && (timer.running ? stopTimer() : resumeTimer());\n  };\n\n  /**\n   * Increase timer. Returns number of milliseconds of an updated timer.\n   * If `timer` parameter isn't set, returns undefined.\n   *\n   * @param {number} ms\n   * @returns {number | undefined}\n   */\n  const increaseTimer = ms => {\n    if (globalState.timeout) {\n      const remaining = globalState.timeout.increase(ms);\n      animateTimerProgressBar(remaining, true);\n      return remaining;\n    }\n  };\n\n  /**\n   * Check if timer is running. Returns true if timer is running\n   * or false if timer is paused or stopped.\n   * If `timer` parameter isn't set, returns undefined\n   *\n   * @returns {boolean}\n   */\n  const isTimerRunning = () => {\n    return !!(globalState.timeout && globalState.timeout.isRunning());\n  };\n\n  let bodyClickListenerAdded = false;\n  const clickHandlers = {};\n\n  /**\n   * @param {string} attr\n   */\n  function bindClickHandler(attr = 'data-swal-template') {\n    clickHandlers[attr] = this;\n    if (!bodyClickListenerAdded) {\n      document.body.addEventListener('click', bodyClickListener);\n      bodyClickListenerAdded = true;\n    }\n  }\n  const bodyClickListener = event => {\n    for (let el = event.target; el && el !== document; el = el.parentNode) {\n      for (const attr in clickHandlers) {\n        const template = el.getAttribute(attr);\n        if (template) {\n          clickHandlers[attr].fire({\n            template\n          });\n          return;\n        }\n      }\n    }\n  };\n\n  // Source: https://gist.github.com/mudge/5830382?permalink_comment_id=2691957#gistcomment-2691957\n\n  class EventEmitter {\n    constructor() {\n      /** @type {Events} */\n      this.events = {};\n    }\n\n    /**\n     * @param {string} eventName\n     * @returns {EventHandlers}\n     */\n    _getHandlersByEventName(eventName) {\n      if (typeof this.events[eventName] === 'undefined') {\n        // not Set because we need to keep the FIFO order\n        // https://github.com/sweetalert2/sweetalert2/pull/2763#discussion_r1748990334\n        this.events[eventName] = [];\n      }\n      return this.events[eventName];\n    }\n\n    /**\n     * @param {string} eventName\n     * @param {EventHandler} eventHandler\n     */\n    on(eventName, eventHandler) {\n      const currentHandlers = this._getHandlersByEventName(eventName);\n      if (!currentHandlers.includes(eventHandler)) {\n        currentHandlers.push(eventHandler);\n      }\n    }\n\n    /**\n     * @param {string} eventName\n     * @param {EventHandler} eventHandler\n     */\n    once(eventName, eventHandler) {\n      /**\n       * @param {Array} args\n       */\n      const onceFn = (...args) => {\n        this.removeListener(eventName, onceFn);\n        eventHandler.apply(this, args);\n      };\n      this.on(eventName, onceFn);\n    }\n\n    /**\n     * @param {string} eventName\n     * @param {Array} args\n     */\n    emit(eventName, ...args) {\n      this._getHandlersByEventName(eventName).forEach(\n      /**\n       * @param {EventHandler} eventHandler\n       */\n      eventHandler => {\n        try {\n          eventHandler.apply(this, args);\n        } catch (error) {\n          console.error(error);\n        }\n      });\n    }\n\n    /**\n     * @param {string} eventName\n     * @param {EventHandler} eventHandler\n     */\n    removeListener(eventName, eventHandler) {\n      const currentHandlers = this._getHandlersByEventName(eventName);\n      const index = currentHandlers.indexOf(eventHandler);\n      if (index > -1) {\n        currentHandlers.splice(index, 1);\n      }\n    }\n\n    /**\n     * @param {string} eventName\n     */\n    removeAllListeners(eventName) {\n      if (this.events[eventName] !== undefined) {\n        // https://github.com/sweetalert2/sweetalert2/pull/2763#discussion_r1749239222\n        this.events[eventName].length = 0;\n      }\n    }\n    reset() {\n      this.events = {};\n    }\n  }\n\n  globalState.eventEmitter = new EventEmitter();\n\n  /**\n   * @param {string} eventName\n   * @param {EventHandler} eventHandler\n   */\n  const on = (eventName, eventHandler) => {\n    globalState.eventEmitter.on(eventName, eventHandler);\n  };\n\n  /**\n   * @param {string} eventName\n   * @param {EventHandler} eventHandler\n   */\n  const once = (eventName, eventHandler) => {\n    globalState.eventEmitter.once(eventName, eventHandler);\n  };\n\n  /**\n   * @param {string} [eventName]\n   * @param {EventHandler} [eventHandler]\n   */\n  const off = (eventName, eventHandler) => {\n    // Remove all handlers for all events\n    if (!eventName) {\n      globalState.eventEmitter.reset();\n      return;\n    }\n    if (eventHandler) {\n      // Remove a specific handler\n      globalState.eventEmitter.removeListener(eventName, eventHandler);\n    } else {\n      // Remove all handlers for a specific event\n      globalState.eventEmitter.removeAllListeners(eventName);\n    }\n  };\n\n  var staticMethods = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    argsToParams: argsToParams,\n    bindClickHandler: bindClickHandler,\n    clickCancel: clickCancel,\n    clickConfirm: clickConfirm,\n    clickDeny: clickDeny,\n    enableLoading: showLoading,\n    fire: fire,\n    getActions: getActions,\n    getCancelButton: getCancelButton,\n    getCloseButton: getCloseButton,\n    getConfirmButton: getConfirmButton,\n    getContainer: getContainer,\n    getDenyButton: getDenyButton,\n    getFocusableElements: getFocusableElements,\n    getFooter: getFooter,\n    getHtmlContainer: getHtmlContainer,\n    getIcon: getIcon,\n    getIconContent: getIconContent,\n    getImage: getImage,\n    getInputLabel: getInputLabel,\n    getLoader: getLoader,\n    getPopup: getPopup,\n    getProgressSteps: getProgressSteps,\n    getTimerLeft: getTimerLeft,\n    getTimerProgressBar: getTimerProgressBar,\n    getTitle: getTitle,\n    getValidationMessage: getValidationMessage,\n    increaseTimer: increaseTimer,\n    isDeprecatedParameter: isDeprecatedParameter,\n    isLoading: isLoading,\n    isTimerRunning: isTimerRunning,\n    isUpdatableParameter: isUpdatableParameter,\n    isValidParameter: isValidParameter,\n    isVisible: isVisible,\n    mixin: mixin,\n    off: off,\n    on: on,\n    once: once,\n    resumeTimer: resumeTimer,\n    showLoading: showLoading,\n    stopTimer: stopTimer,\n    toggleTimer: toggleTimer\n  });\n\n  class Timer {\n    /**\n     * @param {Function} callback\n     * @param {number} delay\n     */\n    constructor(callback, delay) {\n      this.callback = callback;\n      this.remaining = delay;\n      this.running = false;\n      this.start();\n    }\n\n    /**\n     * @returns {number}\n     */\n    start() {\n      if (!this.running) {\n        this.running = true;\n        this.started = new Date();\n        this.id = setTimeout(this.callback, this.remaining);\n      }\n      return this.remaining;\n    }\n\n    /**\n     * @returns {number}\n     */\n    stop() {\n      if (this.started && this.running) {\n        this.running = false;\n        clearTimeout(this.id);\n        this.remaining -= new Date().getTime() - this.started.getTime();\n      }\n      return this.remaining;\n    }\n\n    /**\n     * @param {number} n\n     * @returns {number}\n     */\n    increase(n) {\n      const running = this.running;\n      if (running) {\n        this.stop();\n      }\n      this.remaining += n;\n      if (running) {\n        this.start();\n      }\n      return this.remaining;\n    }\n\n    /**\n     * @returns {number}\n     */\n    getTimerLeft() {\n      if (this.running) {\n        this.stop();\n        this.start();\n      }\n      return this.remaining;\n    }\n\n    /**\n     * @returns {boolean}\n     */\n    isRunning() {\n      return this.running;\n    }\n  }\n\n  const swalStringParams = ['swal-title', 'swal-html', 'swal-footer'];\n\n  /**\n   * @param {SweetAlertOptions} params\n   * @returns {SweetAlertOptions}\n   */\n  const getTemplateParams = params => {\n    const template = typeof params.template === 'string' ? (/** @type {HTMLTemplateElement} */document.querySelector(params.template)) : params.template;\n    if (!template) {\n      return {};\n    }\n    /** @type {DocumentFragment} */\n    const templateContent = template.content;\n    showWarningsForElements(templateContent);\n    const result = Object.assign(getSwalParams(templateContent), getSwalFunctionParams(templateContent), getSwalButtons(templateContent), getSwalImage(templateContent), getSwalIcon(templateContent), getSwalInput(templateContent), getSwalStringParams(templateContent, swalStringParams));\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @returns {Record<string, any>}\n   */\n  const getSwalParams = templateContent => {\n    /** @type {Record<string, any>} */\n    const result = {};\n    /** @type {HTMLElement[]} */\n    const swalParams = Array.from(templateContent.querySelectorAll('swal-param'));\n    swalParams.forEach(param => {\n      showWarningsForAttributes(param, ['name', 'value']);\n      const paramName = /** @type {keyof SweetAlertOptions} */param.getAttribute('name');\n      const value = param.getAttribute('value');\n      if (!paramName || !value) {\n        return;\n      }\n      if (typeof defaultParams[paramName] === 'boolean') {\n        result[paramName] = value !== 'false';\n      } else if (typeof defaultParams[paramName] === 'object') {\n        result[paramName] = JSON.parse(value);\n      } else {\n        result[paramName] = value;\n      }\n    });\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @returns {Record<string, any>}\n   */\n  const getSwalFunctionParams = templateContent => {\n    /** @type {Record<string, any>} */\n    const result = {};\n    /** @type {HTMLElement[]} */\n    const swalFunctions = Array.from(templateContent.querySelectorAll('swal-function-param'));\n    swalFunctions.forEach(param => {\n      const paramName = /** @type {keyof SweetAlertOptions} */param.getAttribute('name');\n      const value = param.getAttribute('value');\n      if (!paramName || !value) {\n        return;\n      }\n      result[paramName] = new Function(`return ${value}`)();\n    });\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @returns {Record<string, any>}\n   */\n  const getSwalButtons = templateContent => {\n    /** @type {Record<string, any>} */\n    const result = {};\n    /** @type {HTMLElement[]} */\n    const swalButtons = Array.from(templateContent.querySelectorAll('swal-button'));\n    swalButtons.forEach(button => {\n      showWarningsForAttributes(button, ['type', 'color', 'aria-label']);\n      const type = button.getAttribute('type');\n      if (!type || !['confirm', 'cancel', 'deny'].includes(type)) {\n        return;\n      }\n      result[`${type}ButtonText`] = button.innerHTML;\n      result[`show${capitalizeFirstLetter(type)}Button`] = true;\n      if (button.hasAttribute('color')) {\n        result[`${type}ButtonColor`] = button.getAttribute('color');\n      }\n      if (button.hasAttribute('aria-label')) {\n        result[`${type}ButtonAriaLabel`] = button.getAttribute('aria-label');\n      }\n    });\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @returns {Pick<SweetAlertOptions, 'imageUrl' | 'imageWidth' | 'imageHeight' | 'imageAlt'>}\n   */\n  const getSwalImage = templateContent => {\n    const result = {};\n    /** @type {HTMLElement | null} */\n    const image = templateContent.querySelector('swal-image');\n    if (image) {\n      showWarningsForAttributes(image, ['src', 'width', 'height', 'alt']);\n      if (image.hasAttribute('src')) {\n        result.imageUrl = image.getAttribute('src') || undefined;\n      }\n      if (image.hasAttribute('width')) {\n        result.imageWidth = image.getAttribute('width') || undefined;\n      }\n      if (image.hasAttribute('height')) {\n        result.imageHeight = image.getAttribute('height') || undefined;\n      }\n      if (image.hasAttribute('alt')) {\n        result.imageAlt = image.getAttribute('alt') || undefined;\n      }\n    }\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @returns {Record<string, any>}\n   */\n  const getSwalIcon = templateContent => {\n    const result = {};\n    /** @type {HTMLElement | null} */\n    const icon = templateContent.querySelector('swal-icon');\n    if (icon) {\n      showWarningsForAttributes(icon, ['type', 'color']);\n      if (icon.hasAttribute('type')) {\n        result.icon = icon.getAttribute('type');\n      }\n      if (icon.hasAttribute('color')) {\n        result.iconColor = icon.getAttribute('color');\n      }\n      result.iconHtml = icon.innerHTML;\n    }\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @returns {Record<string, any>}\n   */\n  const getSwalInput = templateContent => {\n    /** @type {Record<string, any>} */\n    const result = {};\n    /** @type {HTMLElement | null} */\n    const input = templateContent.querySelector('swal-input');\n    if (input) {\n      showWarningsForAttributes(input, ['type', 'label', 'placeholder', 'value']);\n      result.input = input.getAttribute('type') || 'text';\n      if (input.hasAttribute('label')) {\n        result.inputLabel = input.getAttribute('label');\n      }\n      if (input.hasAttribute('placeholder')) {\n        result.inputPlaceholder = input.getAttribute('placeholder');\n      }\n      if (input.hasAttribute('value')) {\n        result.inputValue = input.getAttribute('value');\n      }\n    }\n    /** @type {HTMLElement[]} */\n    const inputOptions = Array.from(templateContent.querySelectorAll('swal-input-option'));\n    if (inputOptions.length) {\n      result.inputOptions = {};\n      inputOptions.forEach(option => {\n        showWarningsForAttributes(option, ['value']);\n        const optionValue = option.getAttribute('value');\n        if (!optionValue) {\n          return;\n        }\n        const optionName = option.innerHTML;\n        result.inputOptions[optionValue] = optionName;\n      });\n    }\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   * @param {string[]} paramNames\n   * @returns {Record<string, any>}\n   */\n  const getSwalStringParams = (templateContent, paramNames) => {\n    /** @type {Record<string, any>} */\n    const result = {};\n    for (const i in paramNames) {\n      const paramName = paramNames[i];\n      /** @type {HTMLElement | null} */\n      const tag = templateContent.querySelector(paramName);\n      if (tag) {\n        showWarningsForAttributes(tag, []);\n        result[paramName.replace(/^swal-/, '')] = tag.innerHTML.trim();\n      }\n    }\n    return result;\n  };\n\n  /**\n   * @param {DocumentFragment} templateContent\n   */\n  const showWarningsForElements = templateContent => {\n    const allowedElements = swalStringParams.concat(['swal-param', 'swal-function-param', 'swal-button', 'swal-image', 'swal-icon', 'swal-input', 'swal-input-option']);\n    Array.from(templateContent.children).forEach(el => {\n      const tagName = el.tagName.toLowerCase();\n      if (!allowedElements.includes(tagName)) {\n        warn(`Unrecognized element <${tagName}>`);\n      }\n    });\n  };\n\n  /**\n   * @param {HTMLElement} el\n   * @param {string[]} allowedAttributes\n   */\n  const showWarningsForAttributes = (el, allowedAttributes) => {\n    Array.from(el.attributes).forEach(attribute => {\n      if (allowedAttributes.indexOf(attribute.name) === -1) {\n        warn([`Unrecognized attribute \"${attribute.name}\" on <${el.tagName.toLowerCase()}>.`, `${allowedAttributes.length ? `Allowed attributes are: ${allowedAttributes.join(', ')}` : 'To set the value, use HTML within the element.'}`]);\n      }\n    });\n  };\n\n  const SHOW_CLASS_TIMEOUT = 10;\n\n  /**\n   * Open popup, add necessary classes and styles, fix scrollbar\n   *\n   * @param {SweetAlertOptions} params\n   */\n  const openPopup = params => {\n    const container = getContainer();\n    const popup = getPopup();\n    if (typeof params.willOpen === 'function') {\n      params.willOpen(popup);\n    }\n    globalState.eventEmitter.emit('willOpen', popup);\n    const bodyStyles = window.getComputedStyle(document.body);\n    const initialBodyOverflow = bodyStyles.overflowY;\n    addClasses(container, popup, params);\n\n    // scrolling is 'hidden' until animation is done, after that 'auto'\n    setTimeout(() => {\n      setScrollingVisibility(container, popup);\n    }, SHOW_CLASS_TIMEOUT);\n    if (isModal()) {\n      fixScrollContainer(container, params.scrollbarPadding, initialBodyOverflow);\n      setAriaHidden();\n    }\n    if (!isToast() && !globalState.previousActiveElement) {\n      globalState.previousActiveElement = document.activeElement;\n    }\n    if (typeof params.didOpen === 'function') {\n      setTimeout(() => params.didOpen(popup));\n    }\n    globalState.eventEmitter.emit('didOpen', popup);\n    removeClass(container, swalClasses['no-transition']);\n  };\n\n  /**\n   * @param {AnimationEvent} event\n   */\n  const swalOpenAnimationFinished = event => {\n    const popup = getPopup();\n    if (event.target !== popup) {\n      return;\n    }\n    const container = getContainer();\n    popup.removeEventListener('animationend', swalOpenAnimationFinished);\n    popup.removeEventListener('transitionend', swalOpenAnimationFinished);\n    container.style.overflowY = 'auto';\n  };\n\n  /**\n   * @param {HTMLElement} container\n   * @param {HTMLElement} popup\n   */\n  const setScrollingVisibility = (container, popup) => {\n    if (hasCssAnimation(popup)) {\n      container.style.overflowY = 'hidden';\n      popup.addEventListener('animationend', swalOpenAnimationFinished);\n      popup.addEventListener('transitionend', swalOpenAnimationFinished);\n    } else {\n      container.style.overflowY = 'auto';\n    }\n  };\n\n  /**\n   * @param {HTMLElement} container\n   * @param {boolean} scrollbarPadding\n   * @param {string} initialBodyOverflow\n   */\n  const fixScrollContainer = (container, scrollbarPadding, initialBodyOverflow) => {\n    iOSfix();\n    if (scrollbarPadding && initialBodyOverflow !== 'hidden') {\n      replaceScrollbarWithPadding(initialBodyOverflow);\n    }\n\n    // sweetalert2/issues/1247\n    setTimeout(() => {\n      container.scrollTop = 0;\n    });\n  };\n\n  /**\n   * @param {HTMLElement} container\n   * @param {HTMLElement} popup\n   * @param {SweetAlertOptions} params\n   */\n  const addClasses = (container, popup, params) => {\n    addClass(container, params.showClass.backdrop);\n    if (params.animation) {\n      // this workaround with opacity is needed for https://github.com/sweetalert2/sweetalert2/issues/2059\n      popup.style.setProperty('opacity', '0', 'important');\n      show(popup, 'grid');\n      setTimeout(() => {\n        // Animate popup right after showing it\n        addClass(popup, params.showClass.popup);\n        // and remove the opacity workaround\n        popup.style.removeProperty('opacity');\n      }, SHOW_CLASS_TIMEOUT); // 10ms in order to fix #2062\n    } else {\n      show(popup, 'grid');\n    }\n    addClass([document.documentElement, document.body], swalClasses.shown);\n    if (params.heightAuto && params.backdrop && !params.toast) {\n      addClass([document.documentElement, document.body], swalClasses['height-auto']);\n    }\n  };\n\n  var defaultInputValidators = {\n    /**\n     * @param {string} string\n     * @param {string} [validationMessage]\n     * @returns {Promise<string | void>}\n     */\n    email: (string, validationMessage) => {\n      return /^[a-zA-Z0-9.+_'-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z0-9-]+$/.test(string) ? Promise.resolve() : Promise.resolve(validationMessage || 'Invalid email address');\n    },\n    /**\n     * @param {string} string\n     * @param {string} [validationMessage]\n     * @returns {Promise<string | void>}\n     */\n    url: (string, validationMessage) => {\n      // taken from https://stackoverflow.com/a/3809435 with a small change from #1306 and #2013\n      return /^https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\\.[a-z]{2,63}\\b([-a-zA-Z0-9@:%_+.~#?&/=]*)$/.test(string) ? Promise.resolve() : Promise.resolve(validationMessage || 'Invalid URL');\n    }\n  };\n\n  /**\n   * @param {SweetAlertOptions} params\n   */\n  function setDefaultInputValidators(params) {\n    // Use default `inputValidator` for supported input types if not provided\n    if (params.inputValidator) {\n      return;\n    }\n    if (params.input === 'email') {\n      params.inputValidator = defaultInputValidators['email'];\n    }\n    if (params.input === 'url') {\n      params.inputValidator = defaultInputValidators['url'];\n    }\n  }\n\n  /**\n   * @param {SweetAlertOptions} params\n   */\n  function validateCustomTargetElement(params) {\n    // Determine if the custom target element is valid\n    if (!params.target || typeof params.target === 'string' && !document.querySelector(params.target) || typeof params.target !== 'string' && !params.target.appendChild) {\n      warn('Target parameter is not valid, defaulting to \"body\"');\n      params.target = 'body';\n    }\n  }\n\n  /**\n   * Set type, text and actions on popup\n   *\n   * @param {SweetAlertOptions} params\n   */\n  function setParameters(params) {\n    setDefaultInputValidators(params);\n\n    // showLoaderOnConfirm && preConfirm\n    if (params.showLoaderOnConfirm && !params.preConfirm) {\n      warn('showLoaderOnConfirm is set to true, but preConfirm is not defined.\\n' + 'showLoaderOnConfirm should be used together with preConfirm, see usage example:\\n' + 'https://sweetalert2.github.io/#ajax-request');\n    }\n    validateCustomTargetElement(params);\n\n    // Replace newlines with <br> in title\n    if (typeof params.title === 'string') {\n      params.title = params.title.split('\\n').join('<br />');\n    }\n    init(params);\n  }\n\n  /** @type {SweetAlert} */\n  let currentInstance;\n  var _promise = /*#__PURE__*/new WeakMap();\n  class SweetAlert {\n    /**\n     * @param {...any} args\n     * @this {SweetAlert}\n     */\n    constructor(...args) {\n      /**\n       * @type {Promise<SweetAlertResult>}\n       */\n      _classPrivateFieldInitSpec(this, _promise, void 0);\n      // Prevent run in Node env\n      if (typeof window === 'undefined') {\n        return;\n      }\n      currentInstance = this;\n\n      // @ts-ignore\n      const outerParams = Object.freeze(this.constructor.argsToParams(args));\n\n      /** @type {Readonly<SweetAlertOptions>} */\n      this.params = outerParams;\n\n      /** @type {boolean} */\n      this.isAwaitingPromise = false;\n      _classPrivateFieldSet2(_promise, this, this._main(currentInstance.params));\n    }\n    _main(userParams, mixinParams = {}) {\n      showWarningsForParams(Object.assign({}, mixinParams, userParams));\n      if (globalState.currentInstance) {\n        const swalPromiseResolve = privateMethods.swalPromiseResolve.get(globalState.currentInstance);\n        const {\n          isAwaitingPromise\n        } = globalState.currentInstance;\n        globalState.currentInstance._destroy();\n        if (!isAwaitingPromise) {\n          swalPromiseResolve({\n            isDismissed: true\n          });\n        }\n        if (isModal()) {\n          unsetAriaHidden();\n        }\n      }\n      globalState.currentInstance = currentInstance;\n      const innerParams = prepareParams(userParams, mixinParams);\n      setParameters(innerParams);\n      Object.freeze(innerParams);\n\n      // clear the previous timer\n      if (globalState.timeout) {\n        globalState.timeout.stop();\n        delete globalState.timeout;\n      }\n\n      // clear the restore focus timeout\n      clearTimeout(globalState.restoreFocusTimeout);\n      const domCache = populateDomCache(currentInstance);\n      render(currentInstance, innerParams);\n      privateProps.innerParams.set(currentInstance, innerParams);\n      return swalPromise(currentInstance, domCache, innerParams);\n    }\n\n    // `catch` cannot be the name of a module export, so we define our thenable methods here instead\n    then(onFulfilled) {\n      return _classPrivateFieldGet2(_promise, this).then(onFulfilled);\n    }\n    finally(onFinally) {\n      return _classPrivateFieldGet2(_promise, this).finally(onFinally);\n    }\n  }\n\n  /**\n   * @param {SweetAlert} instance\n   * @param {DomCache} domCache\n   * @param {SweetAlertOptions} innerParams\n   * @returns {Promise}\n   */\n  const swalPromise = (instance, domCache, innerParams) => {\n    return new Promise((resolve, reject) => {\n      // functions to handle all closings/dismissals\n      /**\n       * @param {DismissReason} dismiss\n       */\n      const dismissWith = dismiss => {\n        instance.close({\n          isDismissed: true,\n          dismiss\n        });\n      };\n      privateMethods.swalPromiseResolve.set(instance, resolve);\n      privateMethods.swalPromiseReject.set(instance, reject);\n      domCache.confirmButton.onclick = () => {\n        handleConfirmButtonClick(instance);\n      };\n      domCache.denyButton.onclick = () => {\n        handleDenyButtonClick(instance);\n      };\n      domCache.cancelButton.onclick = () => {\n        handleCancelButtonClick(instance, dismissWith);\n      };\n      domCache.closeButton.onclick = () => {\n        dismissWith(DismissReason.close);\n      };\n      handlePopupClick(innerParams, domCache, dismissWith);\n      addKeydownHandler(globalState, innerParams, dismissWith);\n      handleInputOptionsAndValue(instance, innerParams);\n      openPopup(innerParams);\n      setupTimer(globalState, innerParams, dismissWith);\n      initFocus(domCache, innerParams);\n\n      // Scroll container to top on open (#1247, #1946)\n      setTimeout(() => {\n        domCache.container.scrollTop = 0;\n      });\n    });\n  };\n\n  /**\n   * @param {SweetAlertOptions} userParams\n   * @param {SweetAlertOptions} mixinParams\n   * @returns {SweetAlertOptions}\n   */\n  const prepareParams = (userParams, mixinParams) => {\n    const templateParams = getTemplateParams(userParams);\n    const params = Object.assign({}, defaultParams, mixinParams, templateParams, userParams); // precedence is described in #2131\n    params.showClass = Object.assign({}, defaultParams.showClass, params.showClass);\n    params.hideClass = Object.assign({}, defaultParams.hideClass, params.hideClass);\n    if (params.animation === false) {\n      params.showClass = {\n        backdrop: 'swal2-noanimation'\n      };\n      params.hideClass = {};\n    }\n    return params;\n  };\n\n  /**\n   * @param {SweetAlert} instance\n   * @returns {DomCache}\n   */\n  const populateDomCache = instance => {\n    const domCache = {\n      popup: getPopup(),\n      container: getContainer(),\n      actions: getActions(),\n      confirmButton: getConfirmButton(),\n      denyButton: getDenyButton(),\n      cancelButton: getCancelButton(),\n      loader: getLoader(),\n      closeButton: getCloseButton(),\n      validationMessage: getValidationMessage(),\n      progressSteps: getProgressSteps()\n    };\n    privateProps.domCache.set(instance, domCache);\n    return domCache;\n  };\n\n  /**\n   * @param {GlobalState} globalState\n   * @param {SweetAlertOptions} innerParams\n   * @param {Function} dismissWith\n   */\n  const setupTimer = (globalState, innerParams, dismissWith) => {\n    const timerProgressBar = getTimerProgressBar();\n    hide(timerProgressBar);\n    if (innerParams.timer) {\n      globalState.timeout = new Timer(() => {\n        dismissWith('timer');\n        delete globalState.timeout;\n      }, innerParams.timer);\n      if (innerParams.timerProgressBar) {\n        show(timerProgressBar);\n        applyCustomClass(timerProgressBar, innerParams, 'timerProgressBar');\n        setTimeout(() => {\n          if (globalState.timeout && globalState.timeout.running) {\n            // timer can be already stopped or unset at this point\n            animateTimerProgressBar(innerParams.timer);\n          }\n        });\n      }\n    }\n  };\n\n  /**\n   * Initialize focus in the popup:\n   *\n   * 1. If `toast` is `true`, don't steal focus from the document.\n   * 2. Else if there is an [autofocus] element, focus it.\n   * 3. Else if `focusConfirm` is `true` and confirm button is visible, focus it.\n   * 4. Else if `focusDeny` is `true` and deny button is visible, focus it.\n   * 5. Else if `focusCancel` is `true` and cancel button is visible, focus it.\n   * 6. Else focus the first focusable element in a popup (if any).\n   *\n   * @param {DomCache} domCache\n   * @param {SweetAlertOptions} innerParams\n   */\n  const initFocus = (domCache, innerParams) => {\n    if (innerParams.toast) {\n      return;\n    }\n    // TODO: this is dumb, remove `allowEnterKey` param in the next major version\n    if (!callIfFunction(innerParams.allowEnterKey)) {\n      warnAboutDeprecation('allowEnterKey');\n      blurActiveElement();\n      return;\n    }\n    if (focusAutofocus(domCache)) {\n      return;\n    }\n    if (focusButton(domCache, innerParams)) {\n      return;\n    }\n    setFocus(-1, 1);\n  };\n\n  /**\n   * @param {DomCache} domCache\n   * @returns {boolean}\n   */\n  const focusAutofocus = domCache => {\n    const autofocusElements = Array.from(domCache.popup.querySelectorAll('[autofocus]'));\n    for (const autofocusElement of autofocusElements) {\n      if (autofocusElement instanceof HTMLElement && isVisible$1(autofocusElement)) {\n        autofocusElement.focus();\n        return true;\n      }\n    }\n    return false;\n  };\n\n  /**\n   * @param {DomCache} domCache\n   * @param {SweetAlertOptions} innerParams\n   * @returns {boolean}\n   */\n  const focusButton = (domCache, innerParams) => {\n    if (innerParams.focusDeny && isVisible$1(domCache.denyButton)) {\n      domCache.denyButton.focus();\n      return true;\n    }\n    if (innerParams.focusCancel && isVisible$1(domCache.cancelButton)) {\n      domCache.cancelButton.focus();\n      return true;\n    }\n    if (innerParams.focusConfirm && isVisible$1(domCache.confirmButton)) {\n      domCache.confirmButton.focus();\n      return true;\n    }\n    return false;\n  };\n  const blurActiveElement = () => {\n    if (document.activeElement instanceof HTMLElement && typeof document.activeElement.blur === 'function') {\n      document.activeElement.blur();\n    }\n  };\n\n  // Dear russian users visiting russian sites. Let's have fun.\n  if (typeof window !== 'undefined' && /^ru\\b/.test(navigator.language) && location.host.match(/\\.(ru|su|by|xn--p1ai)$/)) {\n    const now = new Date();\n    const initiationDate = localStorage.getItem('swal-initiation');\n    if (!initiationDate) {\n      localStorage.setItem('swal-initiation', `${now}`);\n    } else if ((now.getTime() - Date.parse(initiationDate)) / (1000 * 60 * 60 * 24) > 3) {\n      setTimeout(() => {\n        document.body.style.pointerEvents = 'none';\n        const ukrainianAnthem = document.createElement('audio');\n        ukrainianAnthem.src = 'https://flag-gimn.ru/wp-content/uploads/2021/09/Ukraina.mp3';\n        ukrainianAnthem.loop = true;\n        document.body.appendChild(ukrainianAnthem);\n        setTimeout(() => {\n          ukrainianAnthem.play().catch(() => {\n            // ignore\n          });\n        }, 2500);\n      }, 500);\n    }\n  }\n\n  // Assign instance methods from src/instanceMethods/*.js to prototype\n  SweetAlert.prototype.disableButtons = disableButtons;\n  SweetAlert.prototype.enableButtons = enableButtons;\n  SweetAlert.prototype.getInput = getInput;\n  SweetAlert.prototype.disableInput = disableInput;\n  SweetAlert.prototype.enableInput = enableInput;\n  SweetAlert.prototype.hideLoading = hideLoading;\n  SweetAlert.prototype.disableLoading = hideLoading;\n  SweetAlert.prototype.showValidationMessage = showValidationMessage;\n  SweetAlert.prototype.resetValidationMessage = resetValidationMessage;\n  SweetAlert.prototype.close = close;\n  SweetAlert.prototype.closePopup = close;\n  SweetAlert.prototype.closeModal = close;\n  SweetAlert.prototype.closeToast = close;\n  SweetAlert.prototype.rejectPromise = rejectPromise;\n  SweetAlert.prototype.update = update;\n  SweetAlert.prototype._destroy = _destroy;\n\n  // Assign static methods from src/staticMethods/*.js to constructor\n  Object.assign(SweetAlert, staticMethods);\n\n  // Proxy to instance methods to constructor, for now, for backwards compatibility\n  Object.keys(instanceMethods).forEach(key => {\n    /**\n     * @param {...any} args\n     * @returns {any | undefined}\n     */\n    SweetAlert[key] = function (...args) {\n      if (currentInstance && currentInstance[key]) {\n        return currentInstance[key](...args);\n      }\n      return null;\n    };\n  });\n  SweetAlert.DismissReason = DismissReason;\n  SweetAlert.version = '11.22.0';\n\n  const Swal = SweetAlert;\n  // @ts-ignore\n  Swal.default = Swal;\n\n  return Swal;\n\n}));\nif (typeof this !== 'undefined' && this.Sweetalert2){this.swal = this.sweetAlert = this.Swal = this.SweetAlert = this.Sweetalert2}\n\"undefined\"!=typeof document&&function(e,t){var n=e.createElement(\"style\");if(e.getElementsByTagName(\"head\")[0].appendChild(n),n.styleSheet)n.styleSheet.disabled||(n.styleSheet.cssText=t);else try{n.innerHTML=t}catch(e){n.innerText=t}}(document,\":root{--swal2-outline: 0 0 0 3px rgba(100, 150, 200, 0.5);--swal2-container-padding: 0.625em;--swal2-backdrop: rgba(0, 0, 0, 0.4);--swal2-backdrop-transition: background-color 0.1s;--swal2-width: 32em;--swal2-padding: 0 0 1.25em;--swal2-border: none;--swal2-border-radius: 0.3125rem;--swal2-background: white;--swal2-color: #545454;--swal2-show-animation: swal2-show 0.3s;--swal2-hide-animation: swal2-hide 0.15s forwards;--swal2-icon-zoom: 1;--swal2-icon-animations: true;--swal2-title-padding: 0.8em 1em 0;--swal2-html-container-padding: 1em 1.6em 0.3em;--swal2-input-border: 1px solid #d9d9d9;--swal2-input-border-radius: 0.1875em;--swal2-input-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px transparent;--swal2-input-background: transparent;--swal2-input-transition: border-color 0.2s, box-shadow 0.2s;--swal2-input-hover-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px transparent;--swal2-input-focus-border: 1px solid #b4dbed;--swal2-input-focus-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px $swal2-outline-color;--swal2-progress-step-background: #add8e6;--swal2-validation-message-background: #f0f0f0;--swal2-validation-message-color: #666;--swal2-footer-border-color: #eee;--swal2-footer-background: transparent;--swal2-footer-color: inherit;--swal2-close-button-position: initial;--swal2-close-button-inset: auto;--swal2-close-button-font-size: 2.5em;--swal2-close-button-color: #ccc;--swal2-close-button-transition: color 0.2s, box-shadow 0.2s;--swal2-close-button-outline: initial;--swal2-close-button-box-shadow: inset 0 0 0 3px transparent;--swal2-close-button-focus-box-shadow: inset var(--swal2-outline);--swal2-close-button-hover-transform: none;--swal2-actions-justify-content: center;--swal2-actions-width: auto;--swal2-actions-margin: 1.25em auto 0;--swal2-actions-padding: 0;--swal2-actions-border-radius: 0;--swal2-actions-background: transparent;--swal2-action-button-transition: background-color 0.2s, box-shadow 0.2s;--swal2-action-button-hover: black 10%;--swal2-action-button-active: black 10%;--swal2-confirm-button-box-shadow: none;--swal2-confirm-button-border-radius: 0.25em;--swal2-confirm-button-background-color: #7066e0;--swal2-confirm-button-color: #fff;--swal2-deny-button-box-shadow: none;--swal2-deny-button-border-radius: 0.25em;--swal2-deny-button-background-color: #dc3741;--swal2-deny-button-color: #fff;--swal2-cancel-button-box-shadow: none;--swal2-cancel-button-border-radius: 0.25em;--swal2-cancel-button-background-color: #6e7881;--swal2-cancel-button-color: #fff;--swal2-toast-show-animation: swal2-toast-show 0.5s;--swal2-toast-hide-animation: swal2-toast-hide 0.1s forwards;--swal2-toast-border: none;--swal2-toast-box-shadow: 0 0 1px hsl(0deg 0% 0% / 0.075), 0 1px 2px hsl(0deg 0% 0% / 0.075), 1px 2px 4px hsl(0deg 0% 0% / 0.075), 1px 3px 8px hsl(0deg 0% 0% / 0.075), 2px 4px 16px hsl(0deg 0% 0% / 0.075)}[data-swal2-theme=dark]{--swal2-dark-theme-black: #19191a;--swal2-dark-theme-white: #e1e1e1;--swal2-background: var(--swal2-dark-theme-black);--swal2-color: var(--swal2-dark-theme-white);--swal2-footer-border-color: #555;--swal2-input-background: color-mix(in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10%);--swal2-validation-message-background: color-mix( in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10% );--swal2-validation-message-color: var(--swal2-dark-theme-white)}@media(prefers-color-scheme: dark){[data-swal2-theme=auto]{--swal2-dark-theme-black: #19191a;--swal2-dark-theme-white: #e1e1e1;--swal2-background: var(--swal2-dark-theme-black);--swal2-color: var(--swal2-dark-theme-white);--swal2-footer-border-color: #555;--swal2-input-background: color-mix(in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10%);--swal2-validation-message-background: color-mix( in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10% );--swal2-validation-message-color: var(--swal2-dark-theme-white)}}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown){overflow:hidden}body.swal2-height-auto{height:auto !important}body.swal2-no-backdrop .swal2-container{background-color:rgba(0,0,0,0) !important;pointer-events:none}body.swal2-no-backdrop .swal2-container .swal2-popup{pointer-events:all}body.swal2-no-backdrop .swal2-container .swal2-modal{box-shadow:0 0 10px var(--swal2-backdrop)}body.swal2-toast-shown .swal2-container{box-sizing:border-box;width:360px;max-width:100%;background-color:rgba(0,0,0,0);pointer-events:none}body.swal2-toast-shown .swal2-container.swal2-top{inset:0 auto auto 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-top-end,body.swal2-toast-shown .swal2-container.swal2-top-right{inset:0 0 auto auto}body.swal2-toast-shown .swal2-container.swal2-top-start,body.swal2-toast-shown .swal2-container.swal2-top-left{inset:0 auto auto 0}body.swal2-toast-shown .swal2-container.swal2-center-start,body.swal2-toast-shown .swal2-container.swal2-center-left{inset:50% auto auto 0;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-center{inset:50% auto auto 50%;transform:translate(-50%, -50%)}body.swal2-toast-shown .swal2-container.swal2-center-end,body.swal2-toast-shown .swal2-container.swal2-center-right{inset:50% 0 auto auto;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-start,body.swal2-toast-shown .swal2-container.swal2-bottom-left{inset:auto auto 0 0}body.swal2-toast-shown .swal2-container.swal2-bottom{inset:auto auto 0 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-end,body.swal2-toast-shown .swal2-container.swal2-bottom-right{inset:auto 0 0 auto}@media print{body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown){overflow-y:scroll !important}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown)>[aria-hidden=true]{display:none}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown) .swal2-container{position:static !important}}div:where(.swal2-container){display:grid;position:fixed;z-index:1060;inset:0;box-sizing:border-box;grid-template-areas:\\\"top-start     top            top-end\\\" \\\"center-start  center         center-end\\\" \\\"bottom-start  bottom-center  bottom-end\\\";grid-template-rows:minmax(min-content, auto) minmax(min-content, auto) minmax(min-content, auto);height:100%;padding:var(--swal2-container-padding);overflow-x:hidden;transition:var(--swal2-backdrop-transition);-webkit-overflow-scrolling:touch}div:where(.swal2-container).swal2-backdrop-show,div:where(.swal2-container).swal2-noanimation{background:var(--swal2-backdrop)}div:where(.swal2-container).swal2-backdrop-hide{background:rgba(0,0,0,0) !important}div:where(.swal2-container).swal2-top-start,div:where(.swal2-container).swal2-center-start,div:where(.swal2-container).swal2-bottom-start{grid-template-columns:minmax(0, 1fr) auto auto}div:where(.swal2-container).swal2-top,div:where(.swal2-container).swal2-center,div:where(.swal2-container).swal2-bottom{grid-template-columns:auto minmax(0, 1fr) auto}div:where(.swal2-container).swal2-top-end,div:where(.swal2-container).swal2-center-end,div:where(.swal2-container).swal2-bottom-end{grid-template-columns:auto auto minmax(0, 1fr)}div:where(.swal2-container).swal2-top-start>.swal2-popup{align-self:start}div:where(.swal2-container).swal2-top>.swal2-popup{grid-column:2;place-self:start center}div:where(.swal2-container).swal2-top-end>.swal2-popup,div:where(.swal2-container).swal2-top-right>.swal2-popup{grid-column:3;place-self:start end}div:where(.swal2-container).swal2-center-start>.swal2-popup,div:where(.swal2-container).swal2-center-left>.swal2-popup{grid-row:2;align-self:center}div:where(.swal2-container).swal2-center>.swal2-popup{grid-column:2;grid-row:2;place-self:center center}div:where(.swal2-container).swal2-center-end>.swal2-popup,div:where(.swal2-container).swal2-center-right>.swal2-popup{grid-column:3;grid-row:2;place-self:center end}div:where(.swal2-container).swal2-bottom-start>.swal2-popup,div:where(.swal2-container).swal2-bottom-left>.swal2-popup{grid-column:1;grid-row:3;align-self:end}div:where(.swal2-container).swal2-bottom>.swal2-popup{grid-column:2;grid-row:3;place-self:end center}div:where(.swal2-container).swal2-bottom-end>.swal2-popup,div:where(.swal2-container).swal2-bottom-right>.swal2-popup{grid-column:3;grid-row:3;place-self:end end}div:where(.swal2-container).swal2-grow-row>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-column:1/4;width:100%}div:where(.swal2-container).swal2-grow-column>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-row:1/4;align-self:stretch}div:where(.swal2-container).swal2-no-transition{transition:none !important}div:where(.swal2-container)[popover]{width:auto;border:0}div:where(.swal2-container) div:where(.swal2-popup){display:none;position:relative;box-sizing:border-box;grid-template-columns:minmax(0, 100%);width:var(--swal2-width);max-width:100%;padding:var(--swal2-padding);border:var(--swal2-border);border-radius:var(--swal2-border-radius);background:var(--swal2-background);color:var(--swal2-color);font-family:inherit;font-size:1rem;container-name:swal2-popup}div:where(.swal2-container) div:where(.swal2-popup):focus{outline:none}div:where(.swal2-container) div:where(.swal2-popup).swal2-loading{overflow-y:hidden}div:where(.swal2-container) div:where(.swal2-popup).swal2-draggable{cursor:grab}div:where(.swal2-container) div:where(.swal2-popup).swal2-draggable div:where(.swal2-icon){cursor:grab}div:where(.swal2-container) div:where(.swal2-popup).swal2-dragging{cursor:grabbing}div:where(.swal2-container) div:where(.swal2-popup).swal2-dragging div:where(.swal2-icon){cursor:grabbing}div:where(.swal2-container) h2:where(.swal2-title){position:relative;max-width:100%;margin:0;padding:var(--swal2-title-padding);color:inherit;font-size:1.875em;font-weight:600;text-align:center;text-transform:none;word-wrap:break-word;cursor:initial}div:where(.swal2-container) div:where(.swal2-actions){display:flex;z-index:1;box-sizing:border-box;flex-wrap:wrap;align-items:center;justify-content:var(--swal2-actions-justify-content);width:var(--swal2-actions-width);margin:var(--swal2-actions-margin);padding:var(--swal2-actions-padding);border-radius:var(--swal2-actions-border-radius);background:var(--swal2-actions-background)}div:where(.swal2-container) div:where(.swal2-loader){display:none;align-items:center;justify-content:center;width:2.2em;height:2.2em;margin:0 1.875em;animation:swal2-rotate-loading 1.5s linear 0s infinite normal;border-width:.25em;border-style:solid;border-radius:100%;border-color:#2778c4 rgba(0,0,0,0) #2778c4 rgba(0,0,0,0)}div:where(.swal2-container) button:where(.swal2-styled){margin:.3125em;padding:.625em 1.1em;transition:var(--swal2-action-button-transition);border:none;box-shadow:0 0 0 3px rgba(0,0,0,0);font-weight:500}div:where(.swal2-container) button:where(.swal2-styled):not([disabled]){cursor:pointer}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm){border-radius:var(--swal2-confirm-button-border-radius);background:initial;background-color:var(--swal2-confirm-button-background-color);box-shadow:var(--swal2-confirm-button-box-shadow);color:var(--swal2-confirm-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm):hover{background-color:color-mix(in srgb, var(--swal2-confirm-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm):active{background-color:color-mix(in srgb, var(--swal2-confirm-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny){border-radius:var(--swal2-deny-button-border-radius);background:initial;background-color:var(--swal2-deny-button-background-color);box-shadow:var(--swal2-deny-button-box-shadow);color:var(--swal2-deny-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny):hover{background-color:color-mix(in srgb, var(--swal2-deny-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny):active{background-color:color-mix(in srgb, var(--swal2-deny-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel){border-radius:var(--swal2-cancel-button-border-radius);background:initial;background-color:var(--swal2-cancel-button-background-color);box-shadow:var(--swal2-cancel-button-box-shadow);color:var(--swal2-cancel-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel):hover{background-color:color-mix(in srgb, var(--swal2-cancel-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel):active{background-color:color-mix(in srgb, var(--swal2-cancel-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):focus-visible{outline:none;box-shadow:var(--swal2-action-button-focus-box-shadow)}div:where(.swal2-container) button:where(.swal2-styled)[disabled]:not(.swal2-loading){opacity:.4}div:where(.swal2-container) button:where(.swal2-styled)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-footer){margin:1em 0 0;padding:1em 1em 0;border-top:1px solid var(--swal2-footer-border-color);background:var(--swal2-footer-background);color:var(--swal2-footer-color);font-size:1em;text-align:center;cursor:initial}div:where(.swal2-container) .swal2-timer-progress-bar-container{position:absolute;right:0;bottom:0;left:0;grid-column:auto !important;overflow:hidden;border-bottom-right-radius:var(--swal2-border-radius);border-bottom-left-radius:var(--swal2-border-radius)}div:where(.swal2-container) div:where(.swal2-timer-progress-bar){width:100%;height:.25em;background:rgba(0,0,0,.2)}div:where(.swal2-container) img:where(.swal2-image){max-width:100%;margin:2em auto 1em;cursor:initial}div:where(.swal2-container) button:where(.swal2-close){position:var(--swal2-close-button-position);inset:var(--swal2-close-button-inset);z-index:2;align-items:center;justify-content:center;width:1.2em;height:1.2em;margin-top:0;margin-right:0;margin-bottom:-1.2em;padding:0;overflow:hidden;transition:var(--swal2-close-button-transition);border:none;border-radius:var(--swal2-border-radius);outline:var(--swal2-close-button-outline);background:rgba(0,0,0,0);color:var(--swal2-close-button-color);font-family:monospace;font-size:var(--swal2-close-button-font-size);cursor:pointer;justify-self:end}div:where(.swal2-container) button:where(.swal2-close):hover{transform:var(--swal2-close-button-hover-transform);background:rgba(0,0,0,0);color:#f27474}div:where(.swal2-container) button:where(.swal2-close):focus-visible{outline:none;box-shadow:var(--swal2-close-button-focus-box-shadow)}div:where(.swal2-container) button:where(.swal2-close)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-html-container){z-index:1;justify-content:center;margin:0;padding:var(--swal2-html-container-padding);overflow:auto;color:inherit;font-size:1.125em;font-weight:normal;line-height:normal;text-align:center;word-wrap:break-word;word-break:break-word;cursor:initial}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea),div:where(.swal2-container) select:where(.swal2-select),div:where(.swal2-container) div:where(.swal2-radio),div:where(.swal2-container) label:where(.swal2-checkbox){margin:1em 2em 3px}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea){box-sizing:border-box;width:auto;transition:var(--swal2-input-transition);border:var(--swal2-input-border);border-radius:var(--swal2-input-border-radius);background:var(--swal2-input-background);box-shadow:var(--swal2-input-box-shadow);color:inherit;font-size:1.125em}div:where(.swal2-container) input:where(.swal2-input).swal2-inputerror,div:where(.swal2-container) input:where(.swal2-file).swal2-inputerror,div:where(.swal2-container) textarea:where(.swal2-textarea).swal2-inputerror{border-color:#f27474 !important;box-shadow:0 0 2px #f27474 !important}div:where(.swal2-container) input:where(.swal2-input):hover,div:where(.swal2-container) input:where(.swal2-file):hover,div:where(.swal2-container) textarea:where(.swal2-textarea):hover{box-shadow:var(--swal2-input-hover-box-shadow)}div:where(.swal2-container) input:where(.swal2-input):focus,div:where(.swal2-container) input:where(.swal2-file):focus,div:where(.swal2-container) textarea:where(.swal2-textarea):focus{border:var(--swal2-input-focus-border);outline:none;box-shadow:var(--swal2-input-focus-box-shadow)}div:where(.swal2-container) input:where(.swal2-input)::placeholder,div:where(.swal2-container) input:where(.swal2-file)::placeholder,div:where(.swal2-container) textarea:where(.swal2-textarea)::placeholder{color:#ccc}div:where(.swal2-container) .swal2-range{margin:1em 2em 3px;background:var(--swal2-background)}div:where(.swal2-container) .swal2-range input{width:80%}div:where(.swal2-container) .swal2-range output{width:20%;color:inherit;font-weight:600;text-align:center}div:where(.swal2-container) .swal2-range input,div:where(.swal2-container) .swal2-range output{height:2.625em;padding:0;font-size:1.125em;line-height:2.625em}div:where(.swal2-container) .swal2-input{height:2.625em;padding:0 .75em}div:where(.swal2-container) .swal2-file{width:75%;margin-right:auto;margin-left:auto;background:var(--swal2-input-background);font-size:1.125em}div:where(.swal2-container) .swal2-textarea{height:6.75em;padding:.75em}div:where(.swal2-container) .swal2-select{min-width:50%;max-width:100%;padding:.375em .625em;background:var(--swal2-input-background);color:inherit;font-size:1.125em}div:where(.swal2-container) .swal2-radio,div:where(.swal2-container) .swal2-checkbox{align-items:center;justify-content:center;background:var(--swal2-background);color:inherit}div:where(.swal2-container) .swal2-radio label,div:where(.swal2-container) .swal2-checkbox label{margin:0 .6em;font-size:1.125em}div:where(.swal2-container) .swal2-radio input,div:where(.swal2-container) .swal2-checkbox input{flex-shrink:0;margin:0 .4em}div:where(.swal2-container) label:where(.swal2-input-label){display:flex;justify-content:center;margin:1em auto 0}div:where(.swal2-container) div:where(.swal2-validation-message){align-items:center;justify-content:center;margin:1em 0 0;padding:.625em;overflow:hidden;background:var(--swal2-validation-message-background);color:var(--swal2-validation-message-color);font-size:1em;font-weight:300}div:where(.swal2-container) div:where(.swal2-validation-message)::before{content:\\\"!\\\";display:inline-block;width:1.5em;min-width:1.5em;height:1.5em;margin:0 .625em;border-radius:50%;background-color:#f27474;color:#fff;font-weight:600;line-height:1.5em;text-align:center}div:where(.swal2-container) .swal2-progress-steps{flex-wrap:wrap;align-items:center;max-width:100%;margin:1.25em auto;padding:0;background:rgba(0,0,0,0);font-weight:600}div:where(.swal2-container) .swal2-progress-steps li{display:inline-block;position:relative}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step{z-index:20;flex-shrink:0;width:2em;height:2em;border-radius:2em;background:#2778c4;color:#fff;line-height:2em;text-align:center}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step{background:#2778c4}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step{background:var(--swal2-progress-step-background);color:#fff}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step-line{background:var(--swal2-progress-step-background)}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step-line{z-index:10;flex-shrink:0;width:2.5em;height:.4em;margin:0 -1px;background:#2778c4}div:where(.swal2-icon){position:relative;box-sizing:content-box;justify-content:center;width:5em;height:5em;margin:2.5em auto .6em;zoom:var(--swal2-icon-zoom);border:.25em solid rgba(0,0,0,0);border-radius:50%;border-color:#000;font-family:inherit;line-height:5em;cursor:default;user-select:none}div:where(.swal2-icon) .swal2-icon-content{display:flex;align-items:center;font-size:3.75em}div:where(.swal2-icon).swal2-error{border-color:#f27474;color:#f27474}div:where(.swal2-icon).swal2-error .swal2-x-mark{position:relative;flex-grow:1}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line]{display:block;position:absolute;top:2.3125em;width:2.9375em;height:.3125em;border-radius:.125em;background-color:#f27474}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=left]{left:1.0625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=right]{right:1em;transform:rotate(-45deg)}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-error.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-error.swal2-icon-show .swal2-x-mark{animation:swal2-animate-error-x-mark .5s}}div:where(.swal2-icon).swal2-warning{border-color:#f8bb86;color:#f8bb86}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-warning.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-warning.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .5s}}div:where(.swal2-icon).swal2-info{border-color:#3fc3ee;color:#3fc3ee}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-info.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-info.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .8s}}div:where(.swal2-icon).swal2-question{border-color:#87adbd;color:#87adbd}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-question.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-question.swal2-icon-show .swal2-icon-content{animation:swal2-animate-question-mark .8s}}div:where(.swal2-icon).swal2-success{border-color:#a5dc86;color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line]{position:absolute;width:3.75em;height:7.5em;border-radius:50%}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.4375em;left:-2.0635em;transform:rotate(-45deg);transform-origin:3.75em 3.75em;border-radius:7.5em 0 0 7.5em}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.6875em;left:1.875em;transform:rotate(-45deg);transform-origin:0 3.75em;border-radius:0 7.5em 7.5em 0}div:where(.swal2-icon).swal2-success .swal2-success-ring{position:absolute;z-index:2;top:-0.25em;left:-0.25em;box-sizing:content-box;width:100%;height:100%;border:.25em solid rgba(165,220,134,.3);border-radius:50%}div:where(.swal2-icon).swal2-success .swal2-success-fix{position:absolute;z-index:1;top:.5em;left:1.625em;width:.4375em;height:5.625em;transform:rotate(-45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line]{display:block;position:absolute;z-index:2;height:.3125em;border-radius:.125em;background-color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=tip]{top:2.875em;left:.8125em;width:1.5625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=long]{top:2.375em;right:.5em;width:2.9375em;transform:rotate(-45deg)}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-animate-success-line-tip .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-animate-success-line-long .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-circular-line-right{animation:swal2-rotate-success-circular-line 4.25s ease-in}}[class^=swal2]{-webkit-tap-highlight-color:rgba(0,0,0,0)}.swal2-show{animation:var(--swal2-show-animation)}.swal2-hide{animation:var(--swal2-hide-animation)}.swal2-noanimation{transition:none}.swal2-scrollbar-measure{position:absolute;top:-9999px;width:50px;height:50px;overflow:scroll}.swal2-rtl .swal2-close{margin-right:initial;margin-left:0}.swal2-rtl .swal2-timer-progress-bar{right:0;left:auto}.swal2-toast{box-sizing:border-box;grid-column:1/4 !important;grid-row:1/4 !important;grid-template-columns:min-content auto min-content;padding:1em;overflow-y:hidden;border:var(--swal2-toast-border);background:var(--swal2-background);box-shadow:var(--swal2-toast-box-shadow);pointer-events:all}.swal2-toast>*{grid-column:2}.swal2-toast h2:where(.swal2-title){margin:.5em 1em;padding:0;font-size:1em;text-align:initial}.swal2-toast .swal2-loading{justify-content:center}.swal2-toast input:where(.swal2-input){height:2em;margin:.5em;font-size:1em}.swal2-toast .swal2-validation-message{font-size:1em}.swal2-toast div:where(.swal2-footer){margin:.5em 0 0;padding:.5em 0 0;font-size:.8em}.swal2-toast button:where(.swal2-close){grid-column:3/3;grid-row:1/99;align-self:center;width:.8em;height:.8em;margin:0;font-size:2em}.swal2-toast div:where(.swal2-html-container){margin:.5em 1em;padding:0;overflow:initial;font-size:1em;text-align:initial}.swal2-toast div:where(.swal2-html-container):empty{padding:0}.swal2-toast .swal2-loader{grid-column:1;grid-row:1/99;align-self:center;width:2em;height:2em;margin:.25em}.swal2-toast .swal2-icon{grid-column:1;grid-row:1/99;align-self:center;width:2em;min-width:2em;height:2em;margin:0 .5em 0 0}.swal2-toast .swal2-icon .swal2-icon-content{display:flex;align-items:center;font-size:1.8em;font-weight:bold}.swal2-toast .swal2-icon.swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line]{top:.875em;width:1.375em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left]{left:.3125em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right]{right:.3125em}.swal2-toast div:where(.swal2-actions){justify-content:flex-start;height:auto;margin:0;margin-top:.5em;padding:0 .5em}.swal2-toast button:where(.swal2-styled){margin:.25em .5em;padding:.4em .6em;font-size:1em}.swal2-toast .swal2-success{border-color:#a5dc86}.swal2-toast .swal2-success [class^=swal2-success-circular-line]{position:absolute;width:1.6em;height:3em;border-radius:50%}.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.8em;left:-0.5em;transform:rotate(-45deg);transform-origin:2em 2em;border-radius:4em 0 0 4em}.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.25em;left:.9375em;transform-origin:0 1.5em;border-radius:0 4em 4em 0}.swal2-toast .swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-toast .swal2-success .swal2-success-fix{top:0;left:.4375em;width:.4375em;height:2.6875em}.swal2-toast .swal2-success [class^=swal2-success-line]{height:.3125em}.swal2-toast .swal2-success [class^=swal2-success-line][class$=tip]{top:1.125em;left:.1875em;width:.75em}.swal2-toast .swal2-success [class^=swal2-success-line][class$=long]{top:.9375em;right:.1875em;width:1.375em}@container swal2-popup style(--swal2-icon-animations:true){.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-toast-animate-success-line-tip .75s}.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-toast-animate-success-line-long .75s}}.swal2-toast.swal2-show{animation:var(--swal2-toast-show-animation)}.swal2-toast.swal2-hide{animation:var(--swal2-toast-hide-animation)}@keyframes swal2-show{0%{transform:scale(0.7)}45%{transform:scale(1.05)}80%{transform:scale(0.95)}100%{transform:scale(1)}}@keyframes swal2-hide{0%{transform:scale(1);opacity:1}100%{transform:scale(0.5);opacity:0}}@keyframes swal2-animate-success-line-tip{0%{top:1.1875em;left:.0625em;width:0}54%{top:1.0625em;left:.125em;width:0}70%{top:2.1875em;left:-0.375em;width:3.125em}84%{top:3em;left:1.3125em;width:1.0625em}100%{top:2.8125em;left:.8125em;width:1.5625em}}@keyframes swal2-animate-success-line-long{0%{top:3.375em;right:2.875em;width:0}65%{top:3.375em;right:2.875em;width:0}84%{top:2.1875em;right:0;width:3.4375em}100%{top:2.375em;right:.5em;width:2.9375em}}@keyframes swal2-rotate-success-circular-line{0%{transform:rotate(-45deg)}5%{transform:rotate(-45deg)}12%{transform:rotate(-405deg)}100%{transform:rotate(-405deg)}}@keyframes swal2-animate-error-x-mark{0%{margin-top:1.625em;transform:scale(0.4);opacity:0}50%{margin-top:1.625em;transform:scale(0.4);opacity:0}80%{margin-top:-0.375em;transform:scale(1.15)}100%{margin-top:0;transform:scale(1);opacity:1}}@keyframes swal2-animate-error-icon{0%{transform:rotateX(100deg);opacity:0}100%{transform:rotateX(0deg);opacity:1}}@keyframes swal2-rotate-loading{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes swal2-animate-question-mark{0%{transform:rotateY(-360deg)}100%{transform:rotateY(0)}}@keyframes swal2-animate-i-mark{0%{transform:rotateZ(45deg);opacity:0}25%{transform:rotateZ(-25deg);opacity:.4}50%{transform:rotateZ(15deg);opacity:.8}75%{transform:rotateZ(-5deg);opacity:1}100%{transform:rotateX(0);opacity:1}}@keyframes swal2-toast-show{0%{transform:translateY(-0.625em) rotateZ(2deg)}33%{transform:translateY(0) rotateZ(-2deg)}66%{transform:translateY(0.3125em) rotateZ(2deg)}100%{transform:translateY(0) rotateZ(0deg)}}@keyframes swal2-toast-hide{100%{transform:rotateZ(1deg);opacity:0}}@keyframes swal2-toast-animate-success-line-tip{0%{top:.5625em;left:.0625em;width:0}54%{top:.125em;left:.125em;width:0}70%{top:.625em;left:-0.25em;width:1.625em}84%{top:1.0625em;left:.75em;width:.5em}100%{top:1.125em;left:.1875em;width:.75em}}@keyframes swal2-toast-animate-success-line-long{0%{top:1.625em;right:1.375em;width:0}65%{top:1.25em;right:.9375em;width:0}84%{top:.9375em;right:0;width:1.125em}100%{top:.9375em;right:.1875em;width:1.375em}}\");"], "mappings": "AAAA;AACA;AACA;AACA;AACA,CAAC,UAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,CAACD,OAAO,GAAGD,OAAO,CAAC,CAAC,GACzF,OAAOG,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAACH,OAAO,CAAC,IAC3DD,MAAM,GAAG,OAAOM,UAAU,KAAK,WAAW,GAAGA,UAAU,GAAGN,MAAM,IAAIO,IAAI,EAAEP,MAAM,CAACQ,WAAW,GAAGP,OAAO,CAAC,CAAC,CAAC;AAC5G,CAAC,EAAE,IAAI,EAAG,YAAY;EAAE,YAAY;;EAElC,SAASQ,iBAAiBA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;IAClC,IAAI,UAAU,IAAI,OAAOF,CAAC,GAAGA,CAAC,KAAKC,CAAC,GAAGD,CAAC,CAACG,GAAG,CAACF,CAAC,CAAC,EAAE,OAAOG,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGJ,CAAC,GAAGC,CAAC;IACpF,MAAM,IAAII,SAAS,CAAC,+CAA+C,CAAC;EACtE;EACA,SAASC,0BAA0BA,CAACP,CAAC,EAAEC,CAAC,EAAE;IACxC,IAAIA,CAAC,CAACE,GAAG,CAACH,CAAC,CAAC,EAAE,MAAM,IAAIM,SAAS,CAAC,gEAAgE,CAAC;EACrG;EACA,SAASE,sBAAsBA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACpC,OAAOD,CAAC,CAACE,GAAG,CAACZ,iBAAiB,CAACU,CAAC,EAAEC,CAAC,CAAC,CAAC;EACvC;EACA,SAASE,0BAA0BA,CAACZ,CAAC,EAAEC,CAAC,EAAES,CAAC,EAAE;IAC3CH,0BAA0B,CAACP,CAAC,EAAEC,CAAC,CAAC,EAAEA,CAAC,CAACY,GAAG,CAACb,CAAC,EAAEU,CAAC,CAAC;EAC/C;EACA,SAASI,sBAAsBA,CAACL,CAAC,EAAEC,CAAC,EAAEK,CAAC,EAAE;IACvC,OAAON,CAAC,CAACI,GAAG,CAACd,iBAAiB,CAACU,CAAC,EAAEC,CAAC,CAAC,EAAEK,CAAC,CAAC,EAAEA,CAAC;EAC7C;EAEA,MAAMC,qBAAqB,GAAG,GAAG;;EAEjC;EACA,MAAMC,WAAW,GAAG,CAAC,CAAC;EACtB,MAAMC,0BAA0B,GAAGA,CAAA,KAAM;IACvC,IAAID,WAAW,CAACE,qBAAqB,YAAYC,WAAW,EAAE;MAC5DH,WAAW,CAACE,qBAAqB,CAACE,KAAK,CAAC,CAAC;MACzCJ,WAAW,CAACE,qBAAqB,GAAG,IAAI;IAC1C,CAAC,MAAM,IAAIG,QAAQ,CAACC,IAAI,EAAE;MACxBD,QAAQ,CAACC,IAAI,CAACF,KAAK,CAAC,CAAC;IACvB;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;EACE,MAAMG,oBAAoB,GAAGC,WAAW,IAAI;IAC1C,OAAO,IAAIC,OAAO,CAACC,OAAO,IAAI;MAC5B,IAAI,CAACF,WAAW,EAAE;QAChB,OAAOE,OAAO,CAAC,CAAC;MAClB;MACA,MAAMC,CAAC,GAAGC,MAAM,CAACC,OAAO;MACxB,MAAMC,CAAC,GAAGF,MAAM,CAACG,OAAO;MACxBf,WAAW,CAACgB,mBAAmB,GAAGC,UAAU,CAAC,MAAM;QACjDhB,0BAA0B,CAAC,CAAC;QAC5BS,OAAO,CAAC,CAAC;MACX,CAAC,EAAEX,qBAAqB,CAAC,CAAC,CAAC;;MAE3Ba,MAAM,CAACM,QAAQ,CAACP,CAAC,EAAEG,CAAC,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMK,UAAU,GAAG,QAAQ;;EAE3B;AACF;AACA;;EAEE;AACF;AACA;AACA;;EAEE;EACA,MAAMC,UAAU,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,aAAa,EAAE,eAAe,EAAE,OAAO,EAAE,aAAa,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,gBAAgB,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,aAAa,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,sBAAsB,EAAE,eAAe,EAAE,oBAAoB,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,cAAc,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc,EAAE,QAAQ,EAAE,cAAc,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc,EAAE,UAAU,EAAE,aAAa,EAAE,iBAAiB,EAAE,KAAK,EAAE,oBAAoB,EAAE,8BAA8B,EAAE,mBAAmB,EAAE,cAAc,EAAE,cAAc,EAAE,WAAW,EAAE,eAAe,EAAE,YAAY,EAAE,WAAW,EAAE,UAAU,CAAC;EAC/6B,MAAMC,WAAW,GAAGD,UAAU,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,SAAS,KAAK;IACxDD,GAAG,CAACC,SAAS,CAAC,GAAGL,UAAU,GAAGK,SAAS;IACvC,OAAOD,GAAG;EACZ,CAAC,EAAE,0BAA0B,CAAC,CAAC,CAAC;;EAEhC;EACA,MAAME,KAAK,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC;EACjE,MAAMC,SAAS,GAAGD,KAAK,CAACH,MAAM,CAAC,CAACC,GAAG,EAAEI,IAAI,KAAK;IAC5CJ,GAAG,CAACI,IAAI,CAAC,GAAGR,UAAU,GAAGQ,IAAI;IAC7B,OAAOJ,GAAG;EACZ,CAAC,EAAE,wBAAwB,CAAC,CAAC,CAAC;EAE9B,MAAMK,aAAa,GAAG,cAAc;;EAEpC;AACF;AACA;AACA;AACA;AACA;EACE,MAAMC,qBAAqB,GAAGC,GAAG,IAAIA,GAAG,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,GAAG,CAACG,KAAK,CAAC,CAAC,CAAC;;EAE/E;AACF;AACA;AACA;AACA;EACE,MAAMC,IAAI,GAAGC,OAAO,IAAI;IACtBC,OAAO,CAACF,IAAI,CAAC,GAAGN,aAAa,IAAI,OAAOO,OAAO,KAAK,QAAQ,GAAGA,OAAO,CAACE,IAAI,CAAC,GAAG,CAAC,GAAGF,OAAO,EAAE,CAAC;EAC/F,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMG,KAAK,GAAGH,OAAO,IAAI;IACvBC,OAAO,CAACE,KAAK,CAAC,GAAGV,aAAa,IAAIO,OAAO,EAAE,CAAC;EAC9C,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;EACE,MAAMI,wBAAwB,GAAG,EAAE;;EAEnC;AACF;AACA;AACA;AACA;EACE,MAAMC,QAAQ,GAAGL,OAAO,IAAI;IAC1B,IAAI,CAACI,wBAAwB,CAACE,QAAQ,CAACN,OAAO,CAAC,EAAE;MAC/CI,wBAAwB,CAACG,IAAI,CAACP,OAAO,CAAC;MACtCD,IAAI,CAACC,OAAO,CAAC;IACf;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;EACE,MAAMQ,oBAAoB,GAAGA,CAACC,eAAe,EAAEC,UAAU,GAAG,IAAI,KAAK;IACnEL,QAAQ,CAAC,IAAII,eAAe,iEAAiEC,UAAU,GAAG,SAASA,UAAU,YAAY,GAAG,EAAE,EAAE,CAAC;EACnJ,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAMC,cAAc,GAAGC,GAAG,IAAI,OAAOA,GAAG,KAAK,UAAU,GAAGA,GAAG,CAAC,CAAC,GAAGA,GAAG;;EAErE;AACF;AACA;AACA;EACE,MAAMC,cAAc,GAAGD,GAAG,IAAIA,GAAG,IAAI,OAAOA,GAAG,CAACE,SAAS,KAAK,UAAU;;EAExE;AACF;AACA;AACA;EACE,MAAMC,SAAS,GAAGH,GAAG,IAAIC,cAAc,CAACD,GAAG,CAAC,GAAGA,GAAG,CAACE,SAAS,CAAC,CAAC,GAAGxC,OAAO,CAACC,OAAO,CAACqC,GAAG,CAAC;;EAErF;AACF;AACA;AACA;EACE,MAAMI,SAAS,GAAGJ,GAAG,IAAIA,GAAG,IAAItC,OAAO,CAACC,OAAO,CAACqC,GAAG,CAAC,KAAKA,GAAG;;EAE5D;AACF;AACA;AACA;AACA;EACE,MAAMK,YAAY,GAAGA,CAAA,KAAM/C,QAAQ,CAACC,IAAI,CAAC+C,aAAa,CAAC,IAAIhC,WAAW,CAACiC,SAAS,EAAE,CAAC;;EAEnF;AACF;AACA;AACA;EACE,MAAMC,iBAAiB,GAAGC,cAAc,IAAI;IAC1C,MAAMF,SAAS,GAAGF,YAAY,CAAC,CAAC;IAChC,OAAOE,SAAS,GAAGA,SAAS,CAACD,aAAa,CAACG,cAAc,CAAC,GAAG,IAAI;EACnE,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMC,cAAc,GAAGjC,SAAS,IAAI;IAClC,OAAO+B,iBAAiB,CAAC,IAAI/B,SAAS,EAAE,CAAC;EAC3C,CAAC;;EAED;AACF;AACA;EACE,MAAMkC,QAAQ,GAAGA,CAAA,KAAMD,cAAc,CAACpC,WAAW,CAACsC,KAAK,CAAC;;EAExD;AACF;AACA;EACE,MAAMC,OAAO,GAAGA,CAAA,KAAMH,cAAc,CAACpC,WAAW,CAACM,IAAI,CAAC;;EAEtD;AACF;AACA;EACE,MAAMkC,cAAc,GAAGA,CAAA,KAAMJ,cAAc,CAACpC,WAAW,CAAC,cAAc,CAAC,CAAC;;EAExE;AACF;AACA;EACE,MAAMyC,QAAQ,GAAGA,CAAA,KAAML,cAAc,CAACpC,WAAW,CAAC0C,KAAK,CAAC;;EAExD;AACF;AACA;EACE,MAAMC,gBAAgB,GAAGA,CAAA,KAAMP,cAAc,CAACpC,WAAW,CAAC,gBAAgB,CAAC,CAAC;;EAE5E;AACF;AACA;EACE,MAAM4C,QAAQ,GAAGA,CAAA,KAAMR,cAAc,CAACpC,WAAW,CAAC6C,KAAK,CAAC;;EAExD;AACF;AACA;EACE,MAAMC,gBAAgB,GAAGA,CAAA,KAAMV,cAAc,CAACpC,WAAW,CAAC,gBAAgB,CAAC,CAAC;;EAE5E;AACF;AACA;EACE,MAAM+C,oBAAoB,GAAGA,CAAA,KAAMX,cAAc,CAACpC,WAAW,CAAC,oBAAoB,CAAC,CAAC;;EAEpF;AACF;AACA;EACE,MAAMgD,gBAAgB,GAAGA,CAAA,MAAO,gCAAgCd,iBAAiB,CAAC,IAAIlC,WAAW,CAACiD,OAAO,KAAKjD,WAAW,CAACkD,OAAO,EAAE,CAAC,CAAC;;EAErI;AACF;AACA;EACE,MAAMC,eAAe,GAAGA,CAAA,MAAO,gCAAgCjB,iBAAiB,CAAC,IAAIlC,WAAW,CAACiD,OAAO,KAAKjD,WAAW,CAACoD,MAAM,EAAE,CAAC,CAAC;;EAEnI;AACF;AACA;EACE,MAAMC,aAAa,GAAGA,CAAA,MAAO,gCAAgCnB,iBAAiB,CAAC,IAAIlC,WAAW,CAACiD,OAAO,KAAKjD,WAAW,CAACsD,IAAI,EAAE,CAAC,CAAC;;EAE/H;AACF;AACA;EACE,MAAMC,aAAa,GAAGA,CAAA,KAAMnB,cAAc,CAACpC,WAAW,CAAC,aAAa,CAAC,CAAC;;EAEtE;AACF;AACA;EACE,MAAMwD,SAAS,GAAGA,CAAA,KAAMtB,iBAAiB,CAAC,IAAIlC,WAAW,CAACyD,MAAM,EAAE,CAAC;;EAEnE;AACF;AACA;EACE,MAAMC,UAAU,GAAGA,CAAA,KAAMtB,cAAc,CAACpC,WAAW,CAACiD,OAAO,CAAC;;EAE5D;AACF;AACA;EACE,MAAMU,SAAS,GAAGA,CAAA,KAAMvB,cAAc,CAACpC,WAAW,CAAC4D,MAAM,CAAC;;EAE1D;AACF;AACA;EACE,MAAMC,mBAAmB,GAAGA,CAAA,KAAMzB,cAAc,CAACpC,WAAW,CAAC,oBAAoB,CAAC,CAAC;;EAEnF;AACF;AACA;EACE,MAAM8D,cAAc,GAAGA,CAAA,KAAM1B,cAAc,CAACpC,WAAW,CAAC+D,KAAK,CAAC;;EAE9D;EACA,MAAMC,SAAS,GAAG;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;EACC;AACF;AACA;EACE,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAM3B,KAAK,GAAGD,QAAQ,CAAC,CAAC;IACxB,IAAI,CAACC,KAAK,EAAE;MACV,OAAO,EAAE;IACX;IACA;IACA,MAAM4B,6BAA6B,GAAG5B,KAAK,CAAC6B,gBAAgB,CAAC,qDAAqD,CAAC;IACnH,MAAMC,mCAAmC,GAAGC,KAAK,CAACC,IAAI,CAACJ,6BAA6B;IACpF;IAAA,CACCK,IAAI,CAAC,CAACnG,CAAC,EAAEoG,CAAC,KAAK;MACd,MAAMC,SAAS,GAAGC,QAAQ,CAACtG,CAAC,CAACuG,YAAY,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC;MAC7D,MAAMC,SAAS,GAAGF,QAAQ,CAACF,CAAC,CAACG,YAAY,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC;MAC7D,IAAIF,SAAS,GAAGG,SAAS,EAAE;QACzB,OAAO,CAAC;MACV,CAAC,MAAM,IAAIH,SAAS,GAAGG,SAAS,EAAE;QAChC,OAAO,CAAC,CAAC;MACX;MACA,OAAO,CAAC;IACV,CAAC,CAAC;;IAEF;IACA,MAAMC,sBAAsB,GAAGvC,KAAK,CAAC6B,gBAAgB,CAACH,SAAS,CAAC;IAChE,MAAMc,8BAA8B,GAAGT,KAAK,CAACC,IAAI,CAACO,sBAAsB,CAAC,CAACE,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACL,YAAY,CAAC,UAAU,CAAC,KAAK,IAAI,CAAC;IAC5H,OAAO,CAAC,GAAG,IAAIM,GAAG,CAACb,mCAAmC,CAACc,MAAM,CAACJ,8BAA8B,CAAC,CAAC,CAAC,CAACC,MAAM,CAACC,EAAE,IAAIG,WAAW,CAACH,EAAE,CAAC,CAAC;EAC/H,CAAC;;EAED;AACF;AACA;EACE,MAAMI,OAAO,GAAGA,CAAA,KAAM;IACpB,OAAOC,QAAQ,CAACrG,QAAQ,CAACC,IAAI,EAAEe,WAAW,CAACsF,KAAK,CAAC,IAAI,CAACD,QAAQ,CAACrG,QAAQ,CAACC,IAAI,EAAEe,WAAW,CAAC,aAAa,CAAC,CAAC,IAAI,CAACqF,QAAQ,CAACrG,QAAQ,CAACC,IAAI,EAAEe,WAAW,CAAC,aAAa,CAAC,CAAC;EACnK,CAAC;;EAED;AACF;AACA;EACE,MAAMuF,OAAO,GAAGA,CAAA,KAAM;IACpB,MAAMjD,KAAK,GAAGD,QAAQ,CAAC,CAAC;IACxB,IAAI,CAACC,KAAK,EAAE;MACV,OAAO,KAAK;IACd;IACA,OAAO+C,QAAQ,CAAC/C,KAAK,EAAEtC,WAAW,CAACwF,KAAK,CAAC;EAC3C,CAAC;;EAED;AACF;AACA;EACE,MAAMC,SAAS,GAAGA,CAAA,KAAM;IACtB,MAAMnD,KAAK,GAAGD,QAAQ,CAAC,CAAC;IACxB,IAAI,CAACC,KAAK,EAAE;MACV,OAAO,KAAK;IACd;IACA,OAAOA,KAAK,CAACoD,YAAY,CAAC,cAAc,CAAC;EAC3C,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAMC,YAAY,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK;IACnCD,IAAI,CAACE,WAAW,GAAG,EAAE;IACrB,IAAID,IAAI,EAAE;MACR,MAAME,MAAM,GAAG,IAAIC,SAAS,CAAC,CAAC;MAC9B,MAAMC,MAAM,GAAGF,MAAM,CAACG,eAAe,CAACL,IAAI,EAAE,WAAW,CAAC;MACxD,MAAMM,IAAI,GAAGF,MAAM,CAACjE,aAAa,CAAC,MAAM,CAAC;MACzC,IAAImE,IAAI,EAAE;QACR9B,KAAK,CAACC,IAAI,CAAC6B,IAAI,CAACC,UAAU,CAAC,CAACC,OAAO,CAACC,KAAK,IAAI;UAC3CV,IAAI,CAACW,WAAW,CAACD,KAAK,CAAC;QACzB,CAAC,CAAC;MACJ;MACA,MAAMrH,IAAI,GAAGgH,MAAM,CAACjE,aAAa,CAAC,MAAM,CAAC;MACzC,IAAI/C,IAAI,EAAE;QACRoF,KAAK,CAACC,IAAI,CAACrF,IAAI,CAACmH,UAAU,CAAC,CAACC,OAAO,CAACC,KAAK,IAAI;UAC3C,IAAIA,KAAK,YAAYE,gBAAgB,IAAIF,KAAK,YAAYG,gBAAgB,EAAE;YAC1Eb,IAAI,CAACW,WAAW,CAACD,KAAK,CAACI,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;UAC3C,CAAC,MAAM;YACLd,IAAI,CAACW,WAAW,CAACD,KAAK,CAAC;UACzB;QACF,CAAC,CAAC;MACJ;IACF;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMjB,QAAQ,GAAGA,CAACO,IAAI,EAAEzF,SAAS,KAAK;IACpC,IAAI,CAACA,SAAS,EAAE;MACd,OAAO,KAAK;IACd;IACA,MAAMwG,SAAS,GAAGxG,SAAS,CAACyG,KAAK,CAAC,KAAK,CAAC;IACxC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,CAAC5I,MAAM,EAAE8I,CAAC,EAAE,EAAE;MACzC,IAAI,CAACjB,IAAI,CAACe,SAAS,CAACG,QAAQ,CAACH,SAAS,CAACE,CAAC,CAAC,CAAC,EAAE;QAC1C,OAAO,KAAK;MACd;IACF;IACA,OAAO,IAAI;EACb,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAME,mBAAmB,GAAGA,CAACnB,IAAI,EAAEoB,MAAM,KAAK;IAC5C3C,KAAK,CAACC,IAAI,CAACsB,IAAI,CAACe,SAAS,CAAC,CAACN,OAAO,CAAClG,SAAS,IAAI;MAC9C,IAAI,CAAC8G,MAAM,CAACC,MAAM,CAAClH,WAAW,CAAC,CAACoB,QAAQ,CAACjB,SAAS,CAAC,IAAI,CAAC8G,MAAM,CAACC,MAAM,CAAC7G,SAAS,CAAC,CAACe,QAAQ,CAACjB,SAAS,CAAC,IAAI,CAAC8G,MAAM,CAACC,MAAM,CAACF,MAAM,CAACG,SAAS,IAAI,CAAC,CAAC,CAAC,CAAC/F,QAAQ,CAACjB,SAAS,CAAC,EAAE;QAClKyF,IAAI,CAACe,SAAS,CAACS,MAAM,CAACjH,SAAS,CAAC;MAClC;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMkH,gBAAgB,GAAGA,CAACzB,IAAI,EAAEoB,MAAM,EAAE7G,SAAS,KAAK;IACpD4G,mBAAmB,CAACnB,IAAI,EAAEoB,MAAM,CAAC;IACjC,IAAI,CAACA,MAAM,CAACM,WAAW,EAAE;MACvB;IACF;IACA,MAAMA,WAAW,GAAGN,MAAM,CAACM,WAAW,EAAE,0CAA0CnH,SAAS,EAAE;IAC7F,IAAI,CAACmH,WAAW,EAAE;MAChB;IACF;IACA,IAAI,OAAOA,WAAW,KAAK,QAAQ,IAAI,CAACA,WAAW,CAACjB,OAAO,EAAE;MAC3DxF,IAAI,CAAC,+BAA+BV,SAAS,8CAA8C,OAAOmH,WAAW,GAAG,CAAC;MACjH;IACF;IACAC,QAAQ,CAAC3B,IAAI,EAAE0B,WAAW,CAAC;EAC7B,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAME,UAAU,GAAGA,CAAClF,KAAK,EAAEmF,UAAU,KAAK;IACxC,IAAI,CAACA,UAAU,EAAE;MACf,OAAO,IAAI;IACb;IACA,QAAQA,UAAU;MAChB,KAAK,QAAQ;MACb,KAAK,UAAU;MACf,KAAK,MAAM;QACT,OAAOnF,KAAK,CAACN,aAAa,CAAC,IAAIhC,WAAW,CAACsC,KAAK,OAAOtC,WAAW,CAACyH,UAAU,CAAC,EAAE,CAAC;MACnF,KAAK,UAAU;QACb,OAAOnF,KAAK,CAACN,aAAa,CAAC,IAAIhC,WAAW,CAACsC,KAAK,OAAOtC,WAAW,CAAC0H,QAAQ,QAAQ,CAAC;MACtF,KAAK,OAAO;QACV,OAAOpF,KAAK,CAACN,aAAa,CAAC,IAAIhC,WAAW,CAACsC,KAAK,OAAOtC,WAAW,CAAC2H,KAAK,gBAAgB,CAAC,IAAIrF,KAAK,CAACN,aAAa,CAAC,IAAIhC,WAAW,CAACsC,KAAK,OAAOtC,WAAW,CAAC2H,KAAK,oBAAoB,CAAC;MACrL,KAAK,OAAO;QACV,OAAOrF,KAAK,CAACN,aAAa,CAAC,IAAIhC,WAAW,CAACsC,KAAK,OAAOtC,WAAW,CAAC4H,KAAK,QAAQ,CAAC;MACnF;QACE,OAAOtF,KAAK,CAACN,aAAa,CAAC,IAAIhC,WAAW,CAACsC,KAAK,OAAOtC,WAAW,CAAC6H,KAAK,EAAE,CAAC;IAC/E;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAMC,UAAU,GAAGD,KAAK,IAAI;IAC1BA,KAAK,CAAC9I,KAAK,CAAC,CAAC;;IAEb;IACA,IAAI8I,KAAK,CAACE,IAAI,KAAK,MAAM,EAAE;MACzB;MACA,MAAMC,GAAG,GAAGH,KAAK,CAACI,KAAK;MACvBJ,KAAK,CAACI,KAAK,GAAG,EAAE;MAChBJ,KAAK,CAACI,KAAK,GAAGD,GAAG;IACnB;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAME,WAAW,GAAGA,CAACC,MAAM,EAAExB,SAAS,EAAEyB,SAAS,KAAK;IACpD,IAAI,CAACD,MAAM,IAAI,CAACxB,SAAS,EAAE;MACzB;IACF;IACA,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;MACjCA,SAAS,GAAGA,SAAS,CAACC,KAAK,CAAC,KAAK,CAAC,CAAC7B,MAAM,CAACsD,OAAO,CAAC;IACpD;IACA1B,SAAS,CAACN,OAAO,CAAClG,SAAS,IAAI;MAC7B,IAAIkE,KAAK,CAACiE,OAAO,CAACH,MAAM,CAAC,EAAE;QACzBA,MAAM,CAAC9B,OAAO,CAACT,IAAI,IAAI;UACrB,IAAIwC,SAAS,EAAE;YACbxC,IAAI,CAACe,SAAS,CAAC4B,GAAG,CAACpI,SAAS,CAAC;UAC/B,CAAC,MAAM;YACLyF,IAAI,CAACe,SAAS,CAACS,MAAM,CAACjH,SAAS,CAAC;UAClC;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAIiI,SAAS,EAAE;UACbD,MAAM,CAACxB,SAAS,CAAC4B,GAAG,CAACpI,SAAS,CAAC;QACjC,CAAC,MAAM;UACLgI,MAAM,CAACxB,SAAS,CAACS,MAAM,CAACjH,SAAS,CAAC;QACpC;MACF;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMoH,QAAQ,GAAGA,CAACY,MAAM,EAAExB,SAAS,KAAK;IACtCuB,WAAW,CAACC,MAAM,EAAExB,SAAS,EAAE,IAAI,CAAC;EACtC,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAM6B,WAAW,GAAGA,CAACL,MAAM,EAAExB,SAAS,KAAK;IACzCuB,WAAW,CAACC,MAAM,EAAExB,SAAS,EAAE,KAAK,CAAC;EACvC,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAM8B,qBAAqB,GAAGA,CAAC7C,IAAI,EAAEzF,SAAS,KAAK;IACjD,MAAMuI,QAAQ,GAAGrE,KAAK,CAACC,IAAI,CAACsB,IAAI,CAAC8C,QAAQ,CAAC;IAC1C,KAAK,IAAI7B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6B,QAAQ,CAAC3K,MAAM,EAAE8I,CAAC,EAAE,EAAE;MACxC,MAAMP,KAAK,GAAGoC,QAAQ,CAAC7B,CAAC,CAAC;MACzB,IAAIP,KAAK,YAAYxH,WAAW,IAAIuG,QAAQ,CAACiB,KAAK,EAAEnG,SAAS,CAAC,EAAE;QAC9D,OAAOmG,KAAK;MACd;IACF;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMqC,mBAAmB,GAAGA,CAAC/C,IAAI,EAAEgD,QAAQ,EAAEX,KAAK,KAAK;IACrD,IAAIA,KAAK,KAAK,GAAGvD,QAAQ,CAACuD,KAAK,CAAC,EAAE,EAAE;MAClCA,KAAK,GAAGvD,QAAQ,CAACuD,KAAK,CAAC;IACzB;IACA,IAAIA,KAAK,IAAIvD,QAAQ,CAACuD,KAAK,CAAC,KAAK,CAAC,EAAE;MAClCrC,IAAI,CAACiD,KAAK,CAACC,WAAW,CAACF,QAAQ,EAAE,OAAOX,KAAK,KAAK,QAAQ,GAAG,GAAGA,KAAK,IAAI,GAAGA,KAAK,CAAC;IACpF,CAAC,MAAM;MACLrC,IAAI,CAACiD,KAAK,CAACE,cAAc,CAACH,QAAQ,CAAC;IACrC;EACF,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMI,IAAI,GAAGA,CAACpD,IAAI,EAAEqD,OAAO,GAAG,MAAM,KAAK;IACvC,IAAI,CAACrD,IAAI,EAAE;MACT;IACF;IACAA,IAAI,CAACiD,KAAK,CAACI,OAAO,GAAGA,OAAO;EAC9B,CAAC;;EAED;AACF;AACA;EACE,MAAMC,IAAI,GAAGtD,IAAI,IAAI;IACnB,IAAI,CAACA,IAAI,EAAE;MACT;IACF;IACAA,IAAI,CAACiD,KAAK,CAACI,OAAO,GAAG,MAAM;EAC7B,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAME,wBAAwB,GAAGA,CAACvD,IAAI,EAAEqD,OAAO,GAAG,OAAO,KAAK;IAC5D,IAAI,CAACrD,IAAI,EAAE;MACT;IACF;IACA,IAAIwD,gBAAgB,CAAC,MAAM;MACzBC,MAAM,CAACzD,IAAI,EAAEA,IAAI,CAAC0D,SAAS,EAAEL,OAAO,CAAC;IACvC,CAAC,CAAC,CAACM,OAAO,CAAC3D,IAAI,EAAE;MACf4D,SAAS,EAAE,IAAI;MACfC,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;EACE,MAAMC,QAAQ,GAAGA,CAACC,MAAM,EAAEC,QAAQ,EAAEhB,QAAQ,EAAEX,KAAK,KAAK;IACtD;IACA,MAAMjD,EAAE,GAAG2E,MAAM,CAAC3H,aAAa,CAAC4H,QAAQ,CAAC;IACzC,IAAI5E,EAAE,EAAE;MACNA,EAAE,CAAC6D,KAAK,CAACC,WAAW,CAACF,QAAQ,EAAEX,KAAK,CAAC;IACvC;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMoB,MAAM,GAAGA,CAACzD,IAAI,EAAEwC,SAAS,EAAEa,OAAO,GAAG,MAAM,KAAK;IACpD,IAAIb,SAAS,EAAE;MACbY,IAAI,CAACpD,IAAI,EAAEqD,OAAO,CAAC;IACrB,CAAC,MAAM;MACLC,IAAI,CAACtD,IAAI,CAAC;IACZ;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;EACE,MAAMT,WAAW,GAAGS,IAAI,IAAI,CAAC,EAAEA,IAAI,KAAKA,IAAI,CAACiE,WAAW,IAAIjE,IAAI,CAACkE,YAAY,IAAIlE,IAAI,CAACmE,cAAc,CAAC,CAAC,CAAChM,MAAM,CAAC,CAAC;;EAE/G;AACF;AACA;EACE,MAAMiM,mBAAmB,GAAGA,CAAA,KAAM,CAAC7E,WAAW,CAACnC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAACmC,WAAW,CAAC9B,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC8B,WAAW,CAAChC,eAAe,CAAC,CAAC,CAAC;;EAEtI;AACF;AACA;AACA;EACE,MAAM8G,YAAY,GAAGrE,IAAI,IAAI,CAAC,EAAEA,IAAI,CAACsE,YAAY,GAAGtE,IAAI,CAACuE,YAAY,CAAC;;EAEtE;AACF;AACA;AACA;AACA;EACE,MAAMC,wBAAwB,GAAGA,CAACC,OAAO,EAAEC,WAAW,KAAK;IACzD,IAAIX,MAAM,GAAGU,OAAO;IACpB,OAAOV,MAAM,IAAIA,MAAM,KAAKW,WAAW,EAAE;MACvC,IAAIL,YAAY,CAACN,MAAM,CAAC,EAAE;QACxB,OAAO,IAAI;MACb;MACAA,MAAM,GAAGA,MAAM,CAACY,aAAa;IAC/B;IACA,OAAO,KAAK;EACd,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;EACE,MAAMC,eAAe,GAAG5E,IAAI,IAAI;IAC9B,MAAMiD,KAAK,GAAGtJ,MAAM,CAACkL,gBAAgB,CAAC7E,IAAI,CAAC;IAC3C,MAAM8E,YAAY,GAAGC,UAAU,CAAC9B,KAAK,CAAC+B,gBAAgB,CAAC,oBAAoB,CAAC,IAAI,GAAG,CAAC;IACpF,MAAMC,aAAa,GAAGF,UAAU,CAAC9B,KAAK,CAAC+B,gBAAgB,CAAC,qBAAqB,CAAC,IAAI,GAAG,CAAC;IACtF,OAAOF,YAAY,GAAG,CAAC,IAAIG,aAAa,GAAG,CAAC;EAC9C,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMC,uBAAuB,GAAGA,CAACC,KAAK,EAAEC,KAAK,GAAG,KAAK,KAAK;IACxD,MAAMC,gBAAgB,GAAGpH,mBAAmB,CAAC,CAAC;IAC9C,IAAI,CAACoH,gBAAgB,EAAE;MACrB;IACF;IACA,IAAI9F,WAAW,CAAC8F,gBAAgB,CAAC,EAAE;MACjC,IAAID,KAAK,EAAE;QACTC,gBAAgB,CAACpC,KAAK,CAACqC,UAAU,GAAG,MAAM;QAC1CD,gBAAgB,CAACpC,KAAK,CAACsC,KAAK,GAAG,MAAM;MACvC;MACAvL,UAAU,CAAC,MAAM;QACfqL,gBAAgB,CAACpC,KAAK,CAACqC,UAAU,GAAG,SAASH,KAAK,GAAG,IAAI,UAAU;QACnEE,gBAAgB,CAACpC,KAAK,CAACsC,KAAK,GAAG,IAAI;MACrC,CAAC,EAAE,EAAE,CAAC;IACR;EACF,CAAC;EACD,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMH,gBAAgB,GAAGpH,mBAAmB,CAAC,CAAC;IAC9C,IAAI,CAACoH,gBAAgB,EAAE;MACrB;IACF;IACA,MAAMI,qBAAqB,GAAG3G,QAAQ,CAACnF,MAAM,CAACkL,gBAAgB,CAACQ,gBAAgB,CAAC,CAACE,KAAK,CAAC;IACvFF,gBAAgB,CAACpC,KAAK,CAACE,cAAc,CAAC,YAAY,CAAC;IACnDkC,gBAAgB,CAACpC,KAAK,CAACsC,KAAK,GAAG,MAAM;IACrC,MAAMG,yBAAyB,GAAG5G,QAAQ,CAACnF,MAAM,CAACkL,gBAAgB,CAACQ,gBAAgB,CAAC,CAACE,KAAK,CAAC;IAC3F,MAAMI,uBAAuB,GAAGF,qBAAqB,GAAGC,yBAAyB,GAAG,GAAG;IACvFL,gBAAgB,CAACpC,KAAK,CAACsC,KAAK,GAAG,GAAGI,uBAAuB,GAAG;EAC9D,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMC,SAAS,GAAGA,CAAA,KAAM,OAAOjM,MAAM,KAAK,WAAW,IAAI,OAAOP,QAAQ,KAAK,WAAW;EAExF,MAAMyM,SAAS,GAAG;AACpB,yBAAyBzL,WAAW,CAAC0C,KAAK,uBAAuB1C,WAAW,CAAC,gBAAgB,CAAC,YAAYA,WAAW,CAACsC,KAAK;AAC3H,kCAAkCtC,WAAW,CAAC+D,KAAK;AACnD,gBAAgB/D,WAAW,CAAC,gBAAgB,CAAC;AAC7C,iBAAiBA,WAAW,CAACM,IAAI;AACjC,iBAAiBN,WAAW,CAAC6C,KAAK;AAClC,gBAAgB7C,WAAW,CAAC0C,KAAK,SAAS1C,WAAW,CAAC0C,KAAK;AAC3D,iBAAiB1C,WAAW,CAAC,gBAAgB,CAAC,SAASA,WAAW,CAAC,gBAAgB,CAAC;AACpF,mBAAmBA,WAAW,CAAC6H,KAAK,SAAS7H,WAAW,CAAC6H,KAAK;AAC9D,+BAA+B7H,WAAW,CAAC0L,IAAI;AAC/C,iBAAiB1L,WAAW,CAAC4H,KAAK;AAClC;AACA;AACA;AACA,oBAAoB5H,WAAW,CAAC2L,MAAM,SAAS3L,WAAW,CAAC2L,MAAM;AACjE,iBAAiB3L,WAAW,CAAC2H,KAAK;AAClC,mBAAmB3H,WAAW,CAAC0H,QAAQ;AACvC,kCAAkC1H,WAAW,CAAC0H,QAAQ;AACtD,oBAAoB1H,WAAW,CAAC4L,KAAK;AACrC;AACA,sBAAsB5L,WAAW,CAAC6L,QAAQ,SAAS7L,WAAW,CAAC6L,QAAQ;AACvE,iBAAiB7L,WAAW,CAAC,oBAAoB,CAAC,SAASA,WAAW,CAAC,oBAAoB,CAAC;AAC5F,iBAAiBA,WAAW,CAACiD,OAAO;AACpC,mBAAmBjD,WAAW,CAACyD,MAAM;AACrC,oCAAoCzD,WAAW,CAACkD,OAAO;AACvD,oCAAoClD,WAAW,CAACsD,IAAI;AACpD,oCAAoCtD,WAAW,CAACoD,MAAM;AACtD;AACA,iBAAiBpD,WAAW,CAAC4D,MAAM;AACnC,iBAAiB5D,WAAW,CAAC,8BAA8B,CAAC;AAC5D,mBAAmBA,WAAW,CAAC,oBAAoB,CAAC;AACpD;AACA;AACA,CAAC,CAAC8L,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;;EAEzB;AACF;AACA;EACE,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,YAAY,GAAGjK,YAAY,CAAC,CAAC;IACnC,IAAI,CAACiK,YAAY,EAAE;MACjB,OAAO,KAAK;IACd;IACAA,YAAY,CAAC5E,MAAM,CAAC,CAAC;IACrBoB,WAAW,CAAC,CAACxJ,QAAQ,CAACiN,eAAe,EAAEjN,QAAQ,CAACC,IAAI,CAAC,EAAE,CAACe,WAAW,CAAC,aAAa,CAAC,EAAEA,WAAW,CAAC,aAAa,CAAC,EAAEA,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC;IAC3I,OAAO,IAAI;EACb,CAAC;EACD,MAAMkM,wBAAwB,GAAGA,CAAA,KAAM;IACrCvN,WAAW,CAACwN,eAAe,CAACC,sBAAsB,CAAC,CAAC;EACtD,CAAC;EACD,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;IACpC,MAAM/J,KAAK,GAAGD,QAAQ,CAAC,CAAC;IACxB,MAAMwF,KAAK,GAAGY,qBAAqB,CAACnG,KAAK,EAAEtC,WAAW,CAAC6H,KAAK,CAAC;IAC7D,MAAM6D,IAAI,GAAGjD,qBAAqB,CAACnG,KAAK,EAAEtC,WAAW,CAAC0L,IAAI,CAAC;IAC3D;IACA,MAAM9D,KAAK,GAAGtF,KAAK,CAACN,aAAa,CAAC,IAAIhC,WAAW,CAAC4H,KAAK,QAAQ,CAAC;IAChE;IACA,MAAM0E,WAAW,GAAGhK,KAAK,CAACN,aAAa,CAAC,IAAIhC,WAAW,CAAC4H,KAAK,SAAS,CAAC;IACvE,MAAM+D,MAAM,GAAGlD,qBAAqB,CAACnG,KAAK,EAAEtC,WAAW,CAAC2L,MAAM,CAAC;IAC/D;IACA,MAAMjE,QAAQ,GAAGpF,KAAK,CAACN,aAAa,CAAC,IAAIhC,WAAW,CAAC0H,QAAQ,QAAQ,CAAC;IACtE,MAAMmE,QAAQ,GAAGpD,qBAAqB,CAACnG,KAAK,EAAEtC,WAAW,CAAC6L,QAAQ,CAAC;IACnEhE,KAAK,CAAC0E,OAAO,GAAGL,wBAAwB;IACxCR,IAAI,CAACc,QAAQ,GAAGN,wBAAwB;IACxCP,MAAM,CAACa,QAAQ,GAAGN,wBAAwB;IAC1CxE,QAAQ,CAAC8E,QAAQ,GAAGN,wBAAwB;IAC5CL,QAAQ,CAACU,OAAO,GAAGL,wBAAwB;IAC3CtE,KAAK,CAAC2E,OAAO,GAAG,MAAM;MACpBL,wBAAwB,CAAC,CAAC;MAC1BI,WAAW,CAACrE,KAAK,GAAGL,KAAK,CAACK,KAAK;IACjC,CAAC;IACDL,KAAK,CAAC4E,QAAQ,GAAG,MAAM;MACrBN,wBAAwB,CAAC,CAAC;MAC1BI,WAAW,CAACrE,KAAK,GAAGL,KAAK,CAACK,KAAK;IACjC,CAAC;EACH,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMwE,SAAS,GAAGtE,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,GAAGnJ,QAAQ,CAACgD,aAAa,CAACmG,MAAM,CAAC,GAAGA,MAAM;;EAEhG;AACF;AACA;EACE,MAAMuE,kBAAkB,GAAG1F,MAAM,IAAI;IACnC,MAAM1E,KAAK,GAAGD,QAAQ,CAAC,CAAC;IACxBC,KAAK,CAACqK,YAAY,CAAC,MAAM,EAAE3F,MAAM,CAACxB,KAAK,GAAG,OAAO,GAAG,QAAQ,CAAC;IAC7DlD,KAAK,CAACqK,YAAY,CAAC,WAAW,EAAE3F,MAAM,CAACxB,KAAK,GAAG,QAAQ,GAAG,WAAW,CAAC;IACtE,IAAI,CAACwB,MAAM,CAACxB,KAAK,EAAE;MACjBlD,KAAK,CAACqK,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC;IAC1C;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAMC,QAAQ,GAAGC,aAAa,IAAI;IAChC,IAAItN,MAAM,CAACkL,gBAAgB,CAACoC,aAAa,CAAC,CAACC,SAAS,KAAK,KAAK,EAAE;MAC9DvF,QAAQ,CAACxF,YAAY,CAAC,CAAC,EAAE/B,WAAW,CAAC+M,GAAG,CAAC;IAC3C;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMC,IAAI,GAAGhG,MAAM,IAAI;IACrB;IACA,MAAMiG,mBAAmB,GAAGlB,iBAAiB,CAAC,CAAC;IAC/C,IAAIP,SAAS,CAAC,CAAC,EAAE;MACfvK,KAAK,CAAC,6CAA6C,CAAC;MACpD;IACF;IACA,MAAMgB,SAAS,GAAGjD,QAAQ,CAACkO,aAAa,CAAC,KAAK,CAAC;IAC/CjL,SAAS,CAAC9B,SAAS,GAAGH,WAAW,CAACiC,SAAS;IAC3C,IAAIgL,mBAAmB,EAAE;MACvB1F,QAAQ,CAACtF,SAAS,EAAEjC,WAAW,CAAC,eAAe,CAAC,CAAC;IACnD;IACA2F,YAAY,CAAC1D,SAAS,EAAEwJ,SAAS,CAAC;IAClCxJ,SAAS,CAACkL,OAAO,CAAC,YAAY,CAAC,GAAGnG,MAAM,CAACoG,KAAK;IAC9C,MAAMP,aAAa,GAAGJ,SAAS,CAACzF,MAAM,CAACmB,MAAM,CAAC;IAC9C0E,aAAa,CAACtG,WAAW,CAACtE,SAAS,CAAC;IACpC,IAAI+E,MAAM,CAACqG,QAAQ,EAAE;MACnBpL,SAAS,CAAC0K,YAAY,CAAC,SAAS,EAAE,EAAE,CAAC;MACrC1K,SAAS,CAACqL,WAAW,CAAC,CAAC;IACzB;IACAZ,kBAAkB,CAAC1F,MAAM,CAAC;IAC1B4F,QAAQ,CAACC,aAAa,CAAC;IACvBR,uBAAuB,CAAC,CAAC;EAC3B,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMkB,oBAAoB,GAAGA,CAACC,KAAK,EAAErF,MAAM,KAAK;IAC9C;IACA,IAAIqF,KAAK,YAAY1O,WAAW,EAAE;MAChCqJ,MAAM,CAAC5B,WAAW,CAACiH,KAAK,CAAC;IAC3B;;IAEA;IAAA,KACK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAClCC,YAAY,CAACD,KAAK,EAAErF,MAAM,CAAC;IAC7B;;IAEA;IAAA,KACK,IAAIqF,KAAK,EAAE;MACd7H,YAAY,CAACwC,MAAM,EAAEqF,KAAK,CAAC;IAC7B;EACF,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMC,YAAY,GAAGA,CAACD,KAAK,EAAErF,MAAM,KAAK;IACtC;IACA,IAAIqF,KAAK,CAACE,MAAM,EAAE;MAChBC,gBAAgB,CAACxF,MAAM,EAAEqF,KAAK,CAAC;IACjC;;IAEA;IAAA,KACK;MACH7H,YAAY,CAACwC,MAAM,EAAEqF,KAAK,CAACI,QAAQ,CAAC,CAAC,CAAC;IACxC;EACF,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMD,gBAAgB,GAAGA,CAACxF,MAAM,EAAEvC,IAAI,KAAK;IACzCuC,MAAM,CAACrC,WAAW,GAAG,EAAE;IACvB,IAAI,CAAC,IAAIF,IAAI,EAAE;MACb,KAAK,IAAIiB,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIjB,IAAI,EAAEiB,CAAC,EAAE,EAAE;QAC9BsB,MAAM,CAAC5B,WAAW,CAACX,IAAI,CAACiB,CAAC,CAAC,CAACH,SAAS,CAAC,IAAI,CAAC,CAAC;MAC7C;IACF,CAAC,MAAM;MACLyB,MAAM,CAAC5B,WAAW,CAACX,IAAI,CAACc,SAAS,CAAC,IAAI,CAAC,CAAC;IAC1C;EACF,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMmH,aAAa,GAAGA,CAACC,QAAQ,EAAE9G,MAAM,KAAK;IAC1C,MAAM/D,OAAO,GAAGS,UAAU,CAAC,CAAC;IAC5B,MAAMD,MAAM,GAAGD,SAAS,CAAC,CAAC;IAC1B,IAAI,CAACP,OAAO,IAAI,CAACQ,MAAM,EAAE;MACvB;IACF;;IAEA;IACA,IAAI,CAACuD,MAAM,CAAC+G,iBAAiB,IAAI,CAAC/G,MAAM,CAACgH,cAAc,IAAI,CAAChH,MAAM,CAACiH,gBAAgB,EAAE;MACnF/E,IAAI,CAACjG,OAAO,CAAC;IACf,CAAC,MAAM;MACL+F,IAAI,CAAC/F,OAAO,CAAC;IACf;;IAEA;IACAoE,gBAAgB,CAACpE,OAAO,EAAE+D,MAAM,EAAE,SAAS,CAAC;;IAE5C;IACAkH,aAAa,CAACjL,OAAO,EAAEQ,MAAM,EAAEuD,MAAM,CAAC;;IAEtC;IACArB,YAAY,CAAClC,MAAM,EAAEuD,MAAM,CAACmH,UAAU,IAAI,EAAE,CAAC;IAC7C9G,gBAAgB,CAAC5D,MAAM,EAAEuD,MAAM,EAAE,QAAQ,CAAC;EAC5C,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,SAASkH,aAAaA,CAACjL,OAAO,EAAEQ,MAAM,EAAEuD,MAAM,EAAE;IAC9C,MAAMoH,aAAa,GAAGpL,gBAAgB,CAAC,CAAC;IACxC,MAAMqL,UAAU,GAAGhL,aAAa,CAAC,CAAC;IAClC,MAAMiL,YAAY,GAAGnL,eAAe,CAAC,CAAC;IACtC,IAAI,CAACiL,aAAa,IAAI,CAACC,UAAU,IAAI,CAACC,YAAY,EAAE;MAClD;IACF;;IAEA;IACAC,YAAY,CAACH,aAAa,EAAE,SAAS,EAAEpH,MAAM,CAAC;IAC9CuH,YAAY,CAACF,UAAU,EAAE,MAAM,EAAErH,MAAM,CAAC;IACxCuH,YAAY,CAACD,YAAY,EAAE,QAAQ,EAAEtH,MAAM,CAAC;IAC5CwH,oBAAoB,CAACJ,aAAa,EAAEC,UAAU,EAAEC,YAAY,EAAEtH,MAAM,CAAC;IACrE,IAAIA,MAAM,CAACyH,cAAc,EAAE;MACzB,IAAIzH,MAAM,CAACxB,KAAK,EAAE;QAChBvC,OAAO,CAACyL,YAAY,CAACJ,YAAY,EAAEF,aAAa,CAAC;QACjDnL,OAAO,CAACyL,YAAY,CAACL,UAAU,EAAED,aAAa,CAAC;MACjD,CAAC,MAAM;QACLnL,OAAO,CAACyL,YAAY,CAACJ,YAAY,EAAE7K,MAAM,CAAC;QAC1CR,OAAO,CAACyL,YAAY,CAACL,UAAU,EAAE5K,MAAM,CAAC;QACxCR,OAAO,CAACyL,YAAY,CAACN,aAAa,EAAE3K,MAAM,CAAC;MAC7C;IACF;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,SAAS+K,oBAAoBA,CAACJ,aAAa,EAAEC,UAAU,EAAEC,YAAY,EAAEtH,MAAM,EAAE;IAC7E,IAAI,CAACA,MAAM,CAAC2H,cAAc,EAAE;MAC1BnG,WAAW,CAAC,CAAC4F,aAAa,EAAEC,UAAU,EAAEC,YAAY,CAAC,EAAEtO,WAAW,CAAC4O,MAAM,CAAC;MAC1E;IACF;IACArH,QAAQ,CAAC,CAAC6G,aAAa,EAAEC,UAAU,EAAEC,YAAY,CAAC,EAAEtO,WAAW,CAAC4O,MAAM,CAAC;;IAEvE;IACA,IAAI5H,MAAM,CAAC6H,kBAAkB,EAAE;MAC7BT,aAAa,CAACvF,KAAK,CAACC,WAAW,CAAC,yCAAyC,EAAE9B,MAAM,CAAC6H,kBAAkB,CAAC;IACvG;IACA,IAAI7H,MAAM,CAAC8H,eAAe,EAAE;MAC1BT,UAAU,CAACxF,KAAK,CAACC,WAAW,CAAC,sCAAsC,EAAE9B,MAAM,CAAC8H,eAAe,CAAC;IAC9F;IACA,IAAI9H,MAAM,CAAC+H,iBAAiB,EAAE;MAC5BT,YAAY,CAACzF,KAAK,CAACC,WAAW,CAAC,wCAAwC,EAAE9B,MAAM,CAAC+H,iBAAiB,CAAC;IACpG;;IAEA;IACAC,iBAAiB,CAACZ,aAAa,CAAC;IAChCY,iBAAiB,CAACX,UAAU,CAAC;IAC7BW,iBAAiB,CAACV,YAAY,CAAC;EACjC;;EAEA;AACF;AACA;EACE,SAASU,iBAAiBA,CAACC,MAAM,EAAE;IACjC,MAAMC,WAAW,GAAG3P,MAAM,CAACkL,gBAAgB,CAACwE,MAAM,CAAC;IACnD,IAAIC,WAAW,CAACtE,gBAAgB,CAAC,wCAAwC,CAAC,EAAE;MAC1E;MACA;IACF;IACA,MAAMuE,YAAY,GAAGD,WAAW,CAACE,eAAe,CAACtD,OAAO,CAAC,8BAA8B,EAAE,uBAAuB,CAAC;IACjHmD,MAAM,CAACpG,KAAK,CAACC,WAAW,CAAC,wCAAwC,EAAEoG,WAAW,CAACtE,gBAAgB,CAAC,iBAAiB,CAAC,CAACkB,OAAO,CAAC,WAAW,EAAE,IAAIqD,YAAY,EAAE,CAAC,CAAC;EAC9J;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASZ,YAAYA,CAACU,MAAM,EAAEI,UAAU,EAAErI,MAAM,EAAE;IAChD,MAAMsI,UAAU,GAAG,4CAA4C9O,qBAAqB,CAAC6O,UAAU,CAAC;IAChGhG,MAAM,CAAC4F,MAAM,EAAEjI,MAAM,CAAC,OAAOsI,UAAU,QAAQ,CAAC,EAAE,cAAc,CAAC;IACjE3J,YAAY,CAACsJ,MAAM,EAAEjI,MAAM,CAAC,GAAGqI,UAAU,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IAC/DJ,MAAM,CAACtC,YAAY,CAAC,YAAY,EAAE3F,MAAM,CAAC,GAAGqI,UAAU,iBAAiB,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;;IAEjF;IACAJ,MAAM,CAAC9O,SAAS,GAAGH,WAAW,CAACqP,UAAU,CAAC;IAC1ChI,gBAAgB,CAAC4H,MAAM,EAAEjI,MAAM,EAAE,GAAGqI,UAAU,QAAQ,CAAC;EACzD;;EAEA;AACF;AACA;AACA;EACE,MAAME,iBAAiB,GAAGA,CAACzB,QAAQ,EAAE9G,MAAM,KAAK;IAC9C,MAAMwI,WAAW,GAAG1L,cAAc,CAAC,CAAC;IACpC,IAAI,CAAC0L,WAAW,EAAE;MAChB;IACF;IACA7J,YAAY,CAAC6J,WAAW,EAAExI,MAAM,CAACyI,eAAe,IAAI,EAAE,CAAC;;IAEvD;IACApI,gBAAgB,CAACmI,WAAW,EAAExI,MAAM,EAAE,aAAa,CAAC;IACpDqC,MAAM,CAACmG,WAAW,EAAExI,MAAM,CAAC0I,eAAe,CAAC;IAC3CF,WAAW,CAAC7C,YAAY,CAAC,YAAY,EAAE3F,MAAM,CAAC2I,oBAAoB,IAAI,EAAE,CAAC;EAC3E,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMC,eAAe,GAAGA,CAAC9B,QAAQ,EAAE9G,MAAM,KAAK;IAC5C,MAAM/E,SAAS,GAAGF,YAAY,CAAC,CAAC;IAChC,IAAI,CAACE,SAAS,EAAE;MACd;IACF;IACA4N,mBAAmB,CAAC5N,SAAS,EAAE+E,MAAM,CAAC8I,QAAQ,CAAC;IAC/CC,mBAAmB,CAAC9N,SAAS,EAAE+E,MAAM,CAACgJ,QAAQ,CAAC;IAC/CC,eAAe,CAAChO,SAAS,EAAE+E,MAAM,CAACkJ,IAAI,CAAC;;IAEvC;IACA7I,gBAAgB,CAACpF,SAAS,EAAE+E,MAAM,EAAE,WAAW,CAAC;EAClD,CAAC;;EAED;AACF;AACA;AACA;EACE,SAAS6I,mBAAmBA,CAAC5N,SAAS,EAAE6N,QAAQ,EAAE;IAChD,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MAChC7N,SAAS,CAAC4G,KAAK,CAACsH,UAAU,GAAGL,QAAQ;IACvC,CAAC,MAAM,IAAI,CAACA,QAAQ,EAAE;MACpBvI,QAAQ,CAAC,CAACvI,QAAQ,CAACiN,eAAe,EAAEjN,QAAQ,CAACC,IAAI,CAAC,EAAEe,WAAW,CAAC,aAAa,CAAC,CAAC;IACjF;EACF;;EAEA;AACF;AACA;AACA;EACE,SAAS+P,mBAAmBA,CAAC9N,SAAS,EAAE+N,QAAQ,EAAE;IAChD,IAAI,CAACA,QAAQ,EAAE;MACb;IACF;IACA,IAAIA,QAAQ,IAAIhQ,WAAW,EAAE;MAC3BuH,QAAQ,CAACtF,SAAS,EAAEjC,WAAW,CAACgQ,QAAQ,CAAC,CAAC;IAC5C,CAAC,MAAM;MACLnP,IAAI,CAAC,+DAA+D,CAAC;MACrE0G,QAAQ,CAACtF,SAAS,EAAEjC,WAAW,CAACoQ,MAAM,CAAC;IACzC;EACF;;EAEA;AACF;AACA;AACA;EACE,SAASH,eAAeA,CAAChO,SAAS,EAAEiO,IAAI,EAAE;IACxC,IAAI,CAACA,IAAI,EAAE;MACT;IACF;IACA3I,QAAQ,CAACtF,SAAS,EAAEjC,WAAW,CAAC,QAAQkQ,IAAI,EAAE,CAAC,CAAC;EAClD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEE,IAAIG,YAAY,GAAG;IACjBC,WAAW,EAAE,IAAIC,OAAO,CAAC,CAAC;IAC1BC,QAAQ,EAAE,IAAID,OAAO,CAAC;EACxB,CAAC;;EAED;;EAGA;EACA,MAAME,YAAY,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,CAAC;;EAE1F;AACF;AACA;AACA;EACE,MAAMC,WAAW,GAAGA,CAAC5C,QAAQ,EAAE9G,MAAM,KAAK;IACxC,MAAM1E,KAAK,GAAGD,QAAQ,CAAC,CAAC;IACxB,IAAI,CAACC,KAAK,EAAE;MACV;IACF;IACA,MAAMgO,WAAW,GAAGD,YAAY,CAACC,WAAW,CAACjS,GAAG,CAACyP,QAAQ,CAAC;IAC1D,MAAM6C,QAAQ,GAAG,CAACL,WAAW,IAAItJ,MAAM,CAACa,KAAK,KAAKyI,WAAW,CAACzI,KAAK;IACnE4I,YAAY,CAACpK,OAAO,CAACoB,UAAU,IAAI;MACjC,MAAMmJ,cAAc,GAAGnI,qBAAqB,CAACnG,KAAK,EAAEtC,WAAW,CAACyH,UAAU,CAAC,CAAC;MAC5E,IAAI,CAACmJ,cAAc,EAAE;QACnB;MACF;;MAEA;MACAC,aAAa,CAACpJ,UAAU,EAAET,MAAM,CAAC8J,eAAe,CAAC;;MAEjD;MACAF,cAAc,CAACzQ,SAAS,GAAGH,WAAW,CAACyH,UAAU,CAAC;MAClD,IAAIkJ,QAAQ,EAAE;QACZzH,IAAI,CAAC0H,cAAc,CAAC;MACtB;IACF,CAAC,CAAC;IACF,IAAI5J,MAAM,CAACa,KAAK,EAAE;MAChB,IAAI8I,QAAQ,EAAE;QACZI,SAAS,CAAC/J,MAAM,CAAC;MACnB;MACA;MACAgK,cAAc,CAAChK,MAAM,CAAC;IACxB;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAM+J,SAAS,GAAG/J,MAAM,IAAI;IAC1B,IAAI,CAACA,MAAM,CAACa,KAAK,EAAE;MACjB;IACF;IACA,IAAI,CAACoJ,eAAe,CAACjK,MAAM,CAACa,KAAK,CAAC,EAAE;MAClC5G,KAAK,CAAC,sCAAsCgG,MAAM,CAACiK,IAAI,CAACD,eAAe,CAAC,CAACjQ,IAAI,CAAC,KAAK,CAAC,UAAUgG,MAAM,CAACa,KAAK,GAAG,CAAC;MAC9G;IACF;IACA,MAAM+I,cAAc,GAAGO,iBAAiB,CAACnK,MAAM,CAACa,KAAK,CAAC;IACtD,IAAI,CAAC+I,cAAc,EAAE;MACnB;IACF;IACA,MAAM/I,KAAK,GAAGoJ,eAAe,CAACjK,MAAM,CAACa,KAAK,CAAC,CAAC+I,cAAc,EAAE5J,MAAM,CAAC;IACnEgC,IAAI,CAAC4H,cAAc,CAAC;;IAEpB;IACA,IAAI5J,MAAM,CAACoK,cAAc,EAAE;MACzBxR,UAAU,CAAC,MAAM;QACfkI,UAAU,CAACD,KAAK,CAAC;MACnB,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAMwJ,gBAAgB,GAAGxJ,KAAK,IAAI;IAChC,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgB,KAAK,CAACyJ,UAAU,CAACvT,MAAM,EAAE8I,CAAC,EAAE,EAAE;MAChD,MAAM0K,QAAQ,GAAG1J,KAAK,CAACyJ,UAAU,CAACzK,CAAC,CAAC,CAAC2K,IAAI;MACzC,IAAI,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,CAACpQ,QAAQ,CAACmQ,QAAQ,CAAC,EAAE;QACxD1J,KAAK,CAAC4J,eAAe,CAACF,QAAQ,CAAC;MACjC;IACF;EACF,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMV,aAAa,GAAGA,CAACpJ,UAAU,EAAEqJ,eAAe,KAAK;IACrD,MAAMxO,KAAK,GAAGD,QAAQ,CAAC,CAAC;IACxB,IAAI,CAACC,KAAK,EAAE;MACV;IACF;IACA,MAAMuF,KAAK,GAAGL,UAAU,CAAClF,KAAK,EAAEmF,UAAU,CAAC;IAC3C,IAAI,CAACI,KAAK,EAAE;MACV;IACF;IACAwJ,gBAAgB,CAACxJ,KAAK,CAAC;IACvB,KAAK,MAAM6J,IAAI,IAAIZ,eAAe,EAAE;MAClCjJ,KAAK,CAAC8E,YAAY,CAAC+E,IAAI,EAAEZ,eAAe,CAACY,IAAI,CAAC,CAAC;IACjD;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAMV,cAAc,GAAGhK,MAAM,IAAI;IAC/B,IAAI,CAACA,MAAM,CAACa,KAAK,EAAE;MACjB;IACF;IACA,MAAM+I,cAAc,GAAGO,iBAAiB,CAACnK,MAAM,CAACa,KAAK,CAAC;IACtD,IAAI+I,cAAc,EAAE;MAClBvJ,gBAAgB,CAACuJ,cAAc,EAAE5J,MAAM,EAAE,OAAO,CAAC;IACnD;EACF,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAM2K,mBAAmB,GAAGA,CAAC9J,KAAK,EAAEb,MAAM,KAAK;IAC7C,IAAI,CAACa,KAAK,CAAC+J,WAAW,IAAI5K,MAAM,CAAC6K,gBAAgB,EAAE;MACjDhK,KAAK,CAAC+J,WAAW,GAAG5K,MAAM,CAAC6K,gBAAgB;IAC7C;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMC,aAAa,GAAGA,CAACjK,KAAK,EAAEkK,SAAS,EAAE/K,MAAM,KAAK;IAClD,IAAIA,MAAM,CAACgL,UAAU,EAAE;MACrB,MAAMpG,KAAK,GAAG5M,QAAQ,CAACkO,aAAa,CAAC,OAAO,CAAC;MAC7C,MAAM+E,UAAU,GAAGjS,WAAW,CAAC,aAAa,CAAC;MAC7C4L,KAAK,CAACe,YAAY,CAAC,KAAK,EAAE9E,KAAK,CAACqK,EAAE,CAAC;MACnCtG,KAAK,CAACzL,SAAS,GAAG8R,UAAU;MAC5B,IAAI,OAAOjL,MAAM,CAACM,WAAW,KAAK,QAAQ,EAAE;QAC1CC,QAAQ,CAACqE,KAAK,EAAE5E,MAAM,CAACM,WAAW,CAAC0K,UAAU,CAAC;MAChD;MACApG,KAAK,CAACuG,SAAS,GAAGnL,MAAM,CAACgL,UAAU;MACnCD,SAAS,CAACK,qBAAqB,CAAC,aAAa,EAAExG,KAAK,CAAC;IACvD;EACF,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMuF,iBAAiB,GAAGkB,SAAS,IAAI;IACrC,MAAM/P,KAAK,GAAGD,QAAQ,CAAC,CAAC;IACxB,IAAI,CAACC,KAAK,EAAE;MACV;IACF;IACA,OAAOmG,qBAAqB,CAACnG,KAAK,EAAEtC,WAAW,EAAE,wBAAwBqS,SAAS,EAAE,IAAIrS,WAAW,CAAC6H,KAAK,CAAC;EAC5G,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMyK,qBAAqB,GAAGA,CAACzK,KAAK,EAAE0K,UAAU,KAAK;IACnD,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAACnR,QAAQ,CAAC,OAAOmR,UAAU,CAAC,EAAE;MACpD1K,KAAK,CAACI,KAAK,GAAG,GAAGsK,UAAU,EAAE;IAC/B,CAAC,MAAM,IAAI,CAACzQ,SAAS,CAACyQ,UAAU,CAAC,EAAE;MACjC1R,IAAI,CAAC,iFAAiF,OAAO0R,UAAU,GAAG,CAAC;IAC7G;EACF,CAAC;;EAED;EACA,MAAMtB,eAAe,GAAG,CAAC,CAAC;;EAE1B;AACF;AACA;AACA;AACA;EACEA,eAAe,CAACuB,IAAI,GAAGvB,eAAe,CAACwB,KAAK,GAAGxB,eAAe,CAACyB,QAAQ,GAAGzB,eAAe,CAAC0B,MAAM,GAAG1B,eAAe,CAAC2B,GAAG,GAAG3B,eAAe,CAAC4B,GAAG,GAAG5B,eAAe,CAAC6B,MAAM,GAAG7B,eAAe,CAAC8B,IAAI,GAAG9B,eAAe,CAAC,gBAAgB,CAAC,GAAGA,eAAe,CAAC+B,IAAI,GAAG/B,eAAe,CAACgC,IAAI,GAAGhC,eAAe,CAACiC,KAAK,GAAG;EACzS,CAACrL,KAAK,EAAEb,MAAM,KAAK;IACjBsL,qBAAqB,CAACzK,KAAK,EAAEb,MAAM,CAACuL,UAAU,CAAC;IAC/CT,aAAa,CAACjK,KAAK,EAAEA,KAAK,EAAEb,MAAM,CAAC;IACnC2K,mBAAmB,CAAC9J,KAAK,EAAEb,MAAM,CAAC;IAClCa,KAAK,CAACE,IAAI,GAAGf,MAAM,CAACa,KAAK;IACzB,OAAOA,KAAK;EACd,CAAC;;EAED;AACF;AACA;AACA;AACA;EACEoJ,eAAe,CAACvF,IAAI,GAAG,CAAC7D,KAAK,EAAEb,MAAM,KAAK;IACxC8K,aAAa,CAACjK,KAAK,EAAEA,KAAK,EAAEb,MAAM,CAAC;IACnC2K,mBAAmB,CAAC9J,KAAK,EAAEb,MAAM,CAAC;IAClC,OAAOa,KAAK;EACd,CAAC;;EAED;AACF;AACA;AACA;AACA;EACEoJ,eAAe,CAACrJ,KAAK,GAAG,CAACA,KAAK,EAAEZ,MAAM,KAAK;IACzC,MAAMmM,UAAU,GAAGvL,KAAK,CAAC5F,aAAa,CAAC,OAAO,CAAC;IAC/C,MAAMsK,WAAW,GAAG1E,KAAK,CAAC5F,aAAa,CAAC,QAAQ,CAAC;IACjDsQ,qBAAqB,CAACa,UAAU,EAAEnM,MAAM,CAACuL,UAAU,CAAC;IACpDY,UAAU,CAACpL,IAAI,GAAGf,MAAM,CAACa,KAAK;IAC9ByK,qBAAqB,CAAChG,WAAW,EAAEtF,MAAM,CAACuL,UAAU,CAAC;IACrDT,aAAa,CAACqB,UAAU,EAAEvL,KAAK,EAAEZ,MAAM,CAAC;IACxC,OAAOY,KAAK;EACd,CAAC;;EAED;AACF;AACA;AACA;AACA;EACEqJ,eAAe,CAACtF,MAAM,GAAG,CAACA,MAAM,EAAE3E,MAAM,KAAK;IAC3C2E,MAAM,CAAC7F,WAAW,GAAG,EAAE;IACvB,IAAIkB,MAAM,CAAC6K,gBAAgB,EAAE;MAC3B,MAAMD,WAAW,GAAG5S,QAAQ,CAACkO,aAAa,CAAC,QAAQ,CAAC;MACpDvH,YAAY,CAACiM,WAAW,EAAE5K,MAAM,CAAC6K,gBAAgB,CAAC;MAClDD,WAAW,CAAC3J,KAAK,GAAG,EAAE;MACtB2J,WAAW,CAACwB,QAAQ,GAAG,IAAI;MAC3BxB,WAAW,CAACyB,QAAQ,GAAG,IAAI;MAC3B1H,MAAM,CAACpF,WAAW,CAACqL,WAAW,CAAC;IACjC;IACAE,aAAa,CAACnG,MAAM,EAAEA,MAAM,EAAE3E,MAAM,CAAC;IACrC,OAAO2E,MAAM;EACf,CAAC;;EAED;AACF;AACA;AACA;EACEsF,eAAe,CAACtJ,KAAK,GAAGA,KAAK,IAAI;IAC/BA,KAAK,CAAC7B,WAAW,GAAG,EAAE;IACtB,OAAO6B,KAAK;EACd,CAAC;;EAED;AACF;AACA;AACA;AACA;EACEsJ,eAAe,CAACvJ,QAAQ,GAAG,CAAC4L,iBAAiB,EAAEtM,MAAM,KAAK;IACxD,MAAMU,QAAQ,GAAGF,UAAU,CAACnF,QAAQ,CAAC,CAAC,EAAE,UAAU,CAAC;IACnDqF,QAAQ,CAACO,KAAK,GAAG,GAAG;IACpBP,QAAQ,CAAC6L,OAAO,GAAGlL,OAAO,CAACrB,MAAM,CAACuL,UAAU,CAAC;IAC7C,MAAM3G,KAAK,GAAG0H,iBAAiB,CAACtR,aAAa,CAAC,MAAM,CAAC;IACrD2D,YAAY,CAACiG,KAAK,EAAE5E,MAAM,CAAC6K,gBAAgB,IAAI7K,MAAM,CAACgL,UAAU,CAAC;IACjE,OAAOtK,QAAQ;EACjB,CAAC;;EAED;AACF;AACA;AACA;AACA;EACEuJ,eAAe,CAACpF,QAAQ,GAAG,CAACA,QAAQ,EAAE7E,MAAM,KAAK;IAC/CsL,qBAAqB,CAACzG,QAAQ,EAAE7E,MAAM,CAACuL,UAAU,CAAC;IAClDZ,mBAAmB,CAAC9F,QAAQ,EAAE7E,MAAM,CAAC;IACrC8K,aAAa,CAACjG,QAAQ,EAAEA,QAAQ,EAAE7E,MAAM,CAAC;;IAEzC;AACJ;AACA;AACA;IACI,MAAMwM,SAAS,GAAGxO,EAAE,IAAIN,QAAQ,CAACnF,MAAM,CAACkL,gBAAgB,CAACzF,EAAE,CAAC,CAACyO,UAAU,CAAC,GAAG/O,QAAQ,CAACnF,MAAM,CAACkL,gBAAgB,CAACzF,EAAE,CAAC,CAAC0O,WAAW,CAAC;;IAE5H;IACA9T,UAAU,CAAC,MAAM;MACf;MACA,IAAI,kBAAkB,IAAIL,MAAM,EAAE;QAChC,MAAMoU,iBAAiB,GAAGjP,QAAQ,CAACnF,MAAM,CAACkL,gBAAgB,CAACpI,QAAQ,CAAC,CAAC,CAAC,CAAC8I,KAAK,CAAC;QAC7E,MAAMyI,qBAAqB,GAAGA,CAAA,KAAM;UAClC;UACA,IAAI,CAAC5U,QAAQ,CAACC,IAAI,CAAC6H,QAAQ,CAAC+E,QAAQ,CAAC,EAAE;YACrC;UACF;UACA,MAAMgI,aAAa,GAAGhI,QAAQ,CAAChC,WAAW,GAAG2J,SAAS,CAAC3H,QAAQ,CAAC;UAChE,IAAIgI,aAAa,GAAGF,iBAAiB,EAAE;YACrCtR,QAAQ,CAAC,CAAC,CAACwG,KAAK,CAACsC,KAAK,GAAG,GAAG0I,aAAa,IAAI;UAC/C,CAAC,MAAM;YACLlL,mBAAmB,CAACtG,QAAQ,CAAC,CAAC,EAAE,OAAO,EAAE2E,MAAM,CAACmE,KAAK,CAAC;UACxD;QACF,CAAC;QACD,IAAI/B,gBAAgB,CAACwK,qBAAqB,CAAC,CAACrK,OAAO,CAACsC,QAAQ,EAAE;UAC5DyF,UAAU,EAAE,IAAI;UAChBwC,eAAe,EAAE,CAAC,OAAO;QAC3B,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACF,OAAOjI,QAAQ;EACjB,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMkI,aAAa,GAAGA,CAACjG,QAAQ,EAAE9G,MAAM,KAAK;IAC1C,MAAMgN,aAAa,GAAGrR,gBAAgB,CAAC,CAAC;IACxC,IAAI,CAACqR,aAAa,EAAE;MAClB;IACF;IACA7K,wBAAwB,CAAC6K,aAAa,CAAC;IACvC3M,gBAAgB,CAAC2M,aAAa,EAAEhN,MAAM,EAAE,eAAe,CAAC;;IAExD;IACA,IAAIA,MAAM,CAACnB,IAAI,EAAE;MACf0H,oBAAoB,CAACvG,MAAM,CAACnB,IAAI,EAAEmO,aAAa,CAAC;MAChDhL,IAAI,CAACgL,aAAa,EAAE,OAAO,CAAC;IAC9B;;IAEA;IAAA,KACK,IAAIhN,MAAM,CAACwL,IAAI,EAAE;MACpBwB,aAAa,CAAClO,WAAW,GAAGkB,MAAM,CAACwL,IAAI;MACvCxJ,IAAI,CAACgL,aAAa,EAAE,OAAO,CAAC;IAC9B;;IAEA;IAAA,KACK;MACH9K,IAAI,CAAC8K,aAAa,CAAC;IACrB;IACAtD,WAAW,CAAC5C,QAAQ,EAAE9G,MAAM,CAAC;EAC/B,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMiN,YAAY,GAAGA,CAACnG,QAAQ,EAAE9G,MAAM,KAAK;IACzC,MAAMpD,MAAM,GAAGD,SAAS,CAAC,CAAC;IAC1B,IAAI,CAACC,MAAM,EAAE;MACX;IACF;IACAuF,wBAAwB,CAACvF,MAAM,CAAC;IAChCyF,MAAM,CAACzF,MAAM,EAAEoD,MAAM,CAACpD,MAAM,EAAE,OAAO,CAAC;IACtC,IAAIoD,MAAM,CAACpD,MAAM,EAAE;MACjB2J,oBAAoB,CAACvG,MAAM,CAACpD,MAAM,EAAEA,MAAM,CAAC;IAC7C;;IAEA;IACAyD,gBAAgB,CAACzD,MAAM,EAAEoD,MAAM,EAAE,QAAQ,CAAC;EAC5C,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMkN,UAAU,GAAGA,CAACpG,QAAQ,EAAE9G,MAAM,KAAK;IACvC,MAAMsJ,WAAW,GAAGD,YAAY,CAACC,WAAW,CAACjS,GAAG,CAACyP,QAAQ,CAAC;IAC1D,MAAMxN,IAAI,GAAGiC,OAAO,CAAC,CAAC;IACtB,IAAI,CAACjC,IAAI,EAAE;MACT;IACF;;IAEA;IACA,IAAIgQ,WAAW,IAAItJ,MAAM,CAAC1G,IAAI,KAAKgQ,WAAW,CAAChQ,IAAI,EAAE;MACnD;MACA6T,UAAU,CAAC7T,IAAI,EAAE0G,MAAM,CAAC;MACxBoN,WAAW,CAAC9T,IAAI,EAAE0G,MAAM,CAAC;MACzB;IACF;IACA,IAAI,CAACA,MAAM,CAAC1G,IAAI,IAAI,CAAC0G,MAAM,CAACqN,QAAQ,EAAE;MACpCnL,IAAI,CAAC5I,IAAI,CAAC;MACV;IACF;IACA,IAAI0G,MAAM,CAAC1G,IAAI,IAAI2G,MAAM,CAACiK,IAAI,CAAC7Q,SAAS,CAAC,CAACiU,OAAO,CAACtN,MAAM,CAAC1G,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;MACrEW,KAAK,CAAC,oFAAoF+F,MAAM,CAAC1G,IAAI,GAAG,CAAC;MACzG4I,IAAI,CAAC5I,IAAI,CAAC;MACV;IACF;IACA0I,IAAI,CAAC1I,IAAI,CAAC;;IAEV;IACA6T,UAAU,CAAC7T,IAAI,EAAE0G,MAAM,CAAC;IACxBoN,WAAW,CAAC9T,IAAI,EAAE0G,MAAM,CAAC;;IAEzB;IACAO,QAAQ,CAACjH,IAAI,EAAE0G,MAAM,CAACG,SAAS,IAAIH,MAAM,CAACG,SAAS,CAAC7G,IAAI,CAAC;;IAEzD;IACA,MAAMiU,oBAAoB,GAAGhV,MAAM,CAACiV,UAAU,CAAC,8BAA8B,CAAC;IAC9ED,oBAAoB,CAACE,gBAAgB,CAAC,QAAQ,EAAEC,gCAAgC,CAAC;EACnF,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMN,WAAW,GAAGA,CAAC9T,IAAI,EAAE0G,MAAM,KAAK;IACpC,KAAK,MAAM,CAAC2N,QAAQ,EAAEC,aAAa,CAAC,IAAI3N,MAAM,CAAC4N,OAAO,CAACxU,SAAS,CAAC,EAAE;MACjE,IAAI2G,MAAM,CAAC1G,IAAI,KAAKqU,QAAQ,EAAE;QAC5BnM,WAAW,CAAClI,IAAI,EAAEsU,aAAa,CAAC;MAClC;IACF;IACArN,QAAQ,CAACjH,IAAI,EAAE0G,MAAM,CAAC1G,IAAI,IAAID,SAAS,CAAC2G,MAAM,CAAC1G,IAAI,CAAC,CAAC;;IAErD;IACAwU,QAAQ,CAACxU,IAAI,EAAE0G,MAAM,CAAC;;IAEtB;IACA0N,gCAAgC,CAAC,CAAC;;IAElC;IACArN,gBAAgB,CAAC/G,IAAI,EAAE0G,MAAM,EAAE,MAAM,CAAC;EACxC,CAAC;;EAED;EACA,MAAM0N,gCAAgC,GAAGA,CAAA,KAAM;IAC7C,MAAMpS,KAAK,GAAGD,QAAQ,CAAC,CAAC;IACxB,IAAI,CAACC,KAAK,EAAE;MACV;IACF;IACA,MAAMyS,oBAAoB,GAAGxV,MAAM,CAACkL,gBAAgB,CAACnI,KAAK,CAAC,CAACsI,gBAAgB,CAAC,kBAAkB,CAAC;IAChG;IACA,MAAMoK,gBAAgB,GAAG1S,KAAK,CAAC6B,gBAAgB,CAAC,0DAA0D,CAAC;IAC3G,KAAK,IAAI0C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmO,gBAAgB,CAACjX,MAAM,EAAE8I,CAAC,EAAE,EAAE;MAChDmO,gBAAgB,CAACnO,CAAC,CAAC,CAACgC,KAAK,CAACuG,eAAe,GAAG2F,oBAAoB;IAClE;EACF,CAAC;EACD,MAAME,eAAe,GAAG;AAC1B;AACA;AACA;AACA;AACA,CAAC;EACC,MAAMC,aAAa,GAAG;AACxB;AACA;AACA;AACA;AACA,CAAC;;EAEC;AACF;AACA;AACA;EACE,MAAMf,UAAU,GAAGA,CAAC7T,IAAI,EAAE0G,MAAM,KAAK;IACnC,IAAI,CAACA,MAAM,CAAC1G,IAAI,IAAI,CAAC0G,MAAM,CAACqN,QAAQ,EAAE;MACpC;IACF;IACA,IAAIc,UAAU,GAAG7U,IAAI,CAACgJ,SAAS;IAC/B,IAAI8L,UAAU,GAAG,EAAE;IACnB,IAAIpO,MAAM,CAACqN,QAAQ,EAAE;MACnBe,UAAU,GAAGC,WAAW,CAACrO,MAAM,CAACqN,QAAQ,CAAC;IAC3C,CAAC,MAAM,IAAIrN,MAAM,CAAC1G,IAAI,KAAK,SAAS,EAAE;MACpC8U,UAAU,GAAGH,eAAe;MAC5BE,UAAU,GAAGA,UAAU,CAACrJ,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC,CAAC;IACxD,CAAC,MAAM,IAAI9E,MAAM,CAAC1G,IAAI,KAAK,OAAO,EAAE;MAClC8U,UAAU,GAAGF,aAAa;IAC5B,CAAC,MAAM,IAAIlO,MAAM,CAAC1G,IAAI,EAAE;MACtB,MAAMgV,eAAe,GAAG;QACtBC,QAAQ,EAAE,GAAG;QACbC,OAAO,EAAE,GAAG;QACZC,IAAI,EAAE;MACR,CAAC;MACDL,UAAU,GAAGC,WAAW,CAACC,eAAe,CAACtO,MAAM,CAAC1G,IAAI,CAAC,CAAC;IACxD;IACA,IAAI6U,UAAU,CAACO,IAAI,CAAC,CAAC,KAAKN,UAAU,CAACM,IAAI,CAAC,CAAC,EAAE;MAC3C/P,YAAY,CAACrF,IAAI,EAAE8U,UAAU,CAAC;IAChC;EACF,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMN,QAAQ,GAAGA,CAACxU,IAAI,EAAE0G,MAAM,KAAK;IACjC,IAAI,CAACA,MAAM,CAAC2O,SAAS,EAAE;MACrB;IACF;IACArV,IAAI,CAACuI,KAAK,CAAC+M,KAAK,GAAG5O,MAAM,CAAC2O,SAAS;IACnCrV,IAAI,CAACuI,KAAK,CAACgN,WAAW,GAAG7O,MAAM,CAAC2O,SAAS;IACzC,KAAK,MAAMG,GAAG,IAAI,CAAC,yBAAyB,EAAE,0BAA0B,EAAE,yBAAyB,EAAE,0BAA0B,CAAC,EAAE;MAChIpM,QAAQ,CAACpJ,IAAI,EAAEwV,GAAG,EAAE,kBAAkB,EAAE9O,MAAM,CAAC2O,SAAS,CAAC;IAC3D;IACAjM,QAAQ,CAACpJ,IAAI,EAAE,qBAAqB,EAAE,cAAc,EAAE0G,MAAM,CAAC2O,SAAS,CAAC;EACzE,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMN,WAAW,GAAGU,OAAO,IAAI,eAAe/V,WAAW,CAAC,cAAc,CAAC,KAAK+V,OAAO,QAAQ;;EAE7F;AACF;AACA;AACA;EACE,MAAMC,WAAW,GAAGA,CAAClI,QAAQ,EAAE9G,MAAM,KAAK;IACxC,MAAMnE,KAAK,GAAGD,QAAQ,CAAC,CAAC;IACxB,IAAI,CAACC,KAAK,EAAE;MACV;IACF;IACA,IAAI,CAACmE,MAAM,CAACiP,QAAQ,EAAE;MACpB/M,IAAI,CAACrG,KAAK,CAAC;MACX;IACF;IACAmG,IAAI,CAACnG,KAAK,EAAE,EAAE,CAAC;;IAEf;IACAA,KAAK,CAAC8J,YAAY,CAAC,KAAK,EAAE3F,MAAM,CAACiP,QAAQ,CAAC;IAC1CpT,KAAK,CAAC8J,YAAY,CAAC,KAAK,EAAE3F,MAAM,CAACkP,QAAQ,IAAI,EAAE,CAAC;;IAEhD;IACAvN,mBAAmB,CAAC9F,KAAK,EAAE,OAAO,EAAEmE,MAAM,CAACmP,UAAU,CAAC;IACtDxN,mBAAmB,CAAC9F,KAAK,EAAE,QAAQ,EAAEmE,MAAM,CAACoP,WAAW,CAAC;;IAExD;IACAvT,KAAK,CAAC1C,SAAS,GAAGH,WAAW,CAAC6C,KAAK;IACnCwE,gBAAgB,CAACxE,KAAK,EAAEmE,MAAM,EAAE,OAAO,CAAC;EAC1C,CAAC;EAED,IAAIqP,QAAQ,GAAG,KAAK;EACpB,IAAIC,UAAU,GAAG,CAAC;EAClB,IAAIC,UAAU,GAAG,CAAC;EAClB,IAAIC,QAAQ,GAAG,CAAC;EAChB,IAAIC,QAAQ,GAAG,CAAC;;EAEhB;AACF;AACA;EACE,MAAMC,qBAAqB,GAAGpU,KAAK,IAAI;IACrCA,KAAK,CAACmS,gBAAgB,CAAC,WAAW,EAAEkC,IAAI,CAAC;IACzC3X,QAAQ,CAACC,IAAI,CAACwV,gBAAgB,CAAC,WAAW,EAAEmC,IAAI,CAAC;IACjDtU,KAAK,CAACmS,gBAAgB,CAAC,SAAS,EAAEoC,EAAE,CAAC;IACrCvU,KAAK,CAACmS,gBAAgB,CAAC,YAAY,EAAEkC,IAAI,CAAC;IAC1C3X,QAAQ,CAACC,IAAI,CAACwV,gBAAgB,CAAC,WAAW,EAAEmC,IAAI,CAAC;IACjDtU,KAAK,CAACmS,gBAAgB,CAAC,UAAU,EAAEoC,EAAE,CAAC;EACxC,CAAC;;EAED;AACF;AACA;EACE,MAAMC,wBAAwB,GAAGxU,KAAK,IAAI;IACxCA,KAAK,CAACyU,mBAAmB,CAAC,WAAW,EAAEJ,IAAI,CAAC;IAC5C3X,QAAQ,CAACC,IAAI,CAAC8X,mBAAmB,CAAC,WAAW,EAAEH,IAAI,CAAC;IACpDtU,KAAK,CAACyU,mBAAmB,CAAC,SAAS,EAAEF,EAAE,CAAC;IACxCvU,KAAK,CAACyU,mBAAmB,CAAC,YAAY,EAAEJ,IAAI,CAAC;IAC7C3X,QAAQ,CAACC,IAAI,CAAC8X,mBAAmB,CAAC,WAAW,EAAEH,IAAI,CAAC;IACpDtU,KAAK,CAACyU,mBAAmB,CAAC,UAAU,EAAEF,EAAE,CAAC;EAC3C,CAAC;;EAED;AACF;AACA;EACE,MAAMF,IAAI,GAAGK,KAAK,IAAI;IACpB,MAAM1U,KAAK,GAAGD,QAAQ,CAAC,CAAC;IACxB,IAAI2U,KAAK,CAAC7O,MAAM,KAAK7F,KAAK,IAAIC,OAAO,CAAC,CAAC,CAACuE,QAAQ,CAAC,0BAA0BkQ,KAAK,CAAC7O,MAAM,CAAC,EAAE;MACxFkO,QAAQ,GAAG,IAAI;MACf,MAAMY,QAAQ,GAAGC,WAAW,CAACF,KAAK,CAAC;MACnCV,UAAU,GAAGW,QAAQ,CAACE,OAAO;MAC7BZ,UAAU,GAAGU,QAAQ,CAACG,OAAO;MAC7BZ,QAAQ,GAAG9R,QAAQ,CAACpC,KAAK,CAACuG,KAAK,CAACwO,gBAAgB,CAAC,IAAI,CAAC;MACtDZ,QAAQ,GAAG/R,QAAQ,CAACpC,KAAK,CAACuG,KAAK,CAACyO,eAAe,CAAC,IAAI,CAAC;MACrD/P,QAAQ,CAACjF,KAAK,EAAE,gBAAgB,CAAC;IACnC;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAMsU,IAAI,GAAGI,KAAK,IAAI;IACpB,MAAM1U,KAAK,GAAGD,QAAQ,CAAC,CAAC;IACxB,IAAIgU,QAAQ,EAAE;MACZ,IAAI;QACFc,OAAO;QACPC;MACF,CAAC,GAAGF,WAAW,CAACF,KAAK,CAAC;MACtB1U,KAAK,CAACuG,KAAK,CAACwO,gBAAgB,GAAG,GAAGb,QAAQ,IAAIW,OAAO,GAAGb,UAAU,CAAC,IAAI;MACvEhU,KAAK,CAACuG,KAAK,CAACyO,eAAe,GAAG,GAAGb,QAAQ,IAAIW,OAAO,GAAGb,UAAU,CAAC,IAAI;IACxE;EACF,CAAC;EACD,MAAMM,EAAE,GAAGA,CAAA,KAAM;IACf,MAAMvU,KAAK,GAAGD,QAAQ,CAAC,CAAC;IACxBgU,QAAQ,GAAG,KAAK;IAChB7N,WAAW,CAAClG,KAAK,EAAE,gBAAgB,CAAC;EACtC,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAM4U,WAAW,GAAGF,KAAK,IAAI;IAC3B,IAAIG,OAAO,GAAG,CAAC;MACbC,OAAO,GAAG,CAAC;IACb,IAAIJ,KAAK,CAACjP,IAAI,CAACwP,UAAU,CAAC,OAAO,CAAC,EAAE;MAClCJ,OAAO,GAAG,yBAAyBH,KAAK,CAACG,OAAO;MAChDC,OAAO,GAAG,yBAAyBJ,KAAK,CAACI,OAAO;IAClD,CAAC,MAAM,IAAIJ,KAAK,CAACjP,IAAI,CAACwP,UAAU,CAAC,OAAO,CAAC,EAAE;MACzCJ,OAAO,GAAG,yBAAyBH,KAAK,CAACQ,OAAO,CAAC,CAAC,CAAC,CAACL,OAAO;MAC3DC,OAAO,GAAG,yBAAyBJ,KAAK,CAACQ,OAAO,CAAC,CAAC,CAAC,CAACJ,OAAO;IAC7D;IACA,OAAO;MACLD,OAAO;MACPC;IACF,CAAC;EACH,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMK,WAAW,GAAGA,CAAC3J,QAAQ,EAAE9G,MAAM,KAAK;IACxC,MAAM/E,SAAS,GAAGF,YAAY,CAAC,CAAC;IAChC,MAAMO,KAAK,GAAGD,QAAQ,CAAC,CAAC;IACxB,IAAI,CAACJ,SAAS,IAAI,CAACK,KAAK,EAAE;MACxB;IACF;;IAEA;IACA;IACA,IAAI0E,MAAM,CAACxB,KAAK,EAAE;MAChBmD,mBAAmB,CAAC1G,SAAS,EAAE,OAAO,EAAE+E,MAAM,CAACmE,KAAK,CAAC;MACrD7I,KAAK,CAACuG,KAAK,CAACsC,KAAK,GAAG,MAAM;MAC1B,MAAM1H,MAAM,GAAGD,SAAS,CAAC,CAAC;MAC1B,IAAIC,MAAM,EAAE;QACVnB,KAAK,CAACoM,YAAY,CAACjL,MAAM,EAAElB,OAAO,CAAC,CAAC,CAAC;MACvC;IACF,CAAC,MAAM;MACLoG,mBAAmB,CAACrG,KAAK,EAAE,OAAO,EAAE0E,MAAM,CAACmE,KAAK,CAAC;IACnD;;IAEA;IACAxC,mBAAmB,CAACrG,KAAK,EAAE,SAAS,EAAE0E,MAAM,CAAC0Q,OAAO,CAAC;;IAErD;IACA,IAAI1Q,MAAM,CAAC4O,KAAK,EAAE;MAChBtT,KAAK,CAACuG,KAAK,CAAC+M,KAAK,GAAG5O,MAAM,CAAC4O,KAAK;IAClC;;IAEA;IACA,IAAI5O,MAAM,CAACmJ,UAAU,EAAE;MACrB7N,KAAK,CAACuG,KAAK,CAACsH,UAAU,GAAGnJ,MAAM,CAACmJ,UAAU;IAC5C;IACAjH,IAAI,CAACnG,oBAAoB,CAAC,CAAC,CAAC;;IAE5B;IACA4U,YAAY,CAACrV,KAAK,EAAE0E,MAAM,CAAC;IAC3B,IAAIA,MAAM,CAAC4Q,SAAS,IAAI,CAAC5Q,MAAM,CAACxB,KAAK,EAAE;MACrC+B,QAAQ,CAACjF,KAAK,EAAEtC,WAAW,CAAC4X,SAAS,CAAC;MACtClB,qBAAqB,CAACpU,KAAK,CAAC;IAC9B,CAAC,MAAM;MACLkG,WAAW,CAAClG,KAAK,EAAEtC,WAAW,CAAC4X,SAAS,CAAC;MACzCd,wBAAwB,CAACxU,KAAK,CAAC;IACjC;EACF,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMqV,YAAY,GAAGA,CAACrV,KAAK,EAAE0E,MAAM,KAAK;IACtC,MAAMG,SAAS,GAAGH,MAAM,CAACG,SAAS,IAAI,CAAC,CAAC;IACxC;IACA7E,KAAK,CAACnC,SAAS,GAAG,GAAGH,WAAW,CAACsC,KAAK,IAAI6C,WAAW,CAAC7C,KAAK,CAAC,GAAG6E,SAAS,CAAC7E,KAAK,GAAG,EAAE,EAAE;IACrF,IAAI0E,MAAM,CAACxB,KAAK,EAAE;MAChB+B,QAAQ,CAAC,CAACvI,QAAQ,CAACiN,eAAe,EAAEjN,QAAQ,CAACC,IAAI,CAAC,EAAEe,WAAW,CAAC,aAAa,CAAC,CAAC;MAC/EuH,QAAQ,CAACjF,KAAK,EAAEtC,WAAW,CAACwF,KAAK,CAAC;IACpC,CAAC,MAAM;MACL+B,QAAQ,CAACjF,KAAK,EAAEtC,WAAW,CAAC6X,KAAK,CAAC;IACpC;;IAEA;IACAxQ,gBAAgB,CAAC/E,KAAK,EAAE0E,MAAM,EAAE,OAAO,CAAC;IACxC;IACA,IAAI,OAAOA,MAAM,CAACM,WAAW,KAAK,QAAQ,EAAE;MAC1CC,QAAQ,CAACjF,KAAK,EAAE0E,MAAM,CAACM,WAAW,CAAC;IACrC;;IAEA;IACA,IAAIN,MAAM,CAAC1G,IAAI,EAAE;MACfiH,QAAQ,CAACjF,KAAK,EAAEtC,WAAW,CAAC,QAAQgH,MAAM,CAAC1G,IAAI,EAAE,CAAC,CAAC;IACrD;EACF,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMwX,mBAAmB,GAAGA,CAAChK,QAAQ,EAAE9G,MAAM,KAAK;IAChD,MAAM+Q,sBAAsB,GAAGjV,gBAAgB,CAAC,CAAC;IACjD,IAAI,CAACiV,sBAAsB,EAAE;MAC3B;IACF;IACA,MAAM;MACJC,aAAa;MACbC;IACF,CAAC,GAAGjR,MAAM;IACV,IAAI,CAACgR,aAAa,IAAIA,aAAa,CAACja,MAAM,KAAK,CAAC,IAAIka,mBAAmB,KAAKC,SAAS,EAAE;MACrFhP,IAAI,CAAC6O,sBAAsB,CAAC;MAC5B;IACF;IACA/O,IAAI,CAAC+O,sBAAsB,CAAC;IAC5BA,sBAAsB,CAACjS,WAAW,GAAG,EAAE;IACvC,IAAImS,mBAAmB,IAAID,aAAa,CAACja,MAAM,EAAE;MAC/C8C,IAAI,CAAC,qFAAqF,GAAG,oDAAoD,CAAC;IACpJ;IACAmX,aAAa,CAAC3R,OAAO,CAAC,CAAC8R,IAAI,EAAEC,KAAK,KAAK;MACrC,MAAMC,MAAM,GAAGC,iBAAiB,CAACH,IAAI,CAAC;MACtCJ,sBAAsB,CAACxR,WAAW,CAAC8R,MAAM,CAAC;MAC1C,IAAID,KAAK,KAAKH,mBAAmB,EAAE;QACjC1Q,QAAQ,CAAC8Q,MAAM,EAAErY,WAAW,CAAC,sBAAsB,CAAC,CAAC;MACvD;MACA,IAAIoY,KAAK,KAAKJ,aAAa,CAACja,MAAM,GAAG,CAAC,EAAE;QACtC,MAAMwa,MAAM,GAAGC,iBAAiB,CAACxR,MAAM,CAAC;QACxC+Q,sBAAsB,CAACxR,WAAW,CAACgS,MAAM,CAAC;MAC5C;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMD,iBAAiB,GAAGH,IAAI,IAAI;IAChC,MAAME,MAAM,GAAGrZ,QAAQ,CAACkO,aAAa,CAAC,IAAI,CAAC;IAC3C3F,QAAQ,CAAC8Q,MAAM,EAAErY,WAAW,CAAC,eAAe,CAAC,CAAC;IAC9C2F,YAAY,CAAC0S,MAAM,EAAEF,IAAI,CAAC;IAC1B,OAAOE,MAAM;EACf,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMG,iBAAiB,GAAGxR,MAAM,IAAI;IAClC,MAAMuR,MAAM,GAAGvZ,QAAQ,CAACkO,aAAa,CAAC,IAAI,CAAC;IAC3C3F,QAAQ,CAACgR,MAAM,EAAEvY,WAAW,CAAC,oBAAoB,CAAC,CAAC;IACnD,IAAIgH,MAAM,CAACyR,qBAAqB,EAAE;MAChC9P,mBAAmB,CAAC4P,MAAM,EAAE,OAAO,EAAEvR,MAAM,CAACyR,qBAAqB,CAAC;IACpE;IACA,OAAOF,MAAM;EACf,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMG,WAAW,GAAGA,CAAC5K,QAAQ,EAAE9G,MAAM,KAAK;IACxC,MAAMtE,KAAK,GAAGD,QAAQ,CAAC,CAAC;IACxB,IAAI,CAACC,KAAK,EAAE;MACV;IACF;IACAyG,wBAAwB,CAACzG,KAAK,CAAC;IAC/B2G,MAAM,CAAC3G,KAAK,EAAEsE,MAAM,CAACtE,KAAK,IAAIsE,MAAM,CAAC2R,SAAS,EAAE,OAAO,CAAC;IACxD,IAAI3R,MAAM,CAACtE,KAAK,EAAE;MAChB6K,oBAAoB,CAACvG,MAAM,CAACtE,KAAK,EAAEA,KAAK,CAAC;IAC3C;IACA,IAAIsE,MAAM,CAAC2R,SAAS,EAAE;MACpBjW,KAAK,CAACyP,SAAS,GAAGnL,MAAM,CAAC2R,SAAS;IACpC;;IAEA;IACAtR,gBAAgB,CAAC3E,KAAK,EAAEsE,MAAM,EAAE,OAAO,CAAC;EAC1C,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAM4R,MAAM,GAAGA,CAAC9K,QAAQ,EAAE9G,MAAM,KAAK;IACnCyQ,WAAW,CAAC3J,QAAQ,EAAE9G,MAAM,CAAC;IAC7B4I,eAAe,CAAC9B,QAAQ,EAAE9G,MAAM,CAAC;IACjC8Q,mBAAmB,CAAChK,QAAQ,EAAE9G,MAAM,CAAC;IACrCkN,UAAU,CAACpG,QAAQ,EAAE9G,MAAM,CAAC;IAC5BgP,WAAW,CAAClI,QAAQ,EAAE9G,MAAM,CAAC;IAC7B0R,WAAW,CAAC5K,QAAQ,EAAE9G,MAAM,CAAC;IAC7BuI,iBAAiB,CAACzB,QAAQ,EAAE9G,MAAM,CAAC;IACnC+M,aAAa,CAACjG,QAAQ,EAAE9G,MAAM,CAAC;IAC/B6G,aAAa,CAACC,QAAQ,EAAE9G,MAAM,CAAC;IAC/BiN,YAAY,CAACnG,QAAQ,EAAE9G,MAAM,CAAC;IAC9B,MAAM1E,KAAK,GAAGD,QAAQ,CAAC,CAAC;IACxB,IAAI,OAAO2E,MAAM,CAAC6R,SAAS,KAAK,UAAU,IAAIvW,KAAK,EAAE;MACnD0E,MAAM,CAAC6R,SAAS,CAACvW,KAAK,CAAC;IACzB;IACA3D,WAAW,CAACma,YAAY,CAACC,IAAI,CAAC,WAAW,EAAEzW,KAAK,CAAC;EACnD,CAAC;;EAED;AACF;AACA;EACE,MAAM0W,SAAS,GAAGA,CAAA,KAAM;IACtB,OAAO7T,WAAW,CAAC9C,QAAQ,CAAC,CAAC,CAAC;EAChC,CAAC;;EAED;AACF;AACA;EACE,MAAM4W,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIC,qBAAqB;IACzB,OAAO,CAACA,qBAAqB,GAAGlW,gBAAgB,CAAC,CAAC,MAAM,IAAI,IAAIkW,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACC,KAAK,CAAC,CAAC;EAC3I,CAAC;;EAED;AACF;AACA;EACE,MAAMC,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAIC,kBAAkB;IACtB,OAAO,CAACA,kBAAkB,GAAGhW,aAAa,CAAC,CAAC,MAAM,IAAI,IAAIgW,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACF,KAAK,CAAC,CAAC;EAC/H,CAAC;;EAED;AACF;AACA;EACE,MAAMG,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIC,oBAAoB;IACxB,OAAO,CAACA,oBAAoB,GAAGpW,eAAe,CAAC,CAAC,MAAM,IAAI,IAAIoW,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,oBAAoB,CAACJ,KAAK,CAAC,CAAC;EACvI,CAAC;;EAED;;EAEA;EACA,MAAMK,aAAa,GAAGvS,MAAM,CAACwS,MAAM,CAAC;IAClCrW,MAAM,EAAE,QAAQ;IAChB0M,QAAQ,EAAE,UAAU;IACpB/L,KAAK,EAAE,OAAO;IACd2V,GAAG,EAAE,KAAK;IACV3O,KAAK,EAAE;EACT,CAAC,CAAC;;EAEF;AACF;AACA;EACE,MAAM4O,oBAAoB,GAAGhb,WAAW,IAAI;IAC1C,IAAIA,WAAW,CAACib,aAAa,IAAIjb,WAAW,CAACkb,mBAAmB,EAAE;MAChElb,WAAW,CAACib,aAAa,CAAC7C,mBAAmB,CAAC,SAAS,EAAEpY,WAAW,CAACmb,cAAc,EAAE;QACnFC,OAAO,EAAEpb,WAAW,CAACqb;MACvB,CAAC,CAAC;MACFrb,WAAW,CAACkb,mBAAmB,GAAG,KAAK;IACzC;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMI,iBAAiB,GAAGA,CAACtb,WAAW,EAAE2R,WAAW,EAAE4J,WAAW,KAAK;IACnEP,oBAAoB,CAAChb,WAAW,CAAC;IACjC,IAAI,CAAC2R,WAAW,CAAC9K,KAAK,EAAE;MACtB7G,WAAW,CAACmb,cAAc,GAAGpc,CAAC,IAAIoc,cAAc,CAACxJ,WAAW,EAAE5S,CAAC,EAAEwc,WAAW,CAAC;MAC7Evb,WAAW,CAACib,aAAa,GAAGtJ,WAAW,CAAC0J,sBAAsB,GAAGza,MAAM,GAAG8C,QAAQ,CAAC,CAAC;MACpF1D,WAAW,CAACqb,sBAAsB,GAAG1J,WAAW,CAAC0J,sBAAsB;MACvErb,WAAW,CAACib,aAAa,CAACnF,gBAAgB,CAAC,SAAS,EAAE9V,WAAW,CAACmb,cAAc,EAAE;QAChFC,OAAO,EAAEpb,WAAW,CAACqb;MACvB,CAAC,CAAC;MACFrb,WAAW,CAACkb,mBAAmB,GAAG,IAAI;IACxC;EACF,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMM,QAAQ,GAAGA,CAAC/B,KAAK,EAAEgC,SAAS,KAAK;IACrC,IAAIC,aAAa;IACjB,MAAMC,iBAAiB,GAAGrW,oBAAoB,CAAC,CAAC;IAChD;IACA,IAAIqW,iBAAiB,CAACvc,MAAM,EAAE;MAC5Bqa,KAAK,GAAGA,KAAK,GAAGgC,SAAS;;MAEzB;MACA,IAAIhC,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBA,KAAK,GAAGkC,iBAAiB,CAACvc,MAAM,GAAG,CAAC;MACtC;;MAEA;MACA,IAAIqa,KAAK,KAAKkC,iBAAiB,CAACvc,MAAM,EAAE;QACtCqa,KAAK,GAAG,CAAC;;QAET;MACF,CAAC,MAAM,IAAIA,KAAK,KAAK,CAAC,CAAC,EAAE;QACvBA,KAAK,GAAGkC,iBAAiB,CAACvc,MAAM,GAAG,CAAC;MACtC;MACAuc,iBAAiB,CAAClC,KAAK,CAAC,CAACrZ,KAAK,CAAC,CAAC;MAChC;IACF;IACA;IACA,CAACsb,aAAa,GAAGhY,QAAQ,CAAC,CAAC,MAAM,IAAI,IAAIgY,aAAa,KAAK,KAAK,CAAC,IAAIA,aAAa,CAACtb,KAAK,CAAC,CAAC;EAC5F,CAAC;EACD,MAAMwb,mBAAmB,GAAG,CAAC,YAAY,EAAE,WAAW,CAAC;EACvD,MAAMC,uBAAuB,GAAG,CAAC,WAAW,EAAE,SAAS,CAAC;;EAExD;AACF;AACA;AACA;AACA;EACE,MAAMV,cAAc,GAAGA,CAACxJ,WAAW,EAAE0G,KAAK,EAAEkD,WAAW,KAAK;IAC1D,IAAI,CAAC5J,WAAW,EAAE;MAChB,OAAO,CAAC;IACV;;IAEA;IACA;IACA;IACA;IACA,IAAI0G,KAAK,CAACyD,WAAW,IAAIzD,KAAK,CAAC0D,OAAO,KAAK,GAAG,EAAE;MAC9C;IACF;IACA,IAAIpK,WAAW,CAACqK,sBAAsB,EAAE;MACtC3D,KAAK,CAAC4D,eAAe,CAAC,CAAC;IACzB;;IAEA;IACA,IAAI5D,KAAK,CAAC6D,GAAG,KAAK,OAAO,EAAE;MACzBC,WAAW,CAAC9D,KAAK,EAAE1G,WAAW,CAAC;IACjC;;IAEA;IAAA,KACK,IAAI0G,KAAK,CAAC6D,GAAG,KAAK,KAAK,EAAE;MAC5BE,SAAS,CAAC/D,KAAK,CAAC;IAClB;;IAEA;IAAA,KACK,IAAI,CAAC,GAAGuD,mBAAmB,EAAE,GAAGC,uBAAuB,CAAC,CAACpZ,QAAQ,CAAC4V,KAAK,CAAC6D,GAAG,CAAC,EAAE;MACjFG,YAAY,CAAChE,KAAK,CAAC6D,GAAG,CAAC;IACzB;;IAEA;IAAA,KACK,IAAI7D,KAAK,CAAC6D,GAAG,KAAK,QAAQ,EAAE;MAC/BI,SAAS,CAACjE,KAAK,EAAE1G,WAAW,EAAE4J,WAAW,CAAC;IAC5C;EACF,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMY,WAAW,GAAGA,CAAC9D,KAAK,EAAE1G,WAAW,KAAK;IAC1C;IACA,IAAI,CAAC7O,cAAc,CAAC6O,WAAW,CAAC4K,aAAa,CAAC,EAAE;MAC9C;IACF;IACA,MAAMrT,KAAK,GAAGL,UAAU,CAACnF,QAAQ,CAAC,CAAC,EAAEiO,WAAW,CAACzI,KAAK,CAAC;IACvD,IAAImP,KAAK,CAAC7O,MAAM,IAAIN,KAAK,IAAImP,KAAK,CAAC7O,MAAM,YAAYrJ,WAAW,IAAIkY,KAAK,CAAC7O,MAAM,CAACgT,SAAS,KAAKtT,KAAK,CAACsT,SAAS,EAAE;MAC9G,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC/Z,QAAQ,CAACkP,WAAW,CAACzI,KAAK,CAAC,EAAE;QACpD,OAAO,CAAC;MACV;MACAoR,YAAY,CAAC,CAAC;MACdjC,KAAK,CAACoE,cAAc,CAAC,CAAC;IACxB;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAML,SAAS,GAAG/D,KAAK,IAAI;IACzB,MAAMnK,aAAa,GAAGmK,KAAK,CAAC7O,MAAM;IAClC,MAAMmS,iBAAiB,GAAGrW,oBAAoB,CAAC,CAAC;IAChD,IAAIoX,QAAQ,GAAG,CAAC,CAAC;IACjB,KAAK,IAAIxU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyT,iBAAiB,CAACvc,MAAM,EAAE8I,CAAC,EAAE,EAAE;MACjD,IAAIgG,aAAa,KAAKyN,iBAAiB,CAACzT,CAAC,CAAC,EAAE;QAC1CwU,QAAQ,GAAGxU,CAAC;QACZ;MACF;IACF;;IAEA;IACA,IAAI,CAACmQ,KAAK,CAACsE,QAAQ,EAAE;MACnBnB,QAAQ,CAACkB,QAAQ,EAAE,CAAC,CAAC;IACvB;;IAEA;IAAA,KACK;MACHlB,QAAQ,CAACkB,QAAQ,EAAE,CAAC,CAAC,CAAC;IACxB;IACArE,KAAK,CAAC4D,eAAe,CAAC,CAAC;IACvB5D,KAAK,CAACoE,cAAc,CAAC,CAAC;EACxB,CAAC;;EAED;AACF;AACA;EACE,MAAMJ,YAAY,GAAGH,GAAG,IAAI;IAC1B,MAAM5X,OAAO,GAAGS,UAAU,CAAC,CAAC;IAC5B,MAAM0K,aAAa,GAAGpL,gBAAgB,CAAC,CAAC;IACxC,MAAMqL,UAAU,GAAGhL,aAAa,CAAC,CAAC;IAClC,MAAMiL,YAAY,GAAGnL,eAAe,CAAC,CAAC;IACtC,IAAI,CAACF,OAAO,IAAI,CAACmL,aAAa,IAAI,CAACC,UAAU,IAAI,CAACC,YAAY,EAAE;MAC9D;IACF;IACA;IACA,MAAMiN,OAAO,GAAG,CAACnN,aAAa,EAAEC,UAAU,EAAEC,YAAY,CAAC;IACzD,IAAItP,QAAQ,CAACwc,aAAa,YAAY1c,WAAW,IAAI,CAACyc,OAAO,CAACna,QAAQ,CAACpC,QAAQ,CAACwc,aAAa,CAAC,EAAE;MAC9F;IACF;IACA,MAAMC,OAAO,GAAGlB,mBAAmB,CAACnZ,QAAQ,CAACyZ,GAAG,CAAC,GAAG,oBAAoB,GAAG,wBAAwB;IACnG,IAAIa,aAAa,GAAG1c,QAAQ,CAACwc,aAAa;IAC1C,IAAI,CAACE,aAAa,EAAE;MAClB;IACF;IACA,KAAK,IAAI7U,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5D,OAAO,CAACyF,QAAQ,CAAC3K,MAAM,EAAE8I,CAAC,EAAE,EAAE;MAChD6U,aAAa,GAAGA,aAAa,CAACD,OAAO,CAAC;MACtC,IAAI,CAACC,aAAa,EAAE;QAClB;MACF;MACA,IAAIA,aAAa,YAAYC,iBAAiB,IAAIxW,WAAW,CAACuW,aAAa,CAAC,EAAE;QAC5E;MACF;IACF;IACA,IAAIA,aAAa,YAAYC,iBAAiB,EAAE;MAC9CD,aAAa,CAAC3c,KAAK,CAAC,CAAC;IACvB;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMkc,SAAS,GAAGA,CAACjE,KAAK,EAAE1G,WAAW,EAAE4J,WAAW,KAAK;IACrD,IAAIzY,cAAc,CAAC6O,WAAW,CAACsL,cAAc,CAAC,EAAE;MAC9C5E,KAAK,CAACoE,cAAc,CAAC,CAAC;MACtBlB,WAAW,CAACV,aAAa,CAACE,GAAG,CAAC;IAChC;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEE,IAAImC,cAAc,GAAG;IACnBC,kBAAkB,EAAE,IAAIvL,OAAO,CAAC,CAAC;IACjCwL,iBAAiB,EAAE,IAAIxL,OAAO,CAAC;EACjC,CAAC;;EAED;EACA;EACA;EACA;;EAEA,MAAMyL,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAM/Z,SAAS,GAAGF,YAAY,CAAC,CAAC;IAChC,MAAMka,YAAY,GAAG5X,KAAK,CAACC,IAAI,CAACtF,QAAQ,CAACC,IAAI,CAACyJ,QAAQ,CAAC;IACvDuT,YAAY,CAAC5V,OAAO,CAACrB,EAAE,IAAI;MACzB,IAAIA,EAAE,CAAC8B,QAAQ,CAAC7E,SAAS,CAAC,EAAE;QAC1B;MACF;MACA,IAAI+C,EAAE,CAACU,YAAY,CAAC,aAAa,CAAC,EAAE;QAClCV,EAAE,CAAC2H,YAAY,CAAC,2BAA2B,EAAE3H,EAAE,CAACL,YAAY,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;MACpF;MACAK,EAAE,CAAC2H,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IACxC,CAAC,CAAC;EACJ,CAAC;EACD,MAAMuP,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMD,YAAY,GAAG5X,KAAK,CAACC,IAAI,CAACtF,QAAQ,CAACC,IAAI,CAACyJ,QAAQ,CAAC;IACvDuT,YAAY,CAAC5V,OAAO,CAACrB,EAAE,IAAI;MACzB,IAAIA,EAAE,CAACU,YAAY,CAAC,2BAA2B,CAAC,EAAE;QAChDV,EAAE,CAAC2H,YAAY,CAAC,aAAa,EAAE3H,EAAE,CAACL,YAAY,CAAC,2BAA2B,CAAC,IAAI,EAAE,CAAC;QAClFK,EAAE,CAACyM,eAAe,CAAC,2BAA2B,CAAC;MACjD,CAAC,MAAM;QACLzM,EAAE,CAACyM,eAAe,CAAC,aAAa,CAAC;MACnC;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM0K,aAAa,GAAG,OAAO5c,MAAM,KAAK,WAAW,IAAI,CAAC,CAACA,MAAM,CAAC6c,YAAY,CAAC,CAAC;;EAE9E;AACF;AACA;AACA;EACE,MAAMC,MAAM,GAAGA,CAAA,KAAM;IACnB,IAAIF,aAAa,IAAI,CAAC9W,QAAQ,CAACrG,QAAQ,CAACC,IAAI,EAAEe,WAAW,CAACsc,MAAM,CAAC,EAAE;MACjE,MAAMC,MAAM,GAAGvd,QAAQ,CAACC,IAAI,CAACud,SAAS;MACtCxd,QAAQ,CAACC,IAAI,CAAC4J,KAAK,CAAC4T,GAAG,GAAG,GAAGF,MAAM,GAAG,CAAC,CAAC,IAAI;MAC5ChV,QAAQ,CAACvI,QAAQ,CAACC,IAAI,EAAEe,WAAW,CAACsc,MAAM,CAAC;MAC3CI,cAAc,CAAC,CAAC;IAClB;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAMA,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMza,SAAS,GAAGF,YAAY,CAAC,CAAC;IAChC,IAAI,CAACE,SAAS,EAAE;MACd;IACF;IACA;IACA,IAAI0a,gBAAgB;IACpB;AACJ;AACA;IACI1a,SAAS,CAAC2a,YAAY,GAAG5F,KAAK,IAAI;MAChC2F,gBAAgB,GAAGE,sBAAsB,CAAC7F,KAAK,CAAC;IAClD,CAAC;IACD;AACJ;AACA;IACI/U,SAAS,CAAC6a,WAAW,GAAG9F,KAAK,IAAI;MAC/B,IAAI2F,gBAAgB,EAAE;QACpB3F,KAAK,CAACoE,cAAc,CAAC,CAAC;QACtBpE,KAAK,CAAC4D,eAAe,CAAC,CAAC;MACzB;IACF,CAAC;EACH,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMiC,sBAAsB,GAAG7F,KAAK,IAAI;IACtC,MAAM7O,MAAM,GAAG6O,KAAK,CAAC7O,MAAM;IAC3B,MAAMlG,SAAS,GAAGF,YAAY,CAAC,CAAC;IAChC,MAAMiS,aAAa,GAAGrR,gBAAgB,CAAC,CAAC;IACxC,IAAI,CAACV,SAAS,IAAI,CAAC+R,aAAa,EAAE;MAChC,OAAO,KAAK;IACd;IACA,IAAI+I,QAAQ,CAAC/F,KAAK,CAAC,IAAIgG,MAAM,CAAChG,KAAK,CAAC,EAAE;MACpC,OAAO,KAAK;IACd;IACA,IAAI7O,MAAM,KAAKlG,SAAS,EAAE;MACxB,OAAO,IAAI;IACb;IACA,IAAI,CAACgI,YAAY,CAAChI,SAAS,CAAC,IAAIkG,MAAM,YAAYrJ,WAAW,IAAI,CAACsL,wBAAwB,CAACjC,MAAM,EAAE6L,aAAa,CAAC;IACjH;IACA7L,MAAM,CAAC8U,OAAO,KAAK,OAAO;IAC1B;IACA9U,MAAM,CAAC8U,OAAO,KAAK,UAAU;IAC7B;IACA,EAAEhT,YAAY,CAAC+J,aAAa,CAAC;IAC7B;IACAA,aAAa,CAAClN,QAAQ,CAACqB,MAAM,CAAC,CAAC,EAAE;MAC/B,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;EACE,MAAM4U,QAAQ,GAAG/F,KAAK,IAAI;IACxB,OAAOA,KAAK,CAACQ,OAAO,IAAIR,KAAK,CAACQ,OAAO,CAACzZ,MAAM,IAAIiZ,KAAK,CAACQ,OAAO,CAAC,CAAC,CAAC,CAAC0F,SAAS,KAAK,QAAQ;EACzF,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;EACE,MAAMF,MAAM,GAAGhG,KAAK,IAAI;IACtB,OAAOA,KAAK,CAACQ,OAAO,IAAIR,KAAK,CAACQ,OAAO,CAACzZ,MAAM,GAAG,CAAC;EAClD,CAAC;EACD,MAAMof,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI9X,QAAQ,CAACrG,QAAQ,CAACC,IAAI,EAAEe,WAAW,CAACsc,MAAM,CAAC,EAAE;MAC/C,MAAMC,MAAM,GAAG7X,QAAQ,CAAC1F,QAAQ,CAACC,IAAI,CAAC4J,KAAK,CAAC4T,GAAG,EAAE,EAAE,CAAC;MACpDjU,WAAW,CAACxJ,QAAQ,CAACC,IAAI,EAAEe,WAAW,CAACsc,MAAM,CAAC;MAC9Ctd,QAAQ,CAACC,IAAI,CAAC4J,KAAK,CAAC4T,GAAG,GAAG,EAAE;MAC5Bzd,QAAQ,CAACC,IAAI,CAACud,SAAS,GAAGD,MAAM,GAAG,CAAC,CAAC;IACvC;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;EACE,MAAMa,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,SAAS,GAAGre,QAAQ,CAACkO,aAAa,CAAC,KAAK,CAAC;IAC/CmQ,SAAS,CAACld,SAAS,GAAGH,WAAW,CAAC,mBAAmB,CAAC;IACtDhB,QAAQ,CAACC,IAAI,CAACsH,WAAW,CAAC8W,SAAS,CAAC;IACpC,MAAMC,cAAc,GAAGD,SAAS,CAACE,qBAAqB,CAAC,CAAC,CAACpS,KAAK,GAAGkS,SAAS,CAACG,WAAW;IACtFxe,QAAQ,CAACC,IAAI,CAACwe,WAAW,CAACJ,SAAS,CAAC;IACpC,OAAOC,cAAc;EACvB,CAAC;;EAED;AACF;AACA;AACA;EACE,IAAII,mBAAmB,GAAG,IAAI;;EAE9B;AACF;AACA;EACE,MAAMC,2BAA2B,GAAGC,mBAAmB,IAAI;IACzD;IACA,IAAIF,mBAAmB,KAAK,IAAI,EAAE;MAChC;IACF;IACA;IACA,IAAI1e,QAAQ,CAACC,IAAI,CAACiL,YAAY,GAAG3K,MAAM,CAACse,WAAW,IAAID,mBAAmB,KAAK,QAAQ,CAAC;IAAA,EACtF;MACA;MACAF,mBAAmB,GAAGhZ,QAAQ,CAACnF,MAAM,CAACkL,gBAAgB,CAACzL,QAAQ,CAACC,IAAI,CAAC,CAAC2L,gBAAgB,CAAC,eAAe,CAAC,CAAC;MACxG5L,QAAQ,CAACC,IAAI,CAAC4J,KAAK,CAACiV,YAAY,GAAG,GAAGJ,mBAAmB,GAAGN,gBAAgB,CAAC,CAAC,IAAI;IACpF;EACF,CAAC;EACD,MAAMW,+BAA+B,GAAGA,CAAA,KAAM;IAC5C,IAAIL,mBAAmB,KAAK,IAAI,EAAE;MAChC1e,QAAQ,CAACC,IAAI,CAAC4J,KAAK,CAACiV,YAAY,GAAG,GAAGJ,mBAAmB,IAAI;MAC7DA,mBAAmB,GAAG,IAAI;IAC5B;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;EACE,SAASM,wBAAwBA,CAAClQ,QAAQ,EAAE7L,SAAS,EAAE9C,WAAW,EAAE8e,QAAQ,EAAE;IAC5E,IAAI1Y,OAAO,CAAC,CAAC,EAAE;MACb2Y,yBAAyB,CAACpQ,QAAQ,EAAEmQ,QAAQ,CAAC;IAC/C,CAAC,MAAM;MACL/e,oBAAoB,CAACC,WAAW,CAAC,CAACgf,IAAI,CAAC,MAAMD,yBAAyB,CAACpQ,QAAQ,EAAEmQ,QAAQ,CAAC,CAAC;MAC3FtE,oBAAoB,CAAChb,WAAW,CAAC;IACnC;;IAEA;IACA;IACA,IAAIwd,aAAa,EAAE;MACjBla,SAAS,CAAC0K,YAAY,CAAC,OAAO,EAAE,yBAAyB,CAAC;MAC1D1K,SAAS,CAACwP,eAAe,CAAC,OAAO,CAAC;MAClCxP,SAAS,CAACqH,SAAS,GAAG,EAAE;IAC1B,CAAC,MAAM;MACLrH,SAAS,CAACmF,MAAM,CAAC,CAAC;IACpB;IACA,IAAIhC,OAAO,CAAC,CAAC,EAAE;MACb2Y,+BAA+B,CAAC,CAAC;MACjCZ,UAAU,CAAC,CAAC;MACZjB,eAAe,CAAC,CAAC;IACnB;IACAkC,iBAAiB,CAAC,CAAC;EACrB;;EAEA;AACF;AACA;EACE,SAASA,iBAAiBA,CAAA,EAAG;IAC3B5V,WAAW,CAAC,CAACxJ,QAAQ,CAACiN,eAAe,EAAEjN,QAAQ,CAACC,IAAI,CAAC,EAAE,CAACe,WAAW,CAACsF,KAAK,EAAEtF,WAAW,CAAC,aAAa,CAAC,EAAEA,WAAW,CAAC,aAAa,CAAC,EAAEA,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC;EACjK;;EAEA;AACF;AACA;AACA;AACA;EACE,SAAS+D,KAAKA,CAACsa,YAAY,EAAE;IAC3BA,YAAY,GAAGC,mBAAmB,CAACD,YAAY,CAAC;IAChD,MAAMvC,kBAAkB,GAAGD,cAAc,CAACC,kBAAkB,CAACzd,GAAG,CAAC,IAAI,CAAC;IACtE,MAAM4f,QAAQ,GAAGM,iBAAiB,CAAC,IAAI,CAAC;IACxC,IAAI,IAAI,CAACC,iBAAiB,EAAE;MAC1B;MACA,IAAI,CAACH,YAAY,CAACI,WAAW,EAAE;QAC7BC,qBAAqB,CAAC,IAAI,CAAC;QAC3B5C,kBAAkB,CAACuC,YAAY,CAAC;MAClC;IACF,CAAC,MAAM,IAAIJ,QAAQ,EAAE;MACnB;MACAnC,kBAAkB,CAACuC,YAAY,CAAC;IAClC;EACF;EACA,MAAME,iBAAiB,GAAGzQ,QAAQ,IAAI;IACpC,MAAMxL,KAAK,GAAGD,QAAQ,CAAC,CAAC;IACxB,IAAI,CAACC,KAAK,EAAE;MACV,OAAO,KAAK;IACd;IACA,MAAMgO,WAAW,GAAGD,YAAY,CAACC,WAAW,CAACjS,GAAG,CAACyP,QAAQ,CAAC;IAC1D,IAAI,CAACwC,WAAW,IAAIjL,QAAQ,CAAC/C,KAAK,EAAEgO,WAAW,CAACqO,SAAS,CAACrc,KAAK,CAAC,EAAE;MAChE,OAAO,KAAK;IACd;IACAkG,WAAW,CAAClG,KAAK,EAAEgO,WAAW,CAACnJ,SAAS,CAAC7E,KAAK,CAAC;IAC/CiF,QAAQ,CAACjF,KAAK,EAAEgO,WAAW,CAACqO,SAAS,CAACrc,KAAK,CAAC;IAC5C,MAAMwN,QAAQ,GAAG/N,YAAY,CAAC,CAAC;IAC/ByG,WAAW,CAACsH,QAAQ,EAAEQ,WAAW,CAACnJ,SAAS,CAAC2I,QAAQ,CAAC;IACrDvI,QAAQ,CAACuI,QAAQ,EAAEQ,WAAW,CAACqO,SAAS,CAAC7O,QAAQ,CAAC;IAClD8O,oBAAoB,CAAC9Q,QAAQ,EAAExL,KAAK,EAAEgO,WAAW,CAAC;IAClD,OAAO,IAAI;EACb,CAAC;;EAED;AACF;AACA;EACE,SAASuO,aAAaA,CAAC5d,KAAK,EAAE;IAC5B,MAAM4d,aAAa,GAAGhD,cAAc,CAACE,iBAAiB,CAAC1d,GAAG,CAAC,IAAI,CAAC;IAChEqgB,qBAAqB,CAAC,IAAI,CAAC;IAC3B,IAAIG,aAAa,EAAE;MACjB;MACAA,aAAa,CAAC5d,KAAK,CAAC;IACtB;EACF;;EAEA;AACF;AACA;EACE,MAAMyd,qBAAqB,GAAG5Q,QAAQ,IAAI;IACxC,IAAIA,QAAQ,CAAC0Q,iBAAiB,EAAE;MAC9B,OAAO1Q,QAAQ,CAAC0Q,iBAAiB;MACjC;MACA,IAAI,CAACnO,YAAY,CAACC,WAAW,CAACjS,GAAG,CAACyP,QAAQ,CAAC,EAAE;QAC3CA,QAAQ,CAACgR,QAAQ,CAAC,CAAC;MACrB;IACF;EACF,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMR,mBAAmB,GAAGD,YAAY,IAAI;IAC1C;IACA,IAAI,OAAOA,YAAY,KAAK,WAAW,EAAE;MACvC,OAAO;QACLU,WAAW,EAAE,KAAK;QAClBC,QAAQ,EAAE,KAAK;QACfP,WAAW,EAAE;MACf,CAAC;IACH;IACA,OAAOxX,MAAM,CAACgY,MAAM,CAAC;MACnBF,WAAW,EAAE,KAAK;MAClBC,QAAQ,EAAE,KAAK;MACfP,WAAW,EAAE;IACf,CAAC,EAAEJ,YAAY,CAAC;EAClB,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMO,oBAAoB,GAAGA,CAAC9Q,QAAQ,EAAExL,KAAK,EAAEgO,WAAW,KAAK;IAC7D,IAAI4O,qBAAqB;IACzB,MAAMjd,SAAS,GAAGF,YAAY,CAAC,CAAC;IAChC;IACA,MAAMod,oBAAoB,GAAG3U,eAAe,CAAClI,KAAK,CAAC;IACnD,IAAI,OAAOgO,WAAW,CAAC8O,SAAS,KAAK,UAAU,EAAE;MAC/C9O,WAAW,CAAC8O,SAAS,CAAC9c,KAAK,CAAC;IAC9B;IACA,CAAC4c,qBAAqB,GAAGvgB,WAAW,CAACma,YAAY,MAAM,IAAI,IAAIoG,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAACnG,IAAI,CAAC,WAAW,EAAEzW,KAAK,CAAC;IACjJ,IAAI6c,oBAAoB,EAAE;MACxBE,YAAY,CAACvR,QAAQ,EAAExL,KAAK,EAAEL,SAAS,EAAEqO,WAAW,CAACnR,WAAW,EAAEmR,WAAW,CAAC2N,QAAQ,CAAC;IACzF,CAAC,MAAM;MACL;MACAD,wBAAwB,CAAClQ,QAAQ,EAAE7L,SAAS,EAAEqO,WAAW,CAACnR,WAAW,EAAEmR,WAAW,CAAC2N,QAAQ,CAAC;IAC9F;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAMoB,YAAY,GAAGA,CAACvR,QAAQ,EAAExL,KAAK,EAAEL,SAAS,EAAE9C,WAAW,EAAE8e,QAAQ,KAAK;IAC1Etf,WAAW,CAAC2gB,8BAA8B,GAAGtB,wBAAwB,CAACuB,IAAI,CAAC,IAAI,EAAEzR,QAAQ,EAAE7L,SAAS,EAAE9C,WAAW,EAAE8e,QAAQ,CAAC;IAC5H;AACJ;AACA;IACI,MAAMuB,0BAA0B,GAAG,SAAAA,CAAU9hB,CAAC,EAAE;MAC9C,IAAIA,CAAC,CAACyK,MAAM,KAAK7F,KAAK,EAAE;QACtB,IAAImd,qBAAqB;QACzB,CAACA,qBAAqB,GAAG9gB,WAAW,CAAC2gB,8BAA8B,MAAM,IAAI,IAAIG,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAACC,IAAI,CAAC/gB,WAAW,CAAC;QAC5J,OAAOA,WAAW,CAAC2gB,8BAA8B;QACjDhd,KAAK,CAACyU,mBAAmB,CAAC,cAAc,EAAEyI,0BAA0B,CAAC;QACrEld,KAAK,CAACyU,mBAAmB,CAAC,eAAe,EAAEyI,0BAA0B,CAAC;MACxE;IACF,CAAC;IACDld,KAAK,CAACmS,gBAAgB,CAAC,cAAc,EAAE+K,0BAA0B,CAAC;IAClEld,KAAK,CAACmS,gBAAgB,CAAC,eAAe,EAAE+K,0BAA0B,CAAC;EACrE,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMtB,yBAAyB,GAAGA,CAACpQ,QAAQ,EAAEmQ,QAAQ,KAAK;IACxDre,UAAU,CAAC,MAAM;MACf,IAAI+f,sBAAsB;MAC1B,IAAI,OAAO1B,QAAQ,KAAK,UAAU,EAAE;QAClCA,QAAQ,CAACsB,IAAI,CAACzR,QAAQ,CAAC9G,MAAM,CAAC,CAAC,CAAC;MAClC;MACA,CAAC2Y,sBAAsB,GAAGhhB,WAAW,CAACma,YAAY,MAAM,IAAI,IAAI6G,sBAAsB,KAAK,KAAK,CAAC,IAAIA,sBAAsB,CAAC5G,IAAI,CAAC,UAAU,CAAC;MAC5I;MACA,IAAIjL,QAAQ,CAACgR,QAAQ,EAAE;QACrBhR,QAAQ,CAACgR,QAAQ,CAAC,CAAC;MACrB;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;EACE,MAAMc,WAAW,GAAGC,eAAe,IAAI;IACrC,IAAIvd,KAAK,GAAGD,QAAQ,CAAC,CAAC;IACtB,IAAI,CAACC,KAAK,EAAE;MACV,IAAIwd,IAAI,CAAC,CAAC;IACZ;IACAxd,KAAK,GAAGD,QAAQ,CAAC,CAAC;IAClB,IAAI,CAACC,KAAK,EAAE;MACV;IACF;IACA,MAAMmB,MAAM,GAAGD,SAAS,CAAC,CAAC;IAC1B,IAAI+B,OAAO,CAAC,CAAC,EAAE;MACb2D,IAAI,CAAC3G,OAAO,CAAC,CAAC,CAAC;IACjB,CAAC,MAAM;MACLwd,aAAa,CAACzd,KAAK,EAAEud,eAAe,CAAC;IACvC;IACA7W,IAAI,CAACvF,MAAM,CAAC;IACZnB,KAAK,CAACqK,YAAY,CAAC,cAAc,EAAE,MAAM,CAAC;IAC1CrK,KAAK,CAACqK,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC;IACvCrK,KAAK,CAACvD,KAAK,CAAC,CAAC;EACf,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMghB,aAAa,GAAGA,CAACzd,KAAK,EAAEud,eAAe,KAAK;IAChD,MAAM5c,OAAO,GAAGS,UAAU,CAAC,CAAC;IAC5B,MAAMD,MAAM,GAAGD,SAAS,CAAC,CAAC;IAC1B,IAAI,CAACP,OAAO,IAAI,CAACQ,MAAM,EAAE;MACvB;IACF;IACA,IAAI,CAACoc,eAAe,IAAI1a,WAAW,CAACnC,gBAAgB,CAAC,CAAC,CAAC,EAAE;MACvD6c,eAAe,GAAG7c,gBAAgB,CAAC,CAAC;IACtC;IACAgG,IAAI,CAAC/F,OAAO,CAAC;IACb,IAAI4c,eAAe,EAAE;MACnB3W,IAAI,CAAC2W,eAAe,CAAC;MACrBpc,MAAM,CAACkJ,YAAY,CAAC,wBAAwB,EAAEkT,eAAe,CAAC1f,SAAS,CAAC;MACxE8C,OAAO,CAACyL,YAAY,CAACjL,MAAM,EAAEoc,eAAe,CAAC;IAC/C;IACAtY,QAAQ,CAAC,CAACjF,KAAK,EAAEW,OAAO,CAAC,EAAEjD,WAAW,CAACggB,OAAO,CAAC;EACjD,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMC,0BAA0B,GAAGA,CAACnS,QAAQ,EAAE9G,MAAM,KAAK;IACvD,IAAIA,MAAM,CAACa,KAAK,KAAK,QAAQ,IAAIb,MAAM,CAACa,KAAK,KAAK,OAAO,EAAE;MACzDqY,kBAAkB,CAACpS,QAAQ,EAAE9G,MAAM,CAAC;IACtC,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,CAAC,CAACmZ,IAAI,CAACtZ,CAAC,IAAIA,CAAC,KAAKG,MAAM,CAACa,KAAK,CAAC,KAAKlG,cAAc,CAACqF,MAAM,CAACuL,UAAU,CAAC,IAAIzQ,SAAS,CAACkF,MAAM,CAACuL,UAAU,CAAC,CAAC,EAAE;MAC9JqN,WAAW,CAAC5c,gBAAgB,CAAC,CAAC,CAAC;MAC/Bod,gBAAgB,CAACtS,QAAQ,EAAE9G,MAAM,CAAC;IACpC;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMqZ,aAAa,GAAGA,CAACvS,QAAQ,EAAEwC,WAAW,KAAK;IAC/C,MAAMzI,KAAK,GAAGiG,QAAQ,CAACwS,QAAQ,CAAC,CAAC;IACjC,IAAI,CAACzY,KAAK,EAAE;MACV,OAAO,IAAI;IACb;IACA,QAAQyI,WAAW,CAACzI,KAAK;MACvB,KAAK,UAAU;QACb,OAAO0Y,gBAAgB,CAAC1Y,KAAK,CAAC;MAChC,KAAK,OAAO;QACV,OAAO2Y,aAAa,CAAC3Y,KAAK,CAAC;MAC7B,KAAK,MAAM;QACT,OAAO4Y,YAAY,CAAC5Y,KAAK,CAAC;MAC5B;QACE,OAAOyI,WAAW,CAACoQ,aAAa,GAAG7Y,KAAK,CAACI,KAAK,CAACyN,IAAI,CAAC,CAAC,GAAG7N,KAAK,CAACI,KAAK;IACvE;EACF,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMsY,gBAAgB,GAAG1Y,KAAK,IAAIA,KAAK,CAAC0L,OAAO,GAAG,CAAC,GAAG,CAAC;;EAEvD;AACF;AACA;AACA;EACE,MAAMiN,aAAa,GAAG3Y,KAAK,IAAIA,KAAK,CAAC0L,OAAO,GAAG1L,KAAK,CAACI,KAAK,GAAG,IAAI;;EAEjE;AACF;AACA;AACA;EACE,MAAMwY,YAAY,GAAG5Y,KAAK,IAAIA,KAAK,CAAC8Y,KAAK,IAAI9Y,KAAK,CAAC8Y,KAAK,CAAC5iB,MAAM,GAAG8J,KAAK,CAAClD,YAAY,CAAC,UAAU,CAAC,KAAK,IAAI,GAAGkD,KAAK,CAAC8Y,KAAK,GAAG9Y,KAAK,CAAC8Y,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI;;EAE/I;AACF;AACA;AACA;EACE,MAAMT,kBAAkB,GAAGA,CAACpS,QAAQ,EAAE9G,MAAM,KAAK;IAC/C,MAAM1E,KAAK,GAAGD,QAAQ,CAAC,CAAC;IACxB,IAAI,CAACC,KAAK,EAAE;MACV;IACF;IACA;AACJ;AACA;IACI,MAAMse,mBAAmB,GAAGC,YAAY,IAAI;MAC1C,IAAI7Z,MAAM,CAACa,KAAK,KAAK,QAAQ,EAAE;QAC7BiZ,qBAAqB,CAACxe,KAAK,EAAEye,kBAAkB,CAACF,YAAY,CAAC,EAAE7Z,MAAM,CAAC;MACxE,CAAC,MAAM,IAAIA,MAAM,CAACa,KAAK,KAAK,OAAO,EAAE;QACnCmZ,oBAAoB,CAAC1e,KAAK,EAAEye,kBAAkB,CAACF,YAAY,CAAC,EAAE7Z,MAAM,CAAC;MACvE;IACF,CAAC;IACD,IAAIrF,cAAc,CAACqF,MAAM,CAAC6Z,YAAY,CAAC,IAAI/e,SAAS,CAACkF,MAAM,CAAC6Z,YAAY,CAAC,EAAE;MACzEjB,WAAW,CAAC5c,gBAAgB,CAAC,CAAC,CAAC;MAC/BnB,SAAS,CAACmF,MAAM,CAAC6Z,YAAY,CAAC,CAAC1C,IAAI,CAAC0C,YAAY,IAAI;QAClD/S,QAAQ,CAACmT,WAAW,CAAC,CAAC;QACtBL,mBAAmB,CAACC,YAAY,CAAC;MACnC,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,OAAO7Z,MAAM,CAAC6Z,YAAY,KAAK,QAAQ,EAAE;MAClDD,mBAAmB,CAAC5Z,MAAM,CAAC6Z,YAAY,CAAC;IAC1C,CAAC,MAAM;MACL5f,KAAK,CAAC,yEAAyE,OAAO+F,MAAM,CAAC6Z,YAAY,EAAE,CAAC;IAC9G;EACF,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMT,gBAAgB,GAAGA,CAACtS,QAAQ,EAAE9G,MAAM,KAAK;IAC7C,MAAMa,KAAK,GAAGiG,QAAQ,CAACwS,QAAQ,CAAC,CAAC;IACjC,IAAI,CAACzY,KAAK,EAAE;MACV;IACF;IACAqB,IAAI,CAACrB,KAAK,CAAC;IACXhG,SAAS,CAACmF,MAAM,CAACuL,UAAU,CAAC,CAAC4L,IAAI,CAAC5L,UAAU,IAAI;MAC9C1K,KAAK,CAACI,KAAK,GAAGjB,MAAM,CAACa,KAAK,KAAK,QAAQ,GAAG,GAAG8C,UAAU,CAAC4H,UAAU,CAAC,IAAI,CAAC,EAAE,GAAG,GAAGA,UAAU,EAAE;MAC5FvJ,IAAI,CAACnB,KAAK,CAAC;MACXA,KAAK,CAAC9I,KAAK,CAAC,CAAC;MACb+O,QAAQ,CAACmT,WAAW,CAAC,CAAC;IACxB,CAAC,CAAC,CAACC,KAAK,CAACC,GAAG,IAAI;MACdlgB,KAAK,CAAC,gCAAgCkgB,GAAG,EAAE,CAAC;MAC5CtZ,KAAK,CAACI,KAAK,GAAG,EAAE;MAChBe,IAAI,CAACnB,KAAK,CAAC;MACXA,KAAK,CAAC9I,KAAK,CAAC,CAAC;MACb+O,QAAQ,CAACmT,WAAW,CAAC,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,SAASH,qBAAqBA,CAACxe,KAAK,EAAEue,YAAY,EAAE7Z,MAAM,EAAE;IAC1D,MAAM2E,MAAM,GAAGlD,qBAAqB,CAACnG,KAAK,EAAEtC,WAAW,CAAC2L,MAAM,CAAC;IAC/D,IAAI,CAACA,MAAM,EAAE;MACX;IACF;IACA;AACJ;AACA;AACA;AACA;IACI,MAAMyV,YAAY,GAAGA,CAACzX,MAAM,EAAE0X,WAAW,EAAEC,WAAW,KAAK;MACzD,MAAMC,MAAM,GAAGviB,QAAQ,CAACkO,aAAa,CAAC,QAAQ,CAAC;MAC/CqU,MAAM,CAACtZ,KAAK,GAAGqZ,WAAW;MAC1B3b,YAAY,CAAC4b,MAAM,EAAEF,WAAW,CAAC;MACjCE,MAAM,CAAClO,QAAQ,GAAGmO,UAAU,CAACF,WAAW,EAAEta,MAAM,CAACuL,UAAU,CAAC;MAC5D5I,MAAM,CAACpD,WAAW,CAACgb,MAAM,CAAC;IAC5B,CAAC;IACDV,YAAY,CAACxa,OAAO,CAACob,WAAW,IAAI;MAClC,MAAMH,WAAW,GAAGG,WAAW,CAAC,CAAC,CAAC;MAClC,MAAMJ,WAAW,GAAGI,WAAW,CAAC,CAAC,CAAC;MAClC;MACA;MACA;MACA;MACA,IAAIpd,KAAK,CAACiE,OAAO,CAAC+Y,WAAW,CAAC,EAAE;QAC9B;QACA,MAAMK,QAAQ,GAAG1iB,QAAQ,CAACkO,aAAa,CAAC,UAAU,CAAC;QACnDwU,QAAQ,CAAC9V,KAAK,GAAG0V,WAAW;QAC5BI,QAAQ,CAACtO,QAAQ,GAAG,KAAK,CAAC,CAAC;QAC3BzH,MAAM,CAACpF,WAAW,CAACmb,QAAQ,CAAC;QAC5BL,WAAW,CAAChb,OAAO,CAACsb,CAAC,IAAIP,YAAY,CAACM,QAAQ,EAAEC,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9D,CAAC,MAAM;QACL;QACAP,YAAY,CAACzV,MAAM,EAAE0V,WAAW,EAAEC,WAAW,CAAC;MAChD;IACF,CAAC,CAAC;IACF3V,MAAM,CAAC5M,KAAK,CAAC,CAAC;EAChB;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASiiB,oBAAoBA,CAAC1e,KAAK,EAAEue,YAAY,EAAE7Z,MAAM,EAAE;IACzD,MAAMW,KAAK,GAAGc,qBAAqB,CAACnG,KAAK,EAAEtC,WAAW,CAAC2H,KAAK,CAAC;IAC7D,IAAI,CAACA,KAAK,EAAE;MACV;IACF;IACAkZ,YAAY,CAACxa,OAAO,CAACob,WAAW,IAAI;MAClC,MAAMG,UAAU,GAAGH,WAAW,CAAC,CAAC,CAAC;MACjC,MAAMI,UAAU,GAAGJ,WAAW,CAAC,CAAC,CAAC;MACjC,MAAMK,UAAU,GAAG9iB,QAAQ,CAACkO,aAAa,CAAC,OAAO,CAAC;MAClD,MAAM6U,iBAAiB,GAAG/iB,QAAQ,CAACkO,aAAa,CAAC,OAAO,CAAC;MACzD4U,UAAU,CAAC/Z,IAAI,GAAG,OAAO;MACzB+Z,UAAU,CAACtQ,IAAI,GAAGxR,WAAW,CAAC2H,KAAK;MACnCma,UAAU,CAAC7Z,KAAK,GAAG2Z,UAAU;MAC7B,IAAIJ,UAAU,CAACI,UAAU,EAAE5a,MAAM,CAACuL,UAAU,CAAC,EAAE;QAC7CuP,UAAU,CAACvO,OAAO,GAAG,IAAI;MAC3B;MACA,MAAM3H,KAAK,GAAG5M,QAAQ,CAACkO,aAAa,CAAC,MAAM,CAAC;MAC5CvH,YAAY,CAACiG,KAAK,EAAEiW,UAAU,CAAC;MAC/BjW,KAAK,CAACzL,SAAS,GAAGH,WAAW,CAAC4L,KAAK;MACnCmW,iBAAiB,CAACxb,WAAW,CAACub,UAAU,CAAC;MACzCC,iBAAiB,CAACxb,WAAW,CAACqF,KAAK,CAAC;MACpCjE,KAAK,CAACpB,WAAW,CAACwb,iBAAiB,CAAC;IACtC,CAAC,CAAC;IACF,MAAMC,MAAM,GAAGra,KAAK,CAACxD,gBAAgB,CAAC,OAAO,CAAC;IAC9C,IAAI6d,MAAM,CAACjkB,MAAM,EAAE;MACjBikB,MAAM,CAAC,CAAC,CAAC,CAACjjB,KAAK,CAAC,CAAC;IACnB;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAMgiB,kBAAkB,GAAGF,YAAY,IAAI;IACzC;IACA,MAAMoB,MAAM,GAAG,EAAE;IACjB,IAAIpB,YAAY,YAAYqB,GAAG,EAAE;MAC/BrB,YAAY,CAACxa,OAAO,CAAC,CAAC4B,KAAK,EAAE4S,GAAG,KAAK;QACnC,IAAIsH,cAAc,GAAGla,KAAK;QAC1B,IAAI,OAAOka,cAAc,KAAK,QAAQ,EAAE;UACtC;UACAA,cAAc,GAAGpB,kBAAkB,CAACoB,cAAc,CAAC;QACrD;QACAF,MAAM,CAAC5gB,IAAI,CAAC,CAACwZ,GAAG,EAAEsH,cAAc,CAAC,CAAC;MACpC,CAAC,CAAC;IACJ,CAAC,MAAM;MACLlb,MAAM,CAACiK,IAAI,CAAC2P,YAAY,CAAC,CAACxa,OAAO,CAACwU,GAAG,IAAI;QACvC,IAAIsH,cAAc,GAAGtB,YAAY,CAAChG,GAAG,CAAC;QACtC,IAAI,OAAOsH,cAAc,KAAK,QAAQ,EAAE;UACtC;UACAA,cAAc,GAAGpB,kBAAkB,CAACoB,cAAc,CAAC;QACrD;QACAF,MAAM,CAAC5gB,IAAI,CAAC,CAACwZ,GAAG,EAAEsH,cAAc,CAAC,CAAC;MACpC,CAAC,CAAC;IACJ;IACA,OAAOF,MAAM;EACf,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMT,UAAU,GAAGA,CAACF,WAAW,EAAE/O,UAAU,KAAK;IAC9C,OAAO,CAAC,CAACA,UAAU,IAAIA,UAAU,CAAC3E,QAAQ,CAAC,CAAC,KAAK0T,WAAW,CAAC1T,QAAQ,CAAC,CAAC;EACzE,CAAC;;EAED;AACF;AACA;EACE,MAAMwU,wBAAwB,GAAGtU,QAAQ,IAAI;IAC3C,MAAMwC,WAAW,GAAGD,YAAY,CAACC,WAAW,CAACjS,GAAG,CAACyP,QAAQ,CAAC;IAC1DA,QAAQ,CAACuU,cAAc,CAAC,CAAC;IACzB,IAAI/R,WAAW,CAACzI,KAAK,EAAE;MACrBya,4BAA4B,CAACxU,QAAQ,EAAE,SAAS,CAAC;IACnD,CAAC,MAAM;MACL5K,OAAO,CAAC4K,QAAQ,EAAE,IAAI,CAAC;IACzB;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAMyU,qBAAqB,GAAGzU,QAAQ,IAAI;IACxC,MAAMwC,WAAW,GAAGD,YAAY,CAACC,WAAW,CAACjS,GAAG,CAACyP,QAAQ,CAAC;IAC1DA,QAAQ,CAACuU,cAAc,CAAC,CAAC;IACzB,IAAI/R,WAAW,CAACkS,sBAAsB,EAAE;MACtCF,4BAA4B,CAACxU,QAAQ,EAAE,MAAM,CAAC;IAChD,CAAC,MAAM;MACLxK,IAAI,CAACwK,QAAQ,EAAE,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAM2U,uBAAuB,GAAGA,CAAC3U,QAAQ,EAAEoM,WAAW,KAAK;IACzDpM,QAAQ,CAACuU,cAAc,CAAC,CAAC;IACzBnI,WAAW,CAACV,aAAa,CAACpW,MAAM,CAAC;EACnC,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMkf,4BAA4B,GAAGA,CAACxU,QAAQ,EAAE/F,IAAI,KAAK;IACvD,MAAMuI,WAAW,GAAGD,YAAY,CAACC,WAAW,CAACjS,GAAG,CAACyP,QAAQ,CAAC;IAC1D,IAAI,CAACwC,WAAW,CAACzI,KAAK,EAAE;MACtB5G,KAAK,CAAC,0EAA0ET,qBAAqB,CAACuH,IAAI,CAAC,EAAE,CAAC;MAC9G;IACF;IACA,MAAMF,KAAK,GAAGiG,QAAQ,CAACwS,QAAQ,CAAC,CAAC;IACjC,MAAM/N,UAAU,GAAG8N,aAAa,CAACvS,QAAQ,EAAEwC,WAAW,CAAC;IACvD,IAAIA,WAAW,CAACoS,cAAc,EAAE;MAC9BC,oBAAoB,CAAC7U,QAAQ,EAAEyE,UAAU,EAAExK,IAAI,CAAC;IAClD,CAAC,MAAM,IAAIF,KAAK,IAAI,CAACA,KAAK,CAAC+a,aAAa,CAAC,CAAC,EAAE;MAC1C9U,QAAQ,CAAC+U,aAAa,CAAC,CAAC;MACxB/U,QAAQ,CAACgV,qBAAqB,CAACxS,WAAW,CAACyS,iBAAiB,IAAIlb,KAAK,CAACkb,iBAAiB,CAAC;IAC1F,CAAC,MAAM,IAAIhb,IAAI,KAAK,MAAM,EAAE;MAC1BzE,IAAI,CAACwK,QAAQ,EAAEyE,UAAU,CAAC;IAC5B,CAAC,MAAM;MACLrP,OAAO,CAAC4K,QAAQ,EAAEyE,UAAU,CAAC;IAC/B;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMoQ,oBAAoB,GAAGA,CAAC7U,QAAQ,EAAEyE,UAAU,EAAExK,IAAI,KAAK;IAC3D,MAAMuI,WAAW,GAAGD,YAAY,CAACC,WAAW,CAACjS,GAAG,CAACyP,QAAQ,CAAC;IAC1DA,QAAQ,CAACkV,YAAY,CAAC,CAAC;IACvB,MAAMC,iBAAiB,GAAG7jB,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC8e,IAAI,CAAC,MAAMtc,SAAS,CAACyO,WAAW,CAACoS,cAAc,CAACnQ,UAAU,EAAEjC,WAAW,CAACyS,iBAAiB,CAAC,CAAC,CAAC;IACxIE,iBAAiB,CAAC9E,IAAI,CAAC4E,iBAAiB,IAAI;MAC1CjV,QAAQ,CAAC+U,aAAa,CAAC,CAAC;MACxB/U,QAAQ,CAACoV,WAAW,CAAC,CAAC;MACtB,IAAIH,iBAAiB,EAAE;QACrBjV,QAAQ,CAACgV,qBAAqB,CAACC,iBAAiB,CAAC;MACnD,CAAC,MAAM,IAAIhb,IAAI,KAAK,MAAM,EAAE;QAC1BzE,IAAI,CAACwK,QAAQ,EAAEyE,UAAU,CAAC;MAC5B,CAAC,MAAM;QACLrP,OAAO,CAAC4K,QAAQ,EAAEyE,UAAU,CAAC;MAC/B;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMjP,IAAI,GAAGA,CAACwK,QAAQ,EAAE7F,KAAK,KAAK;IAChC,MAAMqI,WAAW,GAAGD,YAAY,CAACC,WAAW,CAACjS,GAAG,CAACyP,QAAQ,IAAIoK,SAAS,CAAC;IACvE,IAAI5H,WAAW,CAAC6S,gBAAgB,EAAE;MAChCvD,WAAW,CAACvc,aAAa,CAAC,CAAC,CAAC;IAC9B;IACA,IAAIiN,WAAW,CAAC8S,OAAO,EAAE;MACvBtV,QAAQ,CAAC0Q,iBAAiB,GAAG,IAAI,CAAC,CAAC;MACnC,MAAM6E,cAAc,GAAGjkB,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC8e,IAAI,CAAC,MAAMtc,SAAS,CAACyO,WAAW,CAAC8S,OAAO,CAACnb,KAAK,EAAEqI,WAAW,CAACyS,iBAAiB,CAAC,CAAC,CAAC;MACzHM,cAAc,CAAClF,IAAI,CAACmF,YAAY,IAAI;QAClC,IAAIA,YAAY,KAAK,KAAK,EAAE;UAC1BxV,QAAQ,CAACmT,WAAW,CAAC,CAAC;UACtBvC,qBAAqB,CAAC5Q,QAAQ,CAAC;QACjC,CAAC,MAAM;UACLA,QAAQ,CAAC/J,KAAK,CAAC;YACbib,QAAQ,EAAE,IAAI;YACd/W,KAAK,EAAE,OAAOqb,YAAY,KAAK,WAAW,GAAGrb,KAAK,GAAGqb;UACvD,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,CAACpC,KAAK,CAACjgB,KAAK,IAAIsiB,UAAU,CAACzV,QAAQ,IAAIoK,SAAS,EAAEjX,KAAK,CAAC,CAAC;IAC7D,CAAC,MAAM;MACL6M,QAAQ,CAAC/J,KAAK,CAAC;QACbib,QAAQ,EAAE,IAAI;QACd/W;MACF,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMub,WAAW,GAAGA,CAAC1V,QAAQ,EAAE7F,KAAK,KAAK;IACvC6F,QAAQ,CAAC/J,KAAK,CAAC;MACbgb,WAAW,EAAE,IAAI;MACjB9W;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMsb,UAAU,GAAGA,CAACzV,QAAQ,EAAE7M,KAAK,KAAK;IACtC6M,QAAQ,CAAC+Q,aAAa,CAAC5d,KAAK,CAAC;EAC/B,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMiC,OAAO,GAAGA,CAAC4K,QAAQ,EAAE7F,KAAK,KAAK;IACnC,MAAMqI,WAAW,GAAGD,YAAY,CAACC,WAAW,CAACjS,GAAG,CAACyP,QAAQ,IAAIoK,SAAS,CAAC;IACvE,IAAI5H,WAAW,CAACmT,mBAAmB,EAAE;MACnC7D,WAAW,CAAC,CAAC;IACf;IACA,IAAItP,WAAW,CAACoT,UAAU,EAAE;MAC1B5V,QAAQ,CAAC1B,sBAAsB,CAAC,CAAC;MACjC0B,QAAQ,CAAC0Q,iBAAiB,GAAG,IAAI,CAAC,CAAC;MACnC,MAAMmF,iBAAiB,GAAGvkB,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC8e,IAAI,CAAC,MAAMtc,SAAS,CAACyO,WAAW,CAACoT,UAAU,CAACzb,KAAK,EAAEqI,WAAW,CAACyS,iBAAiB,CAAC,CAAC,CAAC;MAC/HY,iBAAiB,CAACxF,IAAI,CAACyF,eAAe,IAAI;QACxC,IAAIze,WAAW,CAACpC,oBAAoB,CAAC,CAAC,CAAC,IAAI6gB,eAAe,KAAK,KAAK,EAAE;UACpE9V,QAAQ,CAACmT,WAAW,CAAC,CAAC;UACtBvC,qBAAqB,CAAC5Q,QAAQ,CAAC;QACjC,CAAC,MAAM;UACL0V,WAAW,CAAC1V,QAAQ,EAAE,OAAO8V,eAAe,KAAK,WAAW,GAAG3b,KAAK,GAAG2b,eAAe,CAAC;QACzF;MACF,CAAC,CAAC,CAAC1C,KAAK,CAACjgB,KAAK,IAAIsiB,UAAU,CAACzV,QAAQ,IAAIoK,SAAS,EAAEjX,KAAK,CAAC,CAAC;IAC7D,CAAC,MAAM;MACLuiB,WAAW,CAAC1V,QAAQ,EAAE7F,KAAK,CAAC;IAC9B;EACF,CAAC;;EAED;AACF;AACA;EACE,SAASgZ,WAAWA,CAAA,EAAG;IACrB;IACA,MAAM3Q,WAAW,GAAGD,YAAY,CAACC,WAAW,CAACjS,GAAG,CAAC,IAAI,CAAC;IACtD,IAAI,CAACiS,WAAW,EAAE;MAChB;IACF;IACA,MAAME,QAAQ,GAAGH,YAAY,CAACG,QAAQ,CAACnS,GAAG,CAAC,IAAI,CAAC;IAChD6K,IAAI,CAACsH,QAAQ,CAAC/M,MAAM,CAAC;IACrB,IAAI8B,OAAO,CAAC,CAAC,EAAE;MACb,IAAI+K,WAAW,CAAChQ,IAAI,EAAE;QACpB0I,IAAI,CAACzG,OAAO,CAAC,CAAC,CAAC;MACjB;IACF,CAAC,MAAM;MACLshB,iBAAiB,CAACrT,QAAQ,CAAC;IAC7B;IACAhI,WAAW,CAAC,CAACgI,QAAQ,CAAClO,KAAK,EAAEkO,QAAQ,CAACvN,OAAO,CAAC,EAAEjD,WAAW,CAACggB,OAAO,CAAC;IACpExP,QAAQ,CAAClO,KAAK,CAACmP,eAAe,CAAC,WAAW,CAAC;IAC3CjB,QAAQ,CAAClO,KAAK,CAACmP,eAAe,CAAC,cAAc,CAAC;IAC9CjB,QAAQ,CAACpC,aAAa,CAACgF,QAAQ,GAAG,KAAK;IACvC5C,QAAQ,CAACnC,UAAU,CAAC+E,QAAQ,GAAG,KAAK;IACpC5C,QAAQ,CAAClC,YAAY,CAAC8E,QAAQ,GAAG,KAAK;EACxC;EACA,MAAMyQ,iBAAiB,GAAGrT,QAAQ,IAAI;IACpC,MAAMqP,eAAe,GAAGrP,QAAQ,CAAClO,KAAK,CAACwhB,sBAAsB,CAACtT,QAAQ,CAAC/M,MAAM,CAACkB,YAAY,CAAC,wBAAwB,CAAC,CAAC;IACrH,IAAIkb,eAAe,CAAC9hB,MAAM,EAAE;MAC1BiL,IAAI,CAAC6W,eAAe,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC;IAC1C,CAAC,MAAM,IAAI7V,mBAAmB,CAAC,CAAC,EAAE;MAChCd,IAAI,CAACsH,QAAQ,CAACvN,OAAO,CAAC;IACxB;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,SAASqd,QAAQA,CAAA,EAAG;IAClB,MAAMhQ,WAAW,GAAGD,YAAY,CAACC,WAAW,CAACjS,GAAG,CAAC,IAAI,CAAC;IACtD,MAAMmS,QAAQ,GAAGH,YAAY,CAACG,QAAQ,CAACnS,GAAG,CAAC,IAAI,CAAC;IAChD,IAAI,CAACmS,QAAQ,EAAE;MACb,OAAO,IAAI;IACb;IACA,OAAOhJ,UAAU,CAACgJ,QAAQ,CAAClO,KAAK,EAAEgO,WAAW,CAACzI,KAAK,CAAC;EACtD;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASkc,kBAAkBA,CAACjW,QAAQ,EAAEyN,OAAO,EAAEnI,QAAQ,EAAE;IACvD,MAAM5C,QAAQ,GAAGH,YAAY,CAACG,QAAQ,CAACnS,GAAG,CAACyP,QAAQ,CAAC;IACpDyN,OAAO,CAAClV,OAAO,CAAC4I,MAAM,IAAI;MACxBuB,QAAQ,CAACvB,MAAM,CAAC,CAACmE,QAAQ,GAAGA,QAAQ;IACtC,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;EACE,SAAS4Q,gBAAgBA,CAACnc,KAAK,EAAEuL,QAAQ,EAAE;IACzC,MAAM9Q,KAAK,GAAGD,QAAQ,CAAC,CAAC;IACxB,IAAI,CAACC,KAAK,IAAI,CAACuF,KAAK,EAAE;MACpB;IACF;IACA,IAAIA,KAAK,CAACE,IAAI,KAAK,OAAO,EAAE;MAC1B;MACA,MAAMia,MAAM,GAAG1f,KAAK,CAAC6B,gBAAgB,CAAC,UAAUnE,WAAW,CAAC2H,KAAK,IAAI,CAAC;MACtE,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmb,MAAM,CAACjkB,MAAM,EAAE8I,CAAC,EAAE,EAAE;QACtCmb,MAAM,CAACnb,CAAC,CAAC,CAACuM,QAAQ,GAAGA,QAAQ;MAC/B;IACF,CAAC,MAAM;MACLvL,KAAK,CAACuL,QAAQ,GAAGA,QAAQ;IAC3B;EACF;;EAEA;AACF;AACA;AACA;EACE,SAASyP,aAAaA,CAAA,EAAG;IACvBkB,kBAAkB,CAAC,IAAI,EAAE,CAAC,eAAe,EAAE,YAAY,EAAE,cAAc,CAAC,EAAE,KAAK,CAAC;EAClF;;EAEA;AACF;AACA;AACA;EACE,SAAS1B,cAAcA,CAAA,EAAG;IACxB0B,kBAAkB,CAAC,IAAI,EAAE,CAAC,eAAe,EAAE,YAAY,EAAE,cAAc,CAAC,EAAE,IAAI,CAAC;EACjF;;EAEA;AACF;AACA;AACA;EACE,SAASb,WAAWA,CAAA,EAAG;IACrBc,gBAAgB,CAAC,IAAI,CAAC1D,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC;EAC1C;;EAEA;AACF;AACA;AACA;EACE,SAAS0C,YAAYA,CAAA,EAAG;IACtBgB,gBAAgB,CAAC,IAAI,CAAC1D,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC;EACzC;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,SAASwC,qBAAqBA,CAAC7hB,KAAK,EAAE;IACpC,MAAMuP,QAAQ,GAAGH,YAAY,CAACG,QAAQ,CAACnS,GAAG,CAAC,IAAI,CAAC;IAChD,MAAM2I,MAAM,GAAGqJ,YAAY,CAACC,WAAW,CAACjS,GAAG,CAAC,IAAI,CAAC;IACjDsH,YAAY,CAAC6K,QAAQ,CAACuS,iBAAiB,EAAE9hB,KAAK,CAAC;IAC/CuP,QAAQ,CAACuS,iBAAiB,CAAC5iB,SAAS,GAAGH,WAAW,CAAC,oBAAoB,CAAC;IACxE,IAAIgH,MAAM,CAACM,WAAW,IAAIN,MAAM,CAACM,WAAW,CAACyb,iBAAiB,EAAE;MAC9Dxb,QAAQ,CAACiJ,QAAQ,CAACuS,iBAAiB,EAAE/b,MAAM,CAACM,WAAW,CAACyb,iBAAiB,CAAC;IAC5E;IACA/Z,IAAI,CAACwH,QAAQ,CAACuS,iBAAiB,CAAC;IAChC,MAAMlb,KAAK,GAAG,IAAI,CAACyY,QAAQ,CAAC,CAAC;IAC7B,IAAIzY,KAAK,EAAE;MACTA,KAAK,CAAC8E,YAAY,CAAC,cAAc,EAAE,MAAM,CAAC;MAC1C9E,KAAK,CAAC8E,YAAY,CAAC,kBAAkB,EAAE3M,WAAW,CAAC,oBAAoB,CAAC,CAAC;MACzE8H,UAAU,CAACD,KAAK,CAAC;MACjBN,QAAQ,CAACM,KAAK,EAAE7H,WAAW,CAACikB,UAAU,CAAC;IACzC;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,SAAS7X,sBAAsBA,CAAA,EAAG;IAChC,MAAMoE,QAAQ,GAAGH,YAAY,CAACG,QAAQ,CAACnS,GAAG,CAAC,IAAI,CAAC;IAChD,IAAImS,QAAQ,CAACuS,iBAAiB,EAAE;MAC9B7Z,IAAI,CAACsH,QAAQ,CAACuS,iBAAiB,CAAC;IAClC;IACA,MAAMlb,KAAK,GAAG,IAAI,CAACyY,QAAQ,CAAC,CAAC;IAC7B,IAAIzY,KAAK,EAAE;MACTA,KAAK,CAAC4J,eAAe,CAAC,cAAc,CAAC;MACrC5J,KAAK,CAAC4J,eAAe,CAAC,kBAAkB,CAAC;MACzCjJ,WAAW,CAACX,KAAK,EAAE7H,WAAW,CAACikB,UAAU,CAAC;IAC5C;EACF;EAEA,MAAMC,aAAa,GAAG;IACpBxhB,KAAK,EAAE,EAAE;IACTiW,SAAS,EAAE,EAAE;IACbnG,IAAI,EAAE,EAAE;IACR3M,IAAI,EAAE,EAAE;IACRjC,MAAM,EAAE,EAAE;IACVtD,IAAI,EAAE4X,SAAS;IACfvC,SAAS,EAAEuC,SAAS;IACpB7D,QAAQ,EAAE6D,SAAS;IACnBiM,QAAQ,EAAEjM,SAAS;IACnB1S,KAAK,EAAE,KAAK;IACZoS,SAAS,EAAE,KAAK;IAChBwM,SAAS,EAAE,IAAI;IACfhX,KAAK,EAAE,OAAO;IACdjG,SAAS,EAAE;MACT7E,KAAK,EAAE,YAAY;MACnBwN,QAAQ,EAAE,qBAAqB;MAC/BxP,IAAI,EAAE;IACR,CAAC;IACDqe,SAAS,EAAE;MACTrc,KAAK,EAAE,YAAY;MACnBwN,QAAQ,EAAE,qBAAqB;MAC/BxP,IAAI,EAAE;IACR,CAAC;IACDgH,WAAW,EAAE,CAAC,CAAC;IACfa,MAAM,EAAE,MAAM;IACdyN,KAAK,EAAEsC,SAAS;IAChBpI,QAAQ,EAAE,IAAI;IACduU,UAAU,EAAE,IAAI;IAChBC,iBAAiB,EAAE,IAAI;IACvB1I,cAAc,EAAE,IAAI;IACpBV,aAAa,EAAE,IAAI;IACnBP,sBAAsB,EAAE,IAAI;IAC5BX,sBAAsB,EAAE,KAAK;IAC7BjM,iBAAiB,EAAE,IAAI;IACvBC,cAAc,EAAE,KAAK;IACrBC,gBAAgB,EAAE,KAAK;IACvByV,UAAU,EAAExL,SAAS;IACrBkL,OAAO,EAAElL,SAAS;IAClBqM,iBAAiB,EAAE,IAAI;IACvBC,sBAAsB,EAAE,EAAE;IAC1B3V,kBAAkB,EAAEqJ,SAAS;IAC7BuM,cAAc,EAAE,IAAI;IACpBC,mBAAmB,EAAE,EAAE;IACvB5V,eAAe,EAAEoJ,SAAS;IAC1ByM,gBAAgB,EAAE,QAAQ;IAC1BC,qBAAqB,EAAE,EAAE;IACzB7V,iBAAiB,EAAEmJ,SAAS;IAC5BvJ,cAAc,EAAE,IAAI;IACpBF,cAAc,EAAE,KAAK;IACrBoW,YAAY,EAAE,IAAI;IAClBC,SAAS,EAAE,KAAK;IAChBC,WAAW,EAAE,KAAK;IAClB5lB,WAAW,EAAE,IAAI;IACjBuQ,eAAe,EAAE,KAAK;IACtBD,eAAe,EAAE,SAAS;IAC1BE,oBAAoB,EAAE,mBAAmB;IACzCxB,UAAU,EAAE,EAAE;IACdsV,mBAAmB,EAAE,KAAK;IAC1BN,gBAAgB,EAAE,KAAK;IACvBlN,QAAQ,EAAEiC,SAAS;IACnB/B,UAAU,EAAE+B,SAAS;IACrB9B,WAAW,EAAE8B,SAAS;IACtBhC,QAAQ,EAAE,EAAE;IACZnL,KAAK,EAAEmN,SAAS;IAChBjN,gBAAgB,EAAE,KAAK;IACvBE,KAAK,EAAE+M,SAAS;IAChBR,OAAO,EAAEQ,SAAS;IAClB/H,UAAU,EAAE+H,SAAS;IACrBrQ,KAAK,EAAEqQ,SAAS;IAChBrG,gBAAgB,EAAE,EAAE;IACpBG,UAAU,EAAE,EAAE;IACdO,UAAU,EAAE,EAAE;IACdsO,YAAY,EAAE,CAAC,CAAC;IAChBzP,cAAc,EAAE,IAAI;IACpBsP,aAAa,EAAE,IAAI;IACnB5P,eAAe,EAAE,CAAC,CAAC;IACnB4R,cAAc,EAAExK,SAAS;IACzBsK,sBAAsB,EAAE,KAAK;IAC7BO,iBAAiB,EAAE7K,SAAS;IAC5BhI,IAAI,EAAE,KAAK;IACXF,QAAQ,EAAE,QAAQ;IAClBgI,aAAa,EAAE,EAAE;IACjBC,mBAAmB,EAAEC,SAAS;IAC9BO,qBAAqB,EAAEP,SAAS;IAChC8M,QAAQ,EAAE9M,SAAS;IACnB+M,OAAO,EAAE/M,SAAS;IAClBW,SAAS,EAAEX,SAAS;IACpBkH,SAAS,EAAElH,SAAS;IACpB+F,QAAQ,EAAE/F,SAAS;IACnBgN,UAAU,EAAEhN,SAAS;IACrBiN,gBAAgB,EAAE,IAAI;IACtB9X,QAAQ,EAAE;EACZ,CAAC;EACD,MAAM+X,eAAe,GAAG,CAAC,gBAAgB,EAAE,mBAAmB,EAAE,YAAY,EAAE,gBAAgB,EAAE,uBAAuB,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,sBAAsB,EAAE,iBAAiB,EAAE,OAAO,EAAE,wBAAwB,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,qBAAqB,EAAE,aAAa,EAAE,qBAAqB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,UAAU,EAAE,YAAY,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,aAAa,EAAE,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,eAAe,EAAE,aAAa,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,gBAAgB,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,CAAC;;EAEruB;EACA,MAAMC,gBAAgB,GAAG;IACvBnK,aAAa,EAAEhD;EACjB,CAAC;EACD,MAAMoN,uBAAuB,GAAG,CAAC,mBAAmB,EAAE,eAAe,EAAE,UAAU,EAAE,WAAW,EAAE,cAAc,EAAE,WAAW,EAAE,aAAa,EAAE,aAAa,EAAE,YAAY,EAAE,wBAAwB,CAAC;;EAElM;AACF;AACA;AACA;AACA;AACA;EACE,MAAMC,gBAAgB,GAAGC,SAAS,IAAI;IACpC,OAAOve,MAAM,CAACwe,SAAS,CAACC,cAAc,CAAChG,IAAI,CAACwE,aAAa,EAAEsB,SAAS,CAAC;EACvE,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;EACE,MAAMG,oBAAoB,GAAGH,SAAS,IAAI;IACxC,OAAOJ,eAAe,CAAC9Q,OAAO,CAACkR,SAAS,CAAC,KAAK,CAAC,CAAC;EAClD,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;EACE,MAAMI,qBAAqB,GAAGJ,SAAS,IAAI;IACzC,OAAOH,gBAAgB,CAACG,SAAS,CAAC;EACpC,CAAC;;EAED;AACF;AACA;EACE,MAAMK,mBAAmB,GAAGrY,KAAK,IAAI;IACnC,IAAI,CAAC+X,gBAAgB,CAAC/X,KAAK,CAAC,EAAE;MAC5B3M,IAAI,CAAC,sBAAsB2M,KAAK,GAAG,CAAC;IACtC;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAMsY,wBAAwB,GAAGtY,KAAK,IAAI;IACxC,IAAI8X,uBAAuB,CAAClkB,QAAQ,CAACoM,KAAK,CAAC,EAAE;MAC3C3M,IAAI,CAAC,kBAAkB2M,KAAK,+BAA+B,CAAC;IAC9D;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAMuY,wBAAwB,GAAGvY,KAAK,IAAI;IACxC,MAAMwY,YAAY,GAAGJ,qBAAqB,CAACpY,KAAK,CAAC;IACjD,IAAIwY,YAAY,EAAE;MAChB1kB,oBAAoB,CAACkM,KAAK,EAAEwY,YAAY,CAAC;IAC3C;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMC,qBAAqB,GAAGjf,MAAM,IAAI;IACtC,IAAIA,MAAM,CAAC8I,QAAQ,KAAK,KAAK,IAAI9I,MAAM,CAACsd,iBAAiB,EAAE;MACzDzjB,IAAI,CAAC,iFAAiF,CAAC;IACzF;IACA,IAAImG,MAAM,CAACoG,KAAK,IAAI,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,cAAc,EAAE,OAAO,EAAE,aAAa,EAAE,YAAY,CAAC,CAAChM,QAAQ,CAAC4F,MAAM,CAACoG,KAAK,CAAC,EAAE;MACpJvM,IAAI,CAAC,kBAAkBmG,MAAM,CAACoG,KAAK,GAAG,CAAC;IACzC;IACA,KAAK,MAAMI,KAAK,IAAIxG,MAAM,EAAE;MAC1B6e,mBAAmB,CAACrY,KAAK,CAAC;MAC1B,IAAIxG,MAAM,CAACxB,KAAK,EAAE;QAChBsgB,wBAAwB,CAACtY,KAAK,CAAC;MACjC;MACAuY,wBAAwB,CAACvY,KAAK,CAAC;IACjC;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,SAAS0Y,MAAMA,CAAClf,MAAM,EAAE;IACtB,MAAM/E,SAAS,GAAGF,YAAY,CAAC,CAAC;IAChC,MAAMO,KAAK,GAAGD,QAAQ,CAAC,CAAC;IACxB,MAAMiO,WAAW,GAAGD,YAAY,CAACC,WAAW,CAACjS,GAAG,CAAC,IAAI,CAAC;IACtD,IAAI,CAACiE,KAAK,IAAI+C,QAAQ,CAAC/C,KAAK,EAAEgO,WAAW,CAACqO,SAAS,CAACrc,KAAK,CAAC,EAAE;MAC1DzB,IAAI,CAAC,4IAA4I,CAAC;MAClJ;IACF;IACA,MAAMslB,oBAAoB,GAAGC,iBAAiB,CAACpf,MAAM,CAAC;IACtD,MAAMqf,aAAa,GAAGpf,MAAM,CAACgY,MAAM,CAAC,CAAC,CAAC,EAAE3O,WAAW,EAAE6V,oBAAoB,CAAC;IAC1EF,qBAAqB,CAACI,aAAa,CAAC;IACpCpkB,SAAS,CAACkL,OAAO,CAAC,YAAY,CAAC,GAAGkZ,aAAa,CAACjZ,KAAK;IACrDwL,MAAM,CAAC,IAAI,EAAEyN,aAAa,CAAC;IAC3BhW,YAAY,CAACC,WAAW,CAAC/R,GAAG,CAAC,IAAI,EAAE8nB,aAAa,CAAC;IACjDpf,MAAM,CAACqf,gBAAgB,CAAC,IAAI,EAAE;MAC5Btf,MAAM,EAAE;QACNiB,KAAK,EAAEhB,MAAM,CAACgY,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACjY,MAAM,EAAEA,MAAM,CAAC;QAC7Cuf,QAAQ,EAAE,KAAK;QACfC,UAAU,EAAE;MACd;IACF,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;EACE,MAAMJ,iBAAiB,GAAGpf,MAAM,IAAI;IAClC,MAAMmf,oBAAoB,GAAG,CAAC,CAAC;IAC/Blf,MAAM,CAACiK,IAAI,CAAClK,MAAM,CAAC,CAACX,OAAO,CAACmH,KAAK,IAAI;MACnC,IAAImY,oBAAoB,CAACnY,KAAK,CAAC,EAAE;QAC/B2Y,oBAAoB,CAAC3Y,KAAK,CAAC,GAAGxG,MAAM,CAACwG,KAAK,CAAC;MAC7C,CAAC,MAAM;QACL3M,IAAI,CAAC,gCAAgC2M,KAAK,EAAE,CAAC;MAC/C;IACF,CAAC,CAAC;IACF,OAAO2Y,oBAAoB;EAC7B,CAAC;;EAED;AACF;AACA;EACE,SAASrH,QAAQA,CAAA,EAAG;IAClB,MAAMtO,QAAQ,GAAGH,YAAY,CAACG,QAAQ,CAACnS,GAAG,CAAC,IAAI,CAAC;IAChD,MAAMiS,WAAW,GAAGD,YAAY,CAACC,WAAW,CAACjS,GAAG,CAAC,IAAI,CAAC;IACtD,IAAI,CAACiS,WAAW,EAAE;MAChBmW,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;MACvB,OAAO,CAAC;IACV;;IAEA;IACA,IAAIjW,QAAQ,CAAClO,KAAK,IAAI3D,WAAW,CAAC2gB,8BAA8B,EAAE;MAChE3gB,WAAW,CAAC2gB,8BAA8B,CAAC,CAAC;MAC5C,OAAO3gB,WAAW,CAAC2gB,8BAA8B;IACnD;IACA,IAAI,OAAOhP,WAAW,CAAC4U,UAAU,KAAK,UAAU,EAAE;MAChD5U,WAAW,CAAC4U,UAAU,CAAC,CAAC;IAC1B;IACAvmB,WAAW,CAACma,YAAY,CAACC,IAAI,CAAC,YAAY,CAAC;IAC3C2N,WAAW,CAAC,IAAI,CAAC;EACnB;;EAEA;AACF;AACA;EACE,MAAMA,WAAW,GAAG5Y,QAAQ,IAAI;IAC9B2Y,eAAe,CAAC3Y,QAAQ,CAAC;IACzB;IACA,OAAOA,QAAQ,CAAC9G,MAAM;IACtB;IACA,OAAOrI,WAAW,CAACmb,cAAc;IACjC,OAAOnb,WAAW,CAACib,aAAa;IAChC;IACA,OAAOjb,WAAW,CAACwN,eAAe;EACpC,CAAC;;EAED;AACF;AACA;EACE,MAAMsa,eAAe,GAAG3Y,QAAQ,IAAI;IAClC;IACA,IAAIA,QAAQ,CAAC0Q,iBAAiB,EAAE;MAC9BmI,aAAa,CAACtW,YAAY,EAAEvC,QAAQ,CAAC;MACrCA,QAAQ,CAAC0Q,iBAAiB,GAAG,IAAI;IACnC,CAAC,MAAM;MACLmI,aAAa,CAAC9K,cAAc,EAAE/N,QAAQ,CAAC;MACvC6Y,aAAa,CAACtW,YAAY,EAAEvC,QAAQ,CAAC;MACrC,OAAOA,QAAQ,CAAC0Q,iBAAiB;MACjC;MACA,OAAO1Q,QAAQ,CAACuU,cAAc;MAC9B,OAAOvU,QAAQ,CAAC+U,aAAa;MAC7B,OAAO/U,QAAQ,CAACwS,QAAQ;MACxB,OAAOxS,QAAQ,CAACkV,YAAY;MAC5B,OAAOlV,QAAQ,CAACoV,WAAW;MAC3B,OAAOpV,QAAQ,CAACmT,WAAW;MAC3B,OAAOnT,QAAQ,CAAC8Y,cAAc;MAC9B,OAAO9Y,QAAQ,CAACgV,qBAAqB;MACrC,OAAOhV,QAAQ,CAAC1B,sBAAsB;MACtC,OAAO0B,QAAQ,CAAC/J,KAAK;MACrB,OAAO+J,QAAQ,CAAC+Y,UAAU;MAC1B,OAAO/Y,QAAQ,CAACgZ,UAAU;MAC1B,OAAOhZ,QAAQ,CAACiZ,UAAU;MAC1B,OAAOjZ,QAAQ,CAAC+Q,aAAa;MAC7B,OAAO/Q,QAAQ,CAACoY,MAAM;MACtB,OAAOpY,QAAQ,CAACgR,QAAQ;IAC1B;EACF,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAM6H,aAAa,GAAGA,CAACK,GAAG,EAAElZ,QAAQ,KAAK;IACvC,KAAK,MAAMjH,CAAC,IAAImgB,GAAG,EAAE;MACnBA,GAAG,CAACngB,CAAC,CAAC,CAACogB,MAAM,CAACnZ,QAAQ,CAAC;IACzB;EACF,CAAC;EAED,IAAIoZ,eAAe,GAAG,aAAajgB,MAAM,CAACwS,MAAM,CAAC;IAC/C0N,SAAS,EAAE,IAAI;IACfrI,QAAQ,EAAEA,QAAQ;IAClB/a,KAAK,EAAEA,KAAK;IACZ+iB,UAAU,EAAE/iB,KAAK;IACjB8iB,UAAU,EAAE9iB,KAAK;IACjBgjB,UAAU,EAAEhjB,KAAK;IACjBse,cAAc,EAAEA,cAAc;IAC9BW,YAAY,EAAEA,YAAY;IAC1B4D,cAAc,EAAE3F,WAAW;IAC3B4B,aAAa,EAAEA,aAAa;IAC5BK,WAAW,EAAEA,WAAW;IACxB5C,QAAQ,EAAEA,QAAQ;IAClB5B,qBAAqB,EAAEA,qBAAqB;IAC5CuC,WAAW,EAAEA,WAAW;IACxBpC,aAAa,EAAEA,aAAa;IAC5BzS,sBAAsB,EAAEA,sBAAsB;IAC9C0W,qBAAqB,EAAEA,qBAAqB;IAC5CoD,MAAM,EAAEA;EACV,CAAC,CAAC;;EAEF;AACF;AACA;AACA;AACA;EACE,MAAMkB,gBAAgB,GAAGA,CAAC9W,WAAW,EAAEE,QAAQ,EAAE0J,WAAW,KAAK;IAC/D,IAAI5J,WAAW,CAAC9K,KAAK,EAAE;MACrB6hB,gBAAgB,CAAC/W,WAAW,EAAEE,QAAQ,EAAE0J,WAAW,CAAC;IACtD,CAAC,MAAM;MACL;MACA;MACAoN,oBAAoB,CAAC9W,QAAQ,CAAC;;MAE9B;MACA+W,wBAAwB,CAAC/W,QAAQ,CAAC;MAClCgX,gBAAgB,CAAClX,WAAW,EAAEE,QAAQ,EAAE0J,WAAW,CAAC;IACtD;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMmN,gBAAgB,GAAGA,CAAC/W,WAAW,EAAEE,QAAQ,EAAE0J,WAAW,KAAK;IAC/D;IACA1J,QAAQ,CAAClO,KAAK,CAACmlB,OAAO,GAAG,MAAM;MAC7B,IAAInX,WAAW,KAAKoX,gBAAgB,CAACpX,WAAW,CAAC,IAAIA,WAAW,CAACvF,KAAK,IAAIuF,WAAW,CAACzI,KAAK,CAAC,EAAE;QAC5F;MACF;MACAqS,WAAW,CAACV,aAAa,CAACzV,KAAK,CAAC;IAClC,CAAC;EACH,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAM2jB,gBAAgB,GAAGpX,WAAW,IAAI;IACtC,OAAO,CAAC,EAAEA,WAAW,CAACvC,iBAAiB,IAAIuC,WAAW,CAACtC,cAAc,IAAIsC,WAAW,CAACrC,gBAAgB,IAAIqC,WAAW,CAACZ,eAAe,CAAC;EACvI,CAAC;EACD,IAAIiY,kBAAkB,GAAG,KAAK;;EAE9B;AACF;AACA;EACE,MAAML,oBAAoB,GAAG9W,QAAQ,IAAI;IACvCA,QAAQ,CAAClO,KAAK,CAACslB,WAAW,GAAG,MAAM;MACjCpX,QAAQ,CAACvO,SAAS,CAAC4lB,SAAS,GAAG,UAAUnqB,CAAC,EAAE;QAC1C8S,QAAQ,CAACvO,SAAS,CAAC4lB,SAAS,GAAG,MAAM,CAAC,CAAC;QACvC;QACA;QACA,IAAInqB,CAAC,CAACyK,MAAM,KAAKqI,QAAQ,CAACvO,SAAS,EAAE;UACnC0lB,kBAAkB,GAAG,IAAI;QAC3B;MACF,CAAC;IACH,CAAC;EACH,CAAC;;EAED;AACF;AACA;EACE,MAAMJ,wBAAwB,GAAG/W,QAAQ,IAAI;IAC3CA,QAAQ,CAACvO,SAAS,CAAC2lB,WAAW,GAAGlqB,CAAC,IAAI;MACpC;MACA,IAAIA,CAAC,CAACyK,MAAM,KAAKqI,QAAQ,CAACvO,SAAS,EAAE;QACnCvE,CAAC,CAAC0d,cAAc,CAAC,CAAC;MACpB;MACA5K,QAAQ,CAAClO,KAAK,CAACulB,SAAS,GAAG,UAAUnqB,CAAC,EAAE;QACtC8S,QAAQ,CAAClO,KAAK,CAACulB,SAAS,GAAG,MAAM,CAAC,CAAC;QACnC;QACA,IAAInqB,CAAC,CAACyK,MAAM,KAAKqI,QAAQ,CAAClO,KAAK,IAAI5E,CAAC,CAACyK,MAAM,YAAYrJ,WAAW,IAAI0R,QAAQ,CAAClO,KAAK,CAACwE,QAAQ,CAACpJ,CAAC,CAACyK,MAAM,CAAC,EAAE;UACvGwf,kBAAkB,GAAG,IAAI;QAC3B;MACF,CAAC;IACH,CAAC;EACH,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMH,gBAAgB,GAAGA,CAAClX,WAAW,EAAEE,QAAQ,EAAE0J,WAAW,KAAK;IAC/D1J,QAAQ,CAACvO,SAAS,CAACwlB,OAAO,GAAG/pB,CAAC,IAAI;MAChC,IAAIiqB,kBAAkB,EAAE;QACtBA,kBAAkB,GAAG,KAAK;QAC1B;MACF;MACA,IAAIjqB,CAAC,CAACyK,MAAM,KAAKqI,QAAQ,CAACvO,SAAS,IAAIR,cAAc,CAAC6O,WAAW,CAACgU,iBAAiB,CAAC,EAAE;QACpFpK,WAAW,CAACV,aAAa,CAAC1J,QAAQ,CAAC;MACrC;IACF,CAAC;EACH,CAAC;EAED,MAAMgY,eAAe,GAAGliB,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAAC8H,MAAM;EACvE,MAAMqa,SAAS,GAAGniB,IAAI,IAAIA,IAAI,YAAYoiB,OAAO,IAAIF,eAAe,CAACliB,IAAI,CAAC;EAC1E,MAAMqiB,YAAY,GAAGC,IAAI,IAAI;IAC3B,MAAMlhB,MAAM,GAAG,CAAC,CAAC;IACjB,IAAI,OAAOkhB,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,CAACH,SAAS,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;MACtDjhB,MAAM,CAACgY,MAAM,CAACjY,MAAM,EAAEkhB,IAAI,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,MAAM;MACL,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC7hB,OAAO,CAAC,CAACmL,IAAI,EAAE4G,KAAK,KAAK;QACjD,MAAM1W,GAAG,GAAGwmB,IAAI,CAAC9P,KAAK,CAAC;QACvB,IAAI,OAAO1W,GAAG,KAAK,QAAQ,IAAIqmB,SAAS,CAACrmB,GAAG,CAAC,EAAE;UAC7CsF,MAAM,CAACwK,IAAI,CAAC,GAAG9P,GAAG;QACpB,CAAC,MAAM,IAAIA,GAAG,KAAKwW,SAAS,EAAE;UAC5BjX,KAAK,CAAC,sBAAsBuQ,IAAI,yCAAyC,OAAO9P,GAAG,EAAE,CAAC;QACxF;MACF,CAAC,CAAC;IACJ;IACA,OAAOsF,MAAM;EACf,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;EACE,SAASmhB,IAAIA,CAAC,GAAGD,IAAI,EAAE;IACrB,OAAO,IAAI,IAAI,CAAC,GAAGA,IAAI,CAAC;EAC1B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASE,KAAKA,CAACC,WAAW,EAAE;IAC1B,MAAMC,SAAS,SAAS,IAAI,CAAC;MAC3BC,KAAKA,CAACvhB,MAAM,EAAEwhB,mBAAmB,EAAE;QACjC,OAAO,KAAK,CAACD,KAAK,CAACvhB,MAAM,EAAEC,MAAM,CAACgY,MAAM,CAAC,CAAC,CAAC,EAAEoJ,WAAW,EAAEG,mBAAmB,CAAC,CAAC;MACjF;IACF;IACA;IACA,OAAOF,SAAS;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAMG,YAAY,GAAGA,CAAA,KAAM;IACzB,OAAO9pB,WAAW,CAAC+pB,OAAO,IAAI/pB,WAAW,CAAC+pB,OAAO,CAACD,YAAY,CAAC,CAAC;EAClE,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;EACE,MAAME,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAIhqB,WAAW,CAAC+pB,OAAO,EAAE;MACvBtd,oBAAoB,CAAC,CAAC;MACtB,OAAOzM,WAAW,CAAC+pB,OAAO,CAACE,IAAI,CAAC,CAAC;IACnC;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;EACE,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIlqB,WAAW,CAAC+pB,OAAO,EAAE;MACvB,MAAMI,SAAS,GAAGnqB,WAAW,CAAC+pB,OAAO,CAACK,KAAK,CAAC,CAAC;MAC7Cje,uBAAuB,CAACge,SAAS,CAAC;MAClC,OAAOA,SAAS;IAClB;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;EACE,MAAME,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMje,KAAK,GAAGpM,WAAW,CAAC+pB,OAAO;IACjC,OAAO3d,KAAK,KAAKA,KAAK,CAACke,OAAO,GAAGN,SAAS,CAAC,CAAC,GAAGE,WAAW,CAAC,CAAC,CAAC;EAC/D,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAMK,aAAa,GAAGC,EAAE,IAAI;IAC1B,IAAIxqB,WAAW,CAAC+pB,OAAO,EAAE;MACvB,MAAMI,SAAS,GAAGnqB,WAAW,CAAC+pB,OAAO,CAACU,QAAQ,CAACD,EAAE,CAAC;MAClDre,uBAAuB,CAACge,SAAS,EAAE,IAAI,CAAC;MACxC,OAAOA,SAAS;IAClB;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAMO,cAAc,GAAGA,CAAA,KAAM;IAC3B,OAAO,CAAC,EAAE1qB,WAAW,CAAC+pB,OAAO,IAAI/pB,WAAW,CAAC+pB,OAAO,CAACY,SAAS,CAAC,CAAC,CAAC;EACnE,CAAC;EAED,IAAIC,sBAAsB,GAAG,KAAK;EAClC,MAAMC,aAAa,GAAG,CAAC,CAAC;;EAExB;AACF;AACA;EACE,SAASC,gBAAgBA,CAAC/X,IAAI,GAAG,oBAAoB,EAAE;IACrD8X,aAAa,CAAC9X,IAAI,CAAC,GAAG,IAAI;IAC1B,IAAI,CAAC6X,sBAAsB,EAAE;MAC3BvqB,QAAQ,CAACC,IAAI,CAACwV,gBAAgB,CAAC,OAAO,EAAEiV,iBAAiB,CAAC;MAC1DH,sBAAsB,GAAG,IAAI;IAC/B;EACF;EACA,MAAMG,iBAAiB,GAAG1S,KAAK,IAAI;IACjC,KAAK,IAAIhS,EAAE,GAAGgS,KAAK,CAAC7O,MAAM,EAAEnD,EAAE,IAAIA,EAAE,KAAKhG,QAAQ,EAAEgG,EAAE,GAAGA,EAAE,CAAC2kB,UAAU,EAAE;MACrE,KAAK,MAAMjY,IAAI,IAAI8X,aAAa,EAAE;QAChC,MAAMrF,QAAQ,GAAGnf,EAAE,CAACL,YAAY,CAAC+M,IAAI,CAAC;QACtC,IAAIyS,QAAQ,EAAE;UACZqF,aAAa,CAAC9X,IAAI,CAAC,CAACyW,IAAI,CAAC;YACvBhE;UACF,CAAC,CAAC;UACF;QACF;MACF;IACF;EACF,CAAC;;EAED;;EAEA,MAAMyF,YAAY,CAAC;IACjBC,WAAWA,CAAA,EAAG;MACZ;MACA,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;IAClB;;IAEA;AACJ;AACA;AACA;IACIC,uBAAuBA,CAACC,SAAS,EAAE;MACjC,IAAI,OAAO,IAAI,CAACF,MAAM,CAACE,SAAS,CAAC,KAAK,WAAW,EAAE;QACjD;QACA;QACA,IAAI,CAACF,MAAM,CAACE,SAAS,CAAC,GAAG,EAAE;MAC7B;MACA,OAAO,IAAI,CAACF,MAAM,CAACE,SAAS,CAAC;IAC/B;;IAEA;AACJ;AACA;AACA;IACIC,EAAEA,CAACD,SAAS,EAAEE,YAAY,EAAE;MAC1B,MAAMC,eAAe,GAAG,IAAI,CAACJ,uBAAuB,CAACC,SAAS,CAAC;MAC/D,IAAI,CAACG,eAAe,CAAC/oB,QAAQ,CAAC8oB,YAAY,CAAC,EAAE;QAC3CC,eAAe,CAAC9oB,IAAI,CAAC6oB,YAAY,CAAC;MACpC;IACF;;IAEA;AACJ;AACA;AACA;IACIE,IAAIA,CAACJ,SAAS,EAAEE,YAAY,EAAE;MAC5B;AACN;AACA;MACM,MAAMG,MAAM,GAAGA,CAAC,GAAGnC,IAAI,KAAK;QAC1B,IAAI,CAACoC,cAAc,CAACN,SAAS,EAAEK,MAAM,CAAC;QACtCH,YAAY,CAACK,KAAK,CAAC,IAAI,EAAErC,IAAI,CAAC;MAChC,CAAC;MACD,IAAI,CAAC+B,EAAE,CAACD,SAAS,EAAEK,MAAM,CAAC;IAC5B;;IAEA;AACJ;AACA;AACA;IACItR,IAAIA,CAACiR,SAAS,EAAE,GAAG9B,IAAI,EAAE;MACvB,IAAI,CAAC6B,uBAAuB,CAACC,SAAS,CAAC,CAAC3jB,OAAO;MAC/C;AACN;AACA;MACM6jB,YAAY,IAAI;QACd,IAAI;UACFA,YAAY,CAACK,KAAK,CAAC,IAAI,EAAErC,IAAI,CAAC;QAChC,CAAC,CAAC,OAAOjnB,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAACA,KAAK,CAAC;QACtB;MACF,CAAC,CAAC;IACJ;;IAEA;AACJ;AACA;AACA;IACIqpB,cAAcA,CAACN,SAAS,EAAEE,YAAY,EAAE;MACtC,MAAMC,eAAe,GAAG,IAAI,CAACJ,uBAAuB,CAACC,SAAS,CAAC;MAC/D,MAAM5R,KAAK,GAAG+R,eAAe,CAAC7V,OAAO,CAAC4V,YAAY,CAAC;MACnD,IAAI9R,KAAK,GAAG,CAAC,CAAC,EAAE;QACd+R,eAAe,CAACK,MAAM,CAACpS,KAAK,EAAE,CAAC,CAAC;MAClC;IACF;;IAEA;AACJ;AACA;IACIqS,kBAAkBA,CAACT,SAAS,EAAE;MAC5B,IAAI,IAAI,CAACF,MAAM,CAACE,SAAS,CAAC,KAAK9R,SAAS,EAAE;QACxC;QACA,IAAI,CAAC4R,MAAM,CAACE,SAAS,CAAC,CAACjsB,MAAM,GAAG,CAAC;MACnC;IACF;IACAiN,KAAKA,CAAA,EAAG;MACN,IAAI,CAAC8e,MAAM,GAAG,CAAC,CAAC;IAClB;EACF;EAEAnrB,WAAW,CAACma,YAAY,GAAG,IAAI8Q,YAAY,CAAC,CAAC;;EAE7C;AACF;AACA;AACA;EACE,MAAMK,EAAE,GAAGA,CAACD,SAAS,EAAEE,YAAY,KAAK;IACtCvrB,WAAW,CAACma,YAAY,CAACmR,EAAE,CAACD,SAAS,EAAEE,YAAY,CAAC;EACtD,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAME,IAAI,GAAGA,CAACJ,SAAS,EAAEE,YAAY,KAAK;IACxCvrB,WAAW,CAACma,YAAY,CAACsR,IAAI,CAACJ,SAAS,EAAEE,YAAY,CAAC;EACxD,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMQ,GAAG,GAAGA,CAACV,SAAS,EAAEE,YAAY,KAAK;IACvC;IACA,IAAI,CAACF,SAAS,EAAE;MACdrrB,WAAW,CAACma,YAAY,CAAC9N,KAAK,CAAC,CAAC;MAChC;IACF;IACA,IAAIkf,YAAY,EAAE;MAChB;MACAvrB,WAAW,CAACma,YAAY,CAACwR,cAAc,CAACN,SAAS,EAAEE,YAAY,CAAC;IAClE,CAAC,MAAM;MACL;MACAvrB,WAAW,CAACma,YAAY,CAAC2R,kBAAkB,CAACT,SAAS,CAAC;IACxD;EACF,CAAC;EAED,IAAIW,aAAa,GAAG,aAAa1jB,MAAM,CAACwS,MAAM,CAAC;IAC7C0N,SAAS,EAAE,IAAI;IACfc,YAAY,EAAEA,YAAY;IAC1BwB,gBAAgB,EAAEA,gBAAgB;IAClCnQ,WAAW,EAAEA,WAAW;IACxBL,YAAY,EAAEA,YAAY;IAC1BG,SAAS,EAAEA,SAAS;IACpBwR,aAAa,EAAEhL,WAAW;IAC1BuI,IAAI,EAAEA,IAAI;IACVzkB,UAAU,EAAEA,UAAU;IACtBP,eAAe,EAAEA,eAAe;IAChCW,cAAc,EAAEA,cAAc;IAC9Bd,gBAAgB,EAAEA,gBAAgB;IAClCjB,YAAY,EAAEA,YAAY;IAC1BsB,aAAa,EAAEA,aAAa;IAC5BY,oBAAoB,EAAEA,oBAAoB;IAC1CN,SAAS,EAAEA,SAAS;IACpBhB,gBAAgB,EAAEA,gBAAgB;IAClCJ,OAAO,EAAEA,OAAO;IAChBC,cAAc,EAAEA,cAAc;IAC9BI,QAAQ,EAAEA,QAAQ;IAClBW,aAAa,EAAEA,aAAa;IAC5BC,SAAS,EAAEA,SAAS;IACpBnB,QAAQ,EAAEA,QAAQ;IAClBS,gBAAgB,EAAEA,gBAAgB;IAClC2lB,YAAY,EAAEA,YAAY;IAC1B5kB,mBAAmB,EAAEA,mBAAmB;IACxCpB,QAAQ,EAAEA,QAAQ;IAClBM,oBAAoB,EAAEA,oBAAoB;IAC1CmmB,aAAa,EAAEA,aAAa;IAC5BtD,qBAAqB,EAAEA,qBAAqB;IAC5CngB,SAAS,EAAEA,SAAS;IACpB4jB,cAAc,EAAEA,cAAc;IAC9B1D,oBAAoB,EAAEA,oBAAoB;IAC1CJ,gBAAgB,EAAEA,gBAAgB;IAClCvM,SAAS,EAAEA,SAAS;IACpBoP,KAAK,EAAEA,KAAK;IACZsC,GAAG,EAAEA,GAAG;IACRT,EAAE,EAAEA,EAAE;IACNG,IAAI,EAAEA,IAAI;IACVvB,WAAW,EAAEA,WAAW;IACxBjJ,WAAW,EAAEA,WAAW;IACxB+I,SAAS,EAAEA,SAAS;IACpBK,WAAW,EAAEA;EACf,CAAC,CAAC;EAEF,MAAM6B,KAAK,CAAC;IACV;AACJ;AACA;AACA;IACIhB,WAAWA,CAACiB,QAAQ,EAAEC,KAAK,EAAE;MAC3B,IAAI,CAACD,QAAQ,GAAGA,QAAQ;MACxB,IAAI,CAAChC,SAAS,GAAGiC,KAAK;MACtB,IAAI,CAAC9B,OAAO,GAAG,KAAK;MACpB,IAAI,CAACF,KAAK,CAAC,CAAC;IACd;;IAEA;AACJ;AACA;IACIA,KAAKA,CAAA,EAAG;MACN,IAAI,CAAC,IAAI,CAACE,OAAO,EAAE;QACjB,IAAI,CAACA,OAAO,GAAG,IAAI;QACnB,IAAI,CAAC+B,OAAO,GAAG,IAAIC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC/Y,EAAE,GAAGtS,UAAU,CAAC,IAAI,CAACkrB,QAAQ,EAAE,IAAI,CAAChC,SAAS,CAAC;MACrD;MACA,OAAO,IAAI,CAACA,SAAS;IACvB;;IAEA;AACJ;AACA;IACIF,IAAIA,CAAA,EAAG;MACL,IAAI,IAAI,CAACoC,OAAO,IAAI,IAAI,CAAC/B,OAAO,EAAE;QAChC,IAAI,CAACA,OAAO,GAAG,KAAK;QACpBiC,YAAY,CAAC,IAAI,CAAChZ,EAAE,CAAC;QACrB,IAAI,CAAC4W,SAAS,IAAI,IAAImC,IAAI,CAAC,CAAC,CAACE,OAAO,CAAC,CAAC,GAAG,IAAI,CAACH,OAAO,CAACG,OAAO,CAAC,CAAC;MACjE;MACA,OAAO,IAAI,CAACrC,SAAS;IACvB;;IAEA;AACJ;AACA;AACA;IACIM,QAAQA,CAACxrB,CAAC,EAAE;MACV,MAAMqrB,OAAO,GAAG,IAAI,CAACA,OAAO;MAC5B,IAAIA,OAAO,EAAE;QACX,IAAI,CAACL,IAAI,CAAC,CAAC;MACb;MACA,IAAI,CAACE,SAAS,IAAIlrB,CAAC;MACnB,IAAIqrB,OAAO,EAAE;QACX,IAAI,CAACF,KAAK,CAAC,CAAC;MACd;MACA,OAAO,IAAI,CAACD,SAAS;IACvB;;IAEA;AACJ;AACA;IACIL,YAAYA,CAAA,EAAG;MACb,IAAI,IAAI,CAACQ,OAAO,EAAE;QAChB,IAAI,CAACL,IAAI,CAAC,CAAC;QACX,IAAI,CAACG,KAAK,CAAC,CAAC;MACd;MACA,OAAO,IAAI,CAACD,SAAS;IACvB;;IAEA;AACJ;AACA;IACIQ,SAASA,CAAA,EAAG;MACV,OAAO,IAAI,CAACL,OAAO;IACrB;EACF;EAEA,MAAMmC,gBAAgB,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,aAAa,CAAC;;EAEnE;AACF;AACA;AACA;EACE,MAAMC,iBAAiB,GAAGrkB,MAAM,IAAI;IAClC,MAAMmd,QAAQ,GAAG,OAAOnd,MAAM,CAACmd,QAAQ,KAAK,QAAQ,IAAI,kCAAkCnlB,QAAQ,CAACgD,aAAa,CAACgF,MAAM,CAACmd,QAAQ,CAAC,IAAInd,MAAM,CAACmd,QAAQ;IACpJ,IAAI,CAACA,QAAQ,EAAE;MACb,OAAO,CAAC,CAAC;IACX;IACA;IACA,MAAMmH,eAAe,GAAGnH,QAAQ,CAACpO,OAAO;IACxCwV,uBAAuB,CAACD,eAAe,CAAC;IACxC,MAAMrJ,MAAM,GAAGhb,MAAM,CAACgY,MAAM,CAACuM,aAAa,CAACF,eAAe,CAAC,EAAEG,qBAAqB,CAACH,eAAe,CAAC,EAAEI,cAAc,CAACJ,eAAe,CAAC,EAAEK,YAAY,CAACL,eAAe,CAAC,EAAEM,WAAW,CAACN,eAAe,CAAC,EAAEO,YAAY,CAACP,eAAe,CAAC,EAAEQ,mBAAmB,CAACR,eAAe,EAAEF,gBAAgB,CAAC,CAAC;IACzR,OAAOnJ,MAAM;EACf,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMuJ,aAAa,GAAGF,eAAe,IAAI;IACvC;IACA,MAAMrJ,MAAM,GAAG,CAAC,CAAC;IACjB;IACA,MAAM8J,UAAU,GAAG1nB,KAAK,CAACC,IAAI,CAACgnB,eAAe,CAACnnB,gBAAgB,CAAC,YAAY,CAAC,CAAC;IAC7E4nB,UAAU,CAAC1lB,OAAO,CAACmH,KAAK,IAAI;MAC1Bwe,yBAAyB,CAACxe,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;MACnD,MAAMgY,SAAS,GAAG,sCAAsChY,KAAK,CAAC7I,YAAY,CAAC,MAAM,CAAC;MAClF,MAAMsD,KAAK,GAAGuF,KAAK,CAAC7I,YAAY,CAAC,OAAO,CAAC;MACzC,IAAI,CAAC6gB,SAAS,IAAI,CAACvd,KAAK,EAAE;QACxB;MACF;MACA,IAAI,OAAOic,aAAa,CAACsB,SAAS,CAAC,KAAK,SAAS,EAAE;QACjDvD,MAAM,CAACuD,SAAS,CAAC,GAAGvd,KAAK,KAAK,OAAO;MACvC,CAAC,MAAM,IAAI,OAAOic,aAAa,CAACsB,SAAS,CAAC,KAAK,QAAQ,EAAE;QACvDvD,MAAM,CAACuD,SAAS,CAAC,GAAGyG,IAAI,CAACC,KAAK,CAACjkB,KAAK,CAAC;MACvC,CAAC,MAAM;QACLga,MAAM,CAACuD,SAAS,CAAC,GAAGvd,KAAK;MAC3B;IACF,CAAC,CAAC;IACF,OAAOga,MAAM;EACf,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMwJ,qBAAqB,GAAGH,eAAe,IAAI;IAC/C;IACA,MAAMrJ,MAAM,GAAG,CAAC,CAAC;IACjB;IACA,MAAMkK,aAAa,GAAG9nB,KAAK,CAACC,IAAI,CAACgnB,eAAe,CAACnnB,gBAAgB,CAAC,qBAAqB,CAAC,CAAC;IACzFgoB,aAAa,CAAC9lB,OAAO,CAACmH,KAAK,IAAI;MAC7B,MAAMgY,SAAS,GAAG,sCAAsChY,KAAK,CAAC7I,YAAY,CAAC,MAAM,CAAC;MAClF,MAAMsD,KAAK,GAAGuF,KAAK,CAAC7I,YAAY,CAAC,OAAO,CAAC;MACzC,IAAI,CAAC6gB,SAAS,IAAI,CAACvd,KAAK,EAAE;QACxB;MACF;MACAga,MAAM,CAACuD,SAAS,CAAC,GAAG,IAAI4G,QAAQ,CAAC,UAAUnkB,KAAK,EAAE,CAAC,CAAC,CAAC;IACvD,CAAC,CAAC;IACF,OAAOga,MAAM;EACf,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMyJ,cAAc,GAAGJ,eAAe,IAAI;IACxC;IACA,MAAMrJ,MAAM,GAAG,CAAC,CAAC;IACjB;IACA,MAAMoK,WAAW,GAAGhoB,KAAK,CAACC,IAAI,CAACgnB,eAAe,CAACnnB,gBAAgB,CAAC,aAAa,CAAC,CAAC;IAC/EkoB,WAAW,CAAChmB,OAAO,CAAC4I,MAAM,IAAI;MAC5B+c,yBAAyB,CAAC/c,MAAM,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;MAClE,MAAMlH,IAAI,GAAGkH,MAAM,CAACtK,YAAY,CAAC,MAAM,CAAC;MACxC,IAAI,CAACoD,IAAI,IAAI,CAAC,CAAC,SAAS,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC3G,QAAQ,CAAC2G,IAAI,CAAC,EAAE;QAC1D;MACF;MACAka,MAAM,CAAC,GAAGla,IAAI,YAAY,CAAC,GAAGkH,MAAM,CAAC3F,SAAS;MAC9C2Y,MAAM,CAAC,OAAOzhB,qBAAqB,CAACuH,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI;MACzD,IAAIkH,MAAM,CAACvJ,YAAY,CAAC,OAAO,CAAC,EAAE;QAChCuc,MAAM,CAAC,GAAGla,IAAI,aAAa,CAAC,GAAGkH,MAAM,CAACtK,YAAY,CAAC,OAAO,CAAC;MAC7D;MACA,IAAIsK,MAAM,CAACvJ,YAAY,CAAC,YAAY,CAAC,EAAE;QACrCuc,MAAM,CAAC,GAAGla,IAAI,iBAAiB,CAAC,GAAGkH,MAAM,CAACtK,YAAY,CAAC,YAAY,CAAC;MACtE;IACF,CAAC,CAAC;IACF,OAAOsd,MAAM;EACf,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAM0J,YAAY,GAAGL,eAAe,IAAI;IACtC,MAAMrJ,MAAM,GAAG,CAAC,CAAC;IACjB;IACA,MAAMpf,KAAK,GAAGyoB,eAAe,CAACtpB,aAAa,CAAC,YAAY,CAAC;IACzD,IAAIa,KAAK,EAAE;MACTmpB,yBAAyB,CAACnpB,KAAK,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;MACnE,IAAIA,KAAK,CAAC6C,YAAY,CAAC,KAAK,CAAC,EAAE;QAC7Buc,MAAM,CAAChM,QAAQ,GAAGpT,KAAK,CAAC8B,YAAY,CAAC,KAAK,CAAC,IAAIuT,SAAS;MAC1D;MACA,IAAIrV,KAAK,CAAC6C,YAAY,CAAC,OAAO,CAAC,EAAE;QAC/Buc,MAAM,CAAC9L,UAAU,GAAGtT,KAAK,CAAC8B,YAAY,CAAC,OAAO,CAAC,IAAIuT,SAAS;MAC9D;MACA,IAAIrV,KAAK,CAAC6C,YAAY,CAAC,QAAQ,CAAC,EAAE;QAChCuc,MAAM,CAAC7L,WAAW,GAAGvT,KAAK,CAAC8B,YAAY,CAAC,QAAQ,CAAC,IAAIuT,SAAS;MAChE;MACA,IAAIrV,KAAK,CAAC6C,YAAY,CAAC,KAAK,CAAC,EAAE;QAC7Buc,MAAM,CAAC/L,QAAQ,GAAGrT,KAAK,CAAC8B,YAAY,CAAC,KAAK,CAAC,IAAIuT,SAAS;MAC1D;IACF;IACA,OAAO+J,MAAM;EACf,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAM2J,WAAW,GAAGN,eAAe,IAAI;IACrC,MAAMrJ,MAAM,GAAG,CAAC,CAAC;IACjB;IACA,MAAM3hB,IAAI,GAAGgrB,eAAe,CAACtpB,aAAa,CAAC,WAAW,CAAC;IACvD,IAAI1B,IAAI,EAAE;MACR0rB,yBAAyB,CAAC1rB,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;MAClD,IAAIA,IAAI,CAACoF,YAAY,CAAC,MAAM,CAAC,EAAE;QAC7Buc,MAAM,CAAC3hB,IAAI,GAAGA,IAAI,CAACqE,YAAY,CAAC,MAAM,CAAC;MACzC;MACA,IAAIrE,IAAI,CAACoF,YAAY,CAAC,OAAO,CAAC,EAAE;QAC9Buc,MAAM,CAACtM,SAAS,GAAGrV,IAAI,CAACqE,YAAY,CAAC,OAAO,CAAC;MAC/C;MACAsd,MAAM,CAAC5N,QAAQ,GAAG/T,IAAI,CAACgJ,SAAS;IAClC;IACA,OAAO2Y,MAAM;EACf,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAM4J,YAAY,GAAGP,eAAe,IAAI;IACtC;IACA,MAAMrJ,MAAM,GAAG,CAAC,CAAC;IACjB;IACA,MAAMpa,KAAK,GAAGyjB,eAAe,CAACtpB,aAAa,CAAC,YAAY,CAAC;IACzD,IAAI6F,KAAK,EAAE;MACTmkB,yBAAyB,CAACnkB,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;MAC3Eoa,MAAM,CAACpa,KAAK,GAAGA,KAAK,CAAClD,YAAY,CAAC,MAAM,CAAC,IAAI,MAAM;MACnD,IAAIkD,KAAK,CAACnC,YAAY,CAAC,OAAO,CAAC,EAAE;QAC/Buc,MAAM,CAACjQ,UAAU,GAAGnK,KAAK,CAAClD,YAAY,CAAC,OAAO,CAAC;MACjD;MACA,IAAIkD,KAAK,CAACnC,YAAY,CAAC,aAAa,CAAC,EAAE;QACrCuc,MAAM,CAACpQ,gBAAgB,GAAGhK,KAAK,CAAClD,YAAY,CAAC,aAAa,CAAC;MAC7D;MACA,IAAIkD,KAAK,CAACnC,YAAY,CAAC,OAAO,CAAC,EAAE;QAC/Buc,MAAM,CAAC1P,UAAU,GAAG1K,KAAK,CAAClD,YAAY,CAAC,OAAO,CAAC;MACjD;IACF;IACA;IACA,MAAMkc,YAAY,GAAGxc,KAAK,CAACC,IAAI,CAACgnB,eAAe,CAACnnB,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;IACtF,IAAI0c,YAAY,CAAC9iB,MAAM,EAAE;MACvBkkB,MAAM,CAACpB,YAAY,GAAG,CAAC,CAAC;MACxBA,YAAY,CAACxa,OAAO,CAACkb,MAAM,IAAI;QAC7ByK,yBAAyB,CAACzK,MAAM,EAAE,CAAC,OAAO,CAAC,CAAC;QAC5C,MAAMD,WAAW,GAAGC,MAAM,CAAC5c,YAAY,CAAC,OAAO,CAAC;QAChD,IAAI,CAAC2c,WAAW,EAAE;UAChB;QACF;QACA,MAAMgL,UAAU,GAAG/K,MAAM,CAACjY,SAAS;QACnC2Y,MAAM,CAACpB,YAAY,CAACS,WAAW,CAAC,GAAGgL,UAAU;MAC/C,CAAC,CAAC;IACJ;IACA,OAAOrK,MAAM;EACf,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAM6J,mBAAmB,GAAGA,CAACR,eAAe,EAAEiB,UAAU,KAAK;IAC3D;IACA,MAAMtK,MAAM,GAAG,CAAC,CAAC;IACjB,KAAK,MAAMpb,CAAC,IAAI0lB,UAAU,EAAE;MAC1B,MAAM/G,SAAS,GAAG+G,UAAU,CAAC1lB,CAAC,CAAC;MAC/B;MACA,MAAM2lB,GAAG,GAAGlB,eAAe,CAACtpB,aAAa,CAACwjB,SAAS,CAAC;MACpD,IAAIgH,GAAG,EAAE;QACPR,yBAAyB,CAACQ,GAAG,EAAE,EAAE,CAAC;QAClCvK,MAAM,CAACuD,SAAS,CAAC1Z,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,GAAG0gB,GAAG,CAACljB,SAAS,CAACoM,IAAI,CAAC,CAAC;MAChE;IACF;IACA,OAAOuM,MAAM;EACf,CAAC;;EAED;AACF;AACA;EACE,MAAMsJ,uBAAuB,GAAGD,eAAe,IAAI;IACjD,MAAMmB,eAAe,GAAGrB,gBAAgB,CAAClmB,MAAM,CAAC,CAAC,YAAY,EAAE,qBAAqB,EAAE,aAAa,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAAC;IACnKb,KAAK,CAACC,IAAI,CAACgnB,eAAe,CAAC5iB,QAAQ,CAAC,CAACrC,OAAO,CAACrB,EAAE,IAAI;MACjD,MAAMiY,OAAO,GAAGjY,EAAE,CAACiY,OAAO,CAACyP,WAAW,CAAC,CAAC;MACxC,IAAI,CAACD,eAAe,CAACrrB,QAAQ,CAAC6b,OAAO,CAAC,EAAE;QACtCpc,IAAI,CAAC,yBAAyBoc,OAAO,GAAG,CAAC;MAC3C;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAM+O,yBAAyB,GAAGA,CAAChnB,EAAE,EAAE2nB,iBAAiB,KAAK;IAC3DtoB,KAAK,CAACC,IAAI,CAACU,EAAE,CAACsM,UAAU,CAAC,CAACjL,OAAO,CAACumB,SAAS,IAAI;MAC7C,IAAID,iBAAiB,CAACrY,OAAO,CAACsY,SAAS,CAACpb,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;QACpD3Q,IAAI,CAAC,CAAC,2BAA2B+rB,SAAS,CAACpb,IAAI,SAASxM,EAAE,CAACiY,OAAO,CAACyP,WAAW,CAAC,CAAC,IAAI,EAAE,GAAGC,iBAAiB,CAAC5uB,MAAM,GAAG,2BAA2B4uB,iBAAiB,CAAC3rB,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,gDAAgD,EAAE,CAAC,CAAC;MACtO;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAM6rB,kBAAkB,GAAG,EAAE;;EAE7B;AACF;AACA;AACA;AACA;EACE,MAAMC,SAAS,GAAG9lB,MAAM,IAAI;IAC1B,MAAM/E,SAAS,GAAGF,YAAY,CAAC,CAAC;IAChC,MAAMO,KAAK,GAAGD,QAAQ,CAAC,CAAC;IACxB,IAAI,OAAO2E,MAAM,CAACge,QAAQ,KAAK,UAAU,EAAE;MACzChe,MAAM,CAACge,QAAQ,CAAC1iB,KAAK,CAAC;IACxB;IACA3D,WAAW,CAACma,YAAY,CAACC,IAAI,CAAC,UAAU,EAAEzW,KAAK,CAAC;IAChD,MAAMyqB,UAAU,GAAGxtB,MAAM,CAACkL,gBAAgB,CAACzL,QAAQ,CAACC,IAAI,CAAC;IACzD,MAAM2e,mBAAmB,GAAGmP,UAAU,CAACC,SAAS;IAChDC,UAAU,CAAChrB,SAAS,EAAEK,KAAK,EAAE0E,MAAM,CAAC;;IAEpC;IACApH,UAAU,CAAC,MAAM;MACfstB,sBAAsB,CAACjrB,SAAS,EAAEK,KAAK,CAAC;IAC1C,CAAC,EAAEuqB,kBAAkB,CAAC;IACtB,IAAIznB,OAAO,CAAC,CAAC,EAAE;MACb+nB,kBAAkB,CAAClrB,SAAS,EAAE+E,MAAM,CAACme,gBAAgB,EAAEvH,mBAAmB,CAAC;MAC3E5B,aAAa,CAAC,CAAC;IACjB;IACA,IAAI,CAACzW,OAAO,CAAC,CAAC,IAAI,CAAC5G,WAAW,CAACE,qBAAqB,EAAE;MACpDF,WAAW,CAACE,qBAAqB,GAAGG,QAAQ,CAACwc,aAAa;IAC5D;IACA,IAAI,OAAOxU,MAAM,CAACie,OAAO,KAAK,UAAU,EAAE;MACxCrlB,UAAU,CAAC,MAAMoH,MAAM,CAACie,OAAO,CAAC3iB,KAAK,CAAC,CAAC;IACzC;IACA3D,WAAW,CAACma,YAAY,CAACC,IAAI,CAAC,SAAS,EAAEzW,KAAK,CAAC;IAC/CkG,WAAW,CAACvG,SAAS,EAAEjC,WAAW,CAAC,eAAe,CAAC,CAAC;EACtD,CAAC;;EAED;AACF;AACA;EACE,MAAMotB,yBAAyB,GAAGpW,KAAK,IAAI;IACzC,MAAM1U,KAAK,GAAGD,QAAQ,CAAC,CAAC;IACxB,IAAI2U,KAAK,CAAC7O,MAAM,KAAK7F,KAAK,EAAE;MAC1B;IACF;IACA,MAAML,SAAS,GAAGF,YAAY,CAAC,CAAC;IAChCO,KAAK,CAACyU,mBAAmB,CAAC,cAAc,EAAEqW,yBAAyB,CAAC;IACpE9qB,KAAK,CAACyU,mBAAmB,CAAC,eAAe,EAAEqW,yBAAyB,CAAC;IACrEnrB,SAAS,CAAC4G,KAAK,CAACmkB,SAAS,GAAG,MAAM;EACpC,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAME,sBAAsB,GAAGA,CAACjrB,SAAS,EAAEK,KAAK,KAAK;IACnD,IAAIkI,eAAe,CAAClI,KAAK,CAAC,EAAE;MAC1BL,SAAS,CAAC4G,KAAK,CAACmkB,SAAS,GAAG,QAAQ;MACpC1qB,KAAK,CAACmS,gBAAgB,CAAC,cAAc,EAAE2Y,yBAAyB,CAAC;MACjE9qB,KAAK,CAACmS,gBAAgB,CAAC,eAAe,EAAE2Y,yBAAyB,CAAC;IACpE,CAAC,MAAM;MACLnrB,SAAS,CAAC4G,KAAK,CAACmkB,SAAS,GAAG,MAAM;IACpC;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMG,kBAAkB,GAAGA,CAAClrB,SAAS,EAAEkjB,gBAAgB,EAAEvH,mBAAmB,KAAK;IAC/EvB,MAAM,CAAC,CAAC;IACR,IAAI8I,gBAAgB,IAAIvH,mBAAmB,KAAK,QAAQ,EAAE;MACxDD,2BAA2B,CAACC,mBAAmB,CAAC;IAClD;;IAEA;IACAhe,UAAU,CAAC,MAAM;MACfqC,SAAS,CAACua,SAAS,GAAG,CAAC;IACzB,CAAC,CAAC;EACJ,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMyQ,UAAU,GAAGA,CAAChrB,SAAS,EAAEK,KAAK,EAAE0E,MAAM,KAAK;IAC/CO,QAAQ,CAACtF,SAAS,EAAE+E,MAAM,CAACG,SAAS,CAAC2I,QAAQ,CAAC;IAC9C,IAAI9I,MAAM,CAACod,SAAS,EAAE;MACpB;MACA9hB,KAAK,CAACuG,KAAK,CAACC,WAAW,CAAC,SAAS,EAAE,GAAG,EAAE,WAAW,CAAC;MACpDE,IAAI,CAAC1G,KAAK,EAAE,MAAM,CAAC;MACnB1C,UAAU,CAAC,MAAM;QACf;QACA2H,QAAQ,CAACjF,KAAK,EAAE0E,MAAM,CAACG,SAAS,CAAC7E,KAAK,CAAC;QACvC;QACAA,KAAK,CAACuG,KAAK,CAACE,cAAc,CAAC,SAAS,CAAC;MACvC,CAAC,EAAE8jB,kBAAkB,CAAC,CAAC,CAAC;IAC1B,CAAC,MAAM;MACL7jB,IAAI,CAAC1G,KAAK,EAAE,MAAM,CAAC;IACrB;IACAiF,QAAQ,CAAC,CAACvI,QAAQ,CAACiN,eAAe,EAAEjN,QAAQ,CAACC,IAAI,CAAC,EAAEe,WAAW,CAACsF,KAAK,CAAC;IACtE,IAAI0B,MAAM,CAACqd,UAAU,IAAIrd,MAAM,CAAC8I,QAAQ,IAAI,CAAC9I,MAAM,CAACxB,KAAK,EAAE;MACzD+B,QAAQ,CAAC,CAACvI,QAAQ,CAACiN,eAAe,EAAEjN,QAAQ,CAACC,IAAI,CAAC,EAAEe,WAAW,CAAC,aAAa,CAAC,CAAC;IACjF;EACF,CAAC;EAED,IAAIqtB,sBAAsB,GAAG;IAC3B;AACJ;AACA;AACA;AACA;IACI5a,KAAK,EAAEA,CAAC6a,MAAM,EAAEvK,iBAAiB,KAAK;MACpC,OAAO,mDAAmD,CAACwK,IAAI,CAACD,MAAM,CAAC,GAAGluB,OAAO,CAACC,OAAO,CAAC,CAAC,GAAGD,OAAO,CAACC,OAAO,CAAC0jB,iBAAiB,IAAI,uBAAuB,CAAC;IAC7J,CAAC;IACD;AACJ;AACA;AACA;AACA;IACIlQ,GAAG,EAAEA,CAACya,MAAM,EAAEvK,iBAAiB,KAAK;MAClC;MACA,OAAO,6FAA6F,CAACwK,IAAI,CAACD,MAAM,CAAC,GAAGluB,OAAO,CAACC,OAAO,CAAC,CAAC,GAAGD,OAAO,CAACC,OAAO,CAAC0jB,iBAAiB,IAAI,aAAa,CAAC;IAC7L;EACF,CAAC;;EAED;AACF;AACA;EACE,SAASyK,yBAAyBA,CAACxmB,MAAM,EAAE;IACzC;IACA,IAAIA,MAAM,CAAC0b,cAAc,EAAE;MACzB;IACF;IACA,IAAI1b,MAAM,CAACa,KAAK,KAAK,OAAO,EAAE;MAC5Bb,MAAM,CAAC0b,cAAc,GAAG2K,sBAAsB,CAAC,OAAO,CAAC;IACzD;IACA,IAAIrmB,MAAM,CAACa,KAAK,KAAK,KAAK,EAAE;MAC1Bb,MAAM,CAAC0b,cAAc,GAAG2K,sBAAsB,CAAC,KAAK,CAAC;IACvD;EACF;;EAEA;AACF;AACA;EACE,SAASI,2BAA2BA,CAACzmB,MAAM,EAAE;IAC3C;IACA,IAAI,CAACA,MAAM,CAACmB,MAAM,IAAI,OAAOnB,MAAM,CAACmB,MAAM,KAAK,QAAQ,IAAI,CAACnJ,QAAQ,CAACgD,aAAa,CAACgF,MAAM,CAACmB,MAAM,CAAC,IAAI,OAAOnB,MAAM,CAACmB,MAAM,KAAK,QAAQ,IAAI,CAACnB,MAAM,CAACmB,MAAM,CAAC5B,WAAW,EAAE;MACpK1F,IAAI,CAAC,qDAAqD,CAAC;MAC3DmG,MAAM,CAACmB,MAAM,GAAG,MAAM;IACxB;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASulB,aAAaA,CAAC1mB,MAAM,EAAE;IAC7BwmB,yBAAyB,CAACxmB,MAAM,CAAC;;IAEjC;IACA,IAAIA,MAAM,CAACyc,mBAAmB,IAAI,CAACzc,MAAM,CAAC0c,UAAU,EAAE;MACpD7iB,IAAI,CAAC,sEAAsE,GAAG,mFAAmF,GAAG,6CAA6C,CAAC;IACpN;IACA4sB,2BAA2B,CAACzmB,MAAM,CAAC;;IAEnC;IACA,IAAI,OAAOA,MAAM,CAACtE,KAAK,KAAK,QAAQ,EAAE;MACpCsE,MAAM,CAACtE,KAAK,GAAGsE,MAAM,CAACtE,KAAK,CAACkE,KAAK,CAAC,IAAI,CAAC,CAAC5F,IAAI,CAAC,QAAQ,CAAC;IACxD;IACAgM,IAAI,CAAChG,MAAM,CAAC;EACd;;EAEA;EACA,IAAImF,eAAe;EACnB,IAAIwhB,QAAQ,GAAG,aAAa,IAAIpd,OAAO,CAAC,CAAC;EACzC,MAAMqd,UAAU,CAAC;IACf;AACJ;AACA;AACA;IACI/D,WAAWA,CAAC,GAAG3B,IAAI,EAAE;MACnB;AACN;AACA;MACM5pB,0BAA0B,CAAC,IAAI,EAAEqvB,QAAQ,EAAE,KAAK,CAAC,CAAC;MAClD;MACA,IAAI,OAAOpuB,MAAM,KAAK,WAAW,EAAE;QACjC;MACF;MACA4M,eAAe,GAAG,IAAI;;MAEtB;MACA,MAAM0hB,WAAW,GAAG5mB,MAAM,CAACwS,MAAM,CAAC,IAAI,CAACoQ,WAAW,CAAC5B,YAAY,CAACC,IAAI,CAAC,CAAC;;MAEtE;MACA,IAAI,CAAClhB,MAAM,GAAG6mB,WAAW;;MAEzB;MACA,IAAI,CAACrP,iBAAiB,GAAG,KAAK;MAC9BhgB,sBAAsB,CAACmvB,QAAQ,EAAE,IAAI,EAAE,IAAI,CAACpF,KAAK,CAACpc,eAAe,CAACnF,MAAM,CAAC,CAAC;IAC5E;IACAuhB,KAAKA,CAACuF,UAAU,EAAEzF,WAAW,GAAG,CAAC,CAAC,EAAE;MAClCpC,qBAAqB,CAAChf,MAAM,CAACgY,MAAM,CAAC,CAAC,CAAC,EAAEoJ,WAAW,EAAEyF,UAAU,CAAC,CAAC;MACjE,IAAInvB,WAAW,CAACwN,eAAe,EAAE;QAC/B,MAAM2P,kBAAkB,GAAGD,cAAc,CAACC,kBAAkB,CAACzd,GAAG,CAACM,WAAW,CAACwN,eAAe,CAAC;QAC7F,MAAM;UACJqS;QACF,CAAC,GAAG7f,WAAW,CAACwN,eAAe;QAC/BxN,WAAW,CAACwN,eAAe,CAAC2S,QAAQ,CAAC,CAAC;QACtC,IAAI,CAACN,iBAAiB,EAAE;UACtB1C,kBAAkB,CAAC;YACjB2C,WAAW,EAAE;UACf,CAAC,CAAC;QACJ;QACA,IAAIrZ,OAAO,CAAC,CAAC,EAAE;UACb8W,eAAe,CAAC,CAAC;QACnB;MACF;MACAvd,WAAW,CAACwN,eAAe,GAAGA,eAAe;MAC7C,MAAMmE,WAAW,GAAGyd,aAAa,CAACD,UAAU,EAAEzF,WAAW,CAAC;MAC1DqF,aAAa,CAACpd,WAAW,CAAC;MAC1BrJ,MAAM,CAACwS,MAAM,CAACnJ,WAAW,CAAC;;MAE1B;MACA,IAAI3R,WAAW,CAAC+pB,OAAO,EAAE;QACvB/pB,WAAW,CAAC+pB,OAAO,CAACE,IAAI,CAAC,CAAC;QAC1B,OAAOjqB,WAAW,CAAC+pB,OAAO;MAC5B;;MAEA;MACAwC,YAAY,CAACvsB,WAAW,CAACgB,mBAAmB,CAAC;MAC7C,MAAM6Q,QAAQ,GAAGwd,gBAAgB,CAAC7hB,eAAe,CAAC;MAClDyM,MAAM,CAACzM,eAAe,EAAEmE,WAAW,CAAC;MACpCD,YAAY,CAACC,WAAW,CAAC/R,GAAG,CAAC4N,eAAe,EAAEmE,WAAW,CAAC;MAC1D,OAAO2d,WAAW,CAAC9hB,eAAe,EAAEqE,QAAQ,EAAEF,WAAW,CAAC;IAC5D;;IAEA;IACA6N,IAAIA,CAAC+P,WAAW,EAAE;MAChB,OAAOhwB,sBAAsB,CAACyvB,QAAQ,EAAE,IAAI,CAAC,CAACxP,IAAI,CAAC+P,WAAW,CAAC;IACjE;IACAC,OAAOA,CAACC,SAAS,EAAE;MACjB,OAAOlwB,sBAAsB,CAACyvB,QAAQ,EAAE,IAAI,CAAC,CAACQ,OAAO,CAACC,SAAS,CAAC;IAClE;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAMH,WAAW,GAAGA,CAACngB,QAAQ,EAAE0C,QAAQ,EAAEF,WAAW,KAAK;IACvD,OAAO,IAAIlR,OAAO,CAAC,CAACC,OAAO,EAAEgvB,MAAM,KAAK;MACtC;MACA;AACN;AACA;MACM,MAAMnU,WAAW,GAAGoU,OAAO,IAAI;QAC7BxgB,QAAQ,CAAC/J,KAAK,CAAC;UACb0a,WAAW,EAAE,IAAI;UACjB6P;QACF,CAAC,CAAC;MACJ,CAAC;MACDzS,cAAc,CAACC,kBAAkB,CAACvd,GAAG,CAACuP,QAAQ,EAAEzO,OAAO,CAAC;MACxDwc,cAAc,CAACE,iBAAiB,CAACxd,GAAG,CAACuP,QAAQ,EAAEugB,MAAM,CAAC;MACtD7d,QAAQ,CAACpC,aAAa,CAACqZ,OAAO,GAAG,MAAM;QACrCrF,wBAAwB,CAACtU,QAAQ,CAAC;MACpC,CAAC;MACD0C,QAAQ,CAACnC,UAAU,CAACoZ,OAAO,GAAG,MAAM;QAClClF,qBAAqB,CAACzU,QAAQ,CAAC;MACjC,CAAC;MACD0C,QAAQ,CAAClC,YAAY,CAACmZ,OAAO,GAAG,MAAM;QACpChF,uBAAuB,CAAC3U,QAAQ,EAAEoM,WAAW,CAAC;MAChD,CAAC;MACD1J,QAAQ,CAAChB,WAAW,CAACiY,OAAO,GAAG,MAAM;QACnCvN,WAAW,CAACV,aAAa,CAACzV,KAAK,CAAC;MAClC,CAAC;MACDqjB,gBAAgB,CAAC9W,WAAW,EAAEE,QAAQ,EAAE0J,WAAW,CAAC;MACpDD,iBAAiB,CAACtb,WAAW,EAAE2R,WAAW,EAAE4J,WAAW,CAAC;MACxD+F,0BAA0B,CAACnS,QAAQ,EAAEwC,WAAW,CAAC;MACjDwc,SAAS,CAACxc,WAAW,CAAC;MACtBie,UAAU,CAAC5vB,WAAW,EAAE2R,WAAW,EAAE4J,WAAW,CAAC;MACjDsU,SAAS,CAAChe,QAAQ,EAAEF,WAAW,CAAC;;MAEhC;MACA1Q,UAAU,CAAC,MAAM;QACf4Q,QAAQ,CAACvO,SAAS,CAACua,SAAS,GAAG,CAAC;MAClC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMuR,aAAa,GAAGA,CAACD,UAAU,EAAEzF,WAAW,KAAK;IACjD,MAAMoG,cAAc,GAAGpD,iBAAiB,CAACyC,UAAU,CAAC;IACpD,MAAM9mB,MAAM,GAAGC,MAAM,CAACgY,MAAM,CAAC,CAAC,CAAC,EAAEiF,aAAa,EAAEmE,WAAW,EAAEoG,cAAc,EAAEX,UAAU,CAAC,CAAC,CAAC;IAC1F9mB,MAAM,CAACG,SAAS,GAAGF,MAAM,CAACgY,MAAM,CAAC,CAAC,CAAC,EAAEiF,aAAa,CAAC/c,SAAS,EAAEH,MAAM,CAACG,SAAS,CAAC;IAC/EH,MAAM,CAAC2X,SAAS,GAAG1X,MAAM,CAACgY,MAAM,CAAC,CAAC,CAAC,EAAEiF,aAAa,CAACvF,SAAS,EAAE3X,MAAM,CAAC2X,SAAS,CAAC;IAC/E,IAAI3X,MAAM,CAACod,SAAS,KAAK,KAAK,EAAE;MAC9Bpd,MAAM,CAACG,SAAS,GAAG;QACjB2I,QAAQ,EAAE;MACZ,CAAC;MACD9I,MAAM,CAAC2X,SAAS,GAAG,CAAC,CAAC;IACvB;IACA,OAAO3X,MAAM;EACf,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMgnB,gBAAgB,GAAGlgB,QAAQ,IAAI;IACnC,MAAM0C,QAAQ,GAAG;MACflO,KAAK,EAAED,QAAQ,CAAC,CAAC;MACjBJ,SAAS,EAAEF,YAAY,CAAC,CAAC;MACzBkB,OAAO,EAAES,UAAU,CAAC,CAAC;MACrB0K,aAAa,EAAEpL,gBAAgB,CAAC,CAAC;MACjCqL,UAAU,EAAEhL,aAAa,CAAC,CAAC;MAC3BiL,YAAY,EAAEnL,eAAe,CAAC,CAAC;MAC/BM,MAAM,EAAED,SAAS,CAAC,CAAC;MACnBgM,WAAW,EAAE1L,cAAc,CAAC,CAAC;MAC7Bif,iBAAiB,EAAEhgB,oBAAoB,CAAC,CAAC;MACzCiV,aAAa,EAAElV,gBAAgB,CAAC;IAClC,CAAC;IACDuN,YAAY,CAACG,QAAQ,CAACjS,GAAG,CAACuP,QAAQ,EAAE0C,QAAQ,CAAC;IAC7C,OAAOA,QAAQ;EACjB,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAM+d,UAAU,GAAGA,CAAC5vB,WAAW,EAAE2R,WAAW,EAAE4J,WAAW,KAAK;IAC5D,MAAMjP,gBAAgB,GAAGpH,mBAAmB,CAAC,CAAC;IAC9CqF,IAAI,CAAC+B,gBAAgB,CAAC;IACtB,IAAIqF,WAAW,CAACvF,KAAK,EAAE;MACrBpM,WAAW,CAAC+pB,OAAO,GAAG,IAAImC,KAAK,CAAC,MAAM;QACpC3Q,WAAW,CAAC,OAAO,CAAC;QACpB,OAAOvb,WAAW,CAAC+pB,OAAO;MAC5B,CAAC,EAAEpY,WAAW,CAACvF,KAAK,CAAC;MACrB,IAAIuF,WAAW,CAACrF,gBAAgB,EAAE;QAChCjC,IAAI,CAACiC,gBAAgB,CAAC;QACtB5D,gBAAgB,CAAC4D,gBAAgB,EAAEqF,WAAW,EAAE,kBAAkB,CAAC;QACnE1Q,UAAU,CAAC,MAAM;UACf,IAAIjB,WAAW,CAAC+pB,OAAO,IAAI/pB,WAAW,CAAC+pB,OAAO,CAACO,OAAO,EAAE;YACtD;YACAne,uBAAuB,CAACwF,WAAW,CAACvF,KAAK,CAAC;UAC5C;QACF,CAAC,CAAC;MACJ;IACF;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMyjB,SAAS,GAAGA,CAAChe,QAAQ,EAAEF,WAAW,KAAK;IAC3C,IAAIA,WAAW,CAAC9K,KAAK,EAAE;MACrB;IACF;IACA;IACA,IAAI,CAAC/D,cAAc,CAAC6O,WAAW,CAAC4K,aAAa,CAAC,EAAE;MAC9C5Z,oBAAoB,CAAC,eAAe,CAAC;MACrCotB,iBAAiB,CAAC,CAAC;MACnB;IACF;IACA,IAAIC,cAAc,CAACne,QAAQ,CAAC,EAAE;MAC5B;IACF;IACA,IAAIoe,WAAW,CAACpe,QAAQ,EAAEF,WAAW,CAAC,EAAE;MACtC;IACF;IACA6J,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACjB,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMwU,cAAc,GAAGne,QAAQ,IAAI;IACjC,MAAMqe,iBAAiB,GAAGxqB,KAAK,CAACC,IAAI,CAACkM,QAAQ,CAAClO,KAAK,CAAC6B,gBAAgB,CAAC,aAAa,CAAC,CAAC;IACpF,KAAK,MAAM2qB,gBAAgB,IAAID,iBAAiB,EAAE;MAChD,IAAIC,gBAAgB,YAAYhwB,WAAW,IAAIqG,WAAW,CAAC2pB,gBAAgB,CAAC,EAAE;QAC5EA,gBAAgB,CAAC/vB,KAAK,CAAC,CAAC;QACxB,OAAO,IAAI;MACb;IACF;IACA,OAAO,KAAK;EACd,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAM6vB,WAAW,GAAGA,CAACpe,QAAQ,EAAEF,WAAW,KAAK;IAC7C,IAAIA,WAAW,CAACwU,SAAS,IAAI3f,WAAW,CAACqL,QAAQ,CAACnC,UAAU,CAAC,EAAE;MAC7DmC,QAAQ,CAACnC,UAAU,CAACtP,KAAK,CAAC,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,IAAIuR,WAAW,CAACyU,WAAW,IAAI5f,WAAW,CAACqL,QAAQ,CAAClC,YAAY,CAAC,EAAE;MACjEkC,QAAQ,CAAClC,YAAY,CAACvP,KAAK,CAAC,CAAC;MAC7B,OAAO,IAAI;IACb;IACA,IAAIuR,WAAW,CAACuU,YAAY,IAAI1f,WAAW,CAACqL,QAAQ,CAACpC,aAAa,CAAC,EAAE;MACnEoC,QAAQ,CAACpC,aAAa,CAACrP,KAAK,CAAC,CAAC;MAC9B,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC;EACD,MAAM2vB,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI1vB,QAAQ,CAACwc,aAAa,YAAY1c,WAAW,IAAI,OAAOE,QAAQ,CAACwc,aAAa,CAACuT,IAAI,KAAK,UAAU,EAAE;MACtG/vB,QAAQ,CAACwc,aAAa,CAACuT,IAAI,CAAC,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,IAAI,OAAOxvB,MAAM,KAAK,WAAW,IAAI,OAAO,CAACguB,IAAI,CAACyB,SAAS,CAACC,QAAQ,CAAC,IAAIC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAAC,wBAAwB,CAAC,EAAE;IACtH,MAAMC,GAAG,GAAG,IAAIpE,IAAI,CAAC,CAAC;IACtB,MAAMqE,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC;IAC9D,IAAI,CAACF,cAAc,EAAE;MACnBC,YAAY,CAACE,OAAO,CAAC,iBAAiB,EAAE,GAAGJ,GAAG,EAAE,CAAC;IACnD,CAAC,MAAM,IAAI,CAACA,GAAG,CAAClE,OAAO,CAAC,CAAC,GAAGF,IAAI,CAACiB,KAAK,CAACoD,cAAc,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE;MACnF1vB,UAAU,CAAC,MAAM;QACfZ,QAAQ,CAACC,IAAI,CAAC4J,KAAK,CAAC6mB,aAAa,GAAG,MAAM;QAC1C,MAAMC,eAAe,GAAG3wB,QAAQ,CAACkO,aAAa,CAAC,OAAO,CAAC;QACvDyiB,eAAe,CAACC,GAAG,GAAG,6DAA6D;QACnFD,eAAe,CAACE,IAAI,GAAG,IAAI;QAC3B7wB,QAAQ,CAACC,IAAI,CAACsH,WAAW,CAACopB,eAAe,CAAC;QAC1C/vB,UAAU,CAAC,MAAM;UACf+vB,eAAe,CAACG,IAAI,CAAC,CAAC,CAAC5O,KAAK,CAAC,MAAM;YACjC;UAAA,CACD,CAAC;QACJ,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,EAAE,GAAG,CAAC;IACT;EACF;;EAEA;EACA0M,UAAU,CAACnI,SAAS,CAACpD,cAAc,GAAGA,cAAc;EACpDuL,UAAU,CAACnI,SAAS,CAAC5C,aAAa,GAAGA,aAAa;EAClD+K,UAAU,CAACnI,SAAS,CAACnF,QAAQ,GAAGA,QAAQ;EACxCsN,UAAU,CAACnI,SAAS,CAACzC,YAAY,GAAGA,YAAY;EAChD4K,UAAU,CAACnI,SAAS,CAACvC,WAAW,GAAGA,WAAW;EAC9C0K,UAAU,CAACnI,SAAS,CAACxE,WAAW,GAAGA,WAAW;EAC9C2M,UAAU,CAACnI,SAAS,CAACmB,cAAc,GAAG3F,WAAW;EACjD2M,UAAU,CAACnI,SAAS,CAAC3C,qBAAqB,GAAGA,qBAAqB;EAClE8K,UAAU,CAACnI,SAAS,CAACrZ,sBAAsB,GAAGA,sBAAsB;EACpEwhB,UAAU,CAACnI,SAAS,CAAC1hB,KAAK,GAAGA,KAAK;EAClC6pB,UAAU,CAACnI,SAAS,CAACoB,UAAU,GAAG9iB,KAAK;EACvC6pB,UAAU,CAACnI,SAAS,CAACqB,UAAU,GAAG/iB,KAAK;EACvC6pB,UAAU,CAACnI,SAAS,CAACsB,UAAU,GAAGhjB,KAAK;EACvC6pB,UAAU,CAACnI,SAAS,CAAC5G,aAAa,GAAGA,aAAa;EAClD+O,UAAU,CAACnI,SAAS,CAACS,MAAM,GAAGA,MAAM;EACpC0H,UAAU,CAACnI,SAAS,CAAC3G,QAAQ,GAAGA,QAAQ;;EAExC;EACA7X,MAAM,CAACgY,MAAM,CAAC2O,UAAU,EAAEjD,aAAa,CAAC;;EAExC;EACA1jB,MAAM,CAACiK,IAAI,CAACgW,eAAe,CAAC,CAAC7gB,OAAO,CAACwU,GAAG,IAAI;IAC1C;AACJ;AACA;AACA;IACI+S,UAAU,CAAC/S,GAAG,CAAC,GAAG,UAAU,GAAGqN,IAAI,EAAE;MACnC,IAAI/b,eAAe,IAAIA,eAAe,CAAC0O,GAAG,CAAC,EAAE;QAC3C,OAAO1O,eAAe,CAAC0O,GAAG,CAAC,CAAC,GAAGqN,IAAI,CAAC;MACtC;MACA,OAAO,IAAI;IACb,CAAC;EACH,CAAC,CAAC;EACF0F,UAAU,CAACpU,aAAa,GAAGA,aAAa;EACxCoU,UAAU,CAACmC,OAAO,GAAG,SAAS;EAE9B,MAAMjQ,IAAI,GAAG8N,UAAU;EACvB;EACA9N,IAAI,CAACkQ,OAAO,GAAGlQ,IAAI;EAEnB,OAAOA,IAAI;AAEb,CAAE,CAAC;AACH,IAAI,OAAO,IAAI,KAAK,WAAW,IAAI,IAAI,CAACtiB,WAAW,EAAC;EAAC,IAAI,CAACyyB,IAAI,GAAG,IAAI,CAACC,UAAU,GAAG,IAAI,CAACpQ,IAAI,GAAG,IAAI,CAAC8N,UAAU,GAAG,IAAI,CAACpwB,WAAW;AAAA;AACjI,WAAW,IAAE,OAAOwB,QAAQ,IAAE,UAAStB,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACF,CAAC,CAACwP,aAAa,CAAC,OAAO,CAAC;EAAC,IAAGxP,CAAC,CAACyyB,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC5pB,WAAW,CAAC3I,CAAC,CAAC,EAACA,CAAC,CAACwyB,UAAU,EAACxyB,CAAC,CAACwyB,UAAU,CAAChd,QAAQ,KAAGxV,CAAC,CAACwyB,UAAU,CAACC,OAAO,GAAC1yB,CAAC,CAAC,CAAC,KAAK,IAAG;IAACC,CAAC,CAAC0L,SAAS,GAAC3L,CAAC;EAAA,CAAC,QAAMD,CAAC,EAAC;IAACE,CAAC,CAACuU,SAAS,GAACxU,CAAC;EAAA;AAAC,CAAC,CAACqB,QAAQ,EAAC,or7BAAor7B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}