{"ast": null, "code": "'use strict';\n\nimport { VERSION } from '../env/data.js';\nimport AxiosError from '../core/AxiosError.js';\nconst validators = {};\n\n// eslint-disable-next-line func-names\n['object', 'boolean', 'number', 'function', 'string', 'symbol'].forEach((type, i) => {\n  validators[type] = function validator(thing) {\n    return typeof thing === type || 'a' + (i < 1 ? 'n ' : ' ') + type;\n  };\n});\nconst deprecatedWarnings = {};\n\n/**\n * Transitional option validator\n *\n * @param {function|boolean?} validator - set to false if the transitional option has been removed\n * @param {string?} version - deprecated version / removed since version\n * @param {string?} message - some message with additional info\n *\n * @returns {function}\n */\nvalidators.transitional = function transitional(validator, version, message) {\n  function formatMessage(opt, desc) {\n    return '[Axios v' + VERSION + '] Transitional option \\'' + opt + '\\'' + desc + (message ? '. ' + message : '');\n  }\n\n  // eslint-disable-next-line func-names\n  return (value, opt, opts) => {\n    if (validator === false) {\n      throw new AxiosError(formatMessage(opt, ' has been removed' + (version ? ' in ' + version : '')), AxiosError.ERR_DEPRECATED);\n    }\n    if (version && !deprecatedWarnings[opt]) {\n      deprecatedWarnings[opt] = true;\n      // eslint-disable-next-line no-console\n      console.warn(formatMessage(opt, ' has been deprecated since v' + version + ' and will be removed in the near future'));\n    }\n    return validator ? validator(value, opt, opts) : true;\n  };\n};\nvalidators.spelling = function spelling(correctSpelling) {\n  return (value, opt) => {\n    // eslint-disable-next-line no-console\n    console.warn(`${opt} is likely a misspelling of ${correctSpelling}`);\n    return true;\n  };\n};\n\n/**\n * Assert object's properties type\n *\n * @param {object} options\n * @param {object} schema\n * @param {boolean?} allowUnknown\n *\n * @returns {object}\n */\n\nfunction assertOptions(options, schema, allowUnknown) {\n  if (typeof options !== 'object') {\n    throw new AxiosError('options must be an object', AxiosError.ERR_BAD_OPTION_VALUE);\n  }\n  const keys = Object.keys(options);\n  let i = keys.length;\n  while (i-- > 0) {\n    const opt = keys[i];\n    const validator = schema[opt];\n    if (validator) {\n      const value = options[opt];\n      const result = value === undefined || validator(value, opt, options);\n      if (result !== true) {\n        throw new AxiosError('option ' + opt + ' must be ' + result, AxiosError.ERR_BAD_OPTION_VALUE);\n      }\n      continue;\n    }\n    if (allowUnknown !== true) {\n      throw new AxiosError('Unknown option ' + opt, AxiosError.ERR_BAD_OPTION);\n    }\n  }\n}\nexport default {\n  assertOptions,\n  validators\n};", "map": {"version": 3, "names": ["VERSION", "AxiosError", "validators", "for<PERSON>ach", "type", "i", "validator", "thing", "deprecatedWarnings", "transitional", "version", "message", "formatMessage", "opt", "desc", "value", "opts", "ERR_DEPRECATED", "console", "warn", "spelling", "correctSpelling", "assertOptions", "options", "schema", "allowUnknown", "ERR_BAD_OPTION_VALUE", "keys", "Object", "length", "result", "undefined", "ERR_BAD_OPTION"], "sources": ["C:/Users/<USER>/Documents/augment-projects/coop/frontend/node_modules/axios/lib/helpers/validator.js"], "sourcesContent": ["'use strict';\n\nimport {VERSION} from '../env/data.js';\nimport AxiosError from '../core/AxiosError.js';\n\nconst validators = {};\n\n// eslint-disable-next-line func-names\n['object', 'boolean', 'number', 'function', 'string', 'symbol'].forEach((type, i) => {\n  validators[type] = function validator(thing) {\n    return typeof thing === type || 'a' + (i < 1 ? 'n ' : ' ') + type;\n  };\n});\n\nconst deprecatedWarnings = {};\n\n/**\n * Transitional option validator\n *\n * @param {function|boolean?} validator - set to false if the transitional option has been removed\n * @param {string?} version - deprecated version / removed since version\n * @param {string?} message - some message with additional info\n *\n * @returns {function}\n */\nvalidators.transitional = function transitional(validator, version, message) {\n  function formatMessage(opt, desc) {\n    return '[Axios v' + VERSION + '] Transitional option \\'' + opt + '\\'' + desc + (message ? '. ' + message : '');\n  }\n\n  // eslint-disable-next-line func-names\n  return (value, opt, opts) => {\n    if (validator === false) {\n      throw new AxiosError(\n        formatMessage(opt, ' has been removed' + (version ? ' in ' + version : '')),\n        AxiosError.ERR_DEPRECATED\n      );\n    }\n\n    if (version && !deprecatedWarnings[opt]) {\n      deprecatedWarnings[opt] = true;\n      // eslint-disable-next-line no-console\n      console.warn(\n        formatMessage(\n          opt,\n          ' has been deprecated since v' + version + ' and will be removed in the near future'\n        )\n      );\n    }\n\n    return validator ? validator(value, opt, opts) : true;\n  };\n};\n\nvalidators.spelling = function spelling(correctSpelling) {\n  return (value, opt) => {\n    // eslint-disable-next-line no-console\n    console.warn(`${opt} is likely a misspelling of ${correctSpelling}`);\n    return true;\n  }\n};\n\n/**\n * Assert object's properties type\n *\n * @param {object} options\n * @param {object} schema\n * @param {boolean?} allowUnknown\n *\n * @returns {object}\n */\n\nfunction assertOptions(options, schema, allowUnknown) {\n  if (typeof options !== 'object') {\n    throw new AxiosError('options must be an object', AxiosError.ERR_BAD_OPTION_VALUE);\n  }\n  const keys = Object.keys(options);\n  let i = keys.length;\n  while (i-- > 0) {\n    const opt = keys[i];\n    const validator = schema[opt];\n    if (validator) {\n      const value = options[opt];\n      const result = value === undefined || validator(value, opt, options);\n      if (result !== true) {\n        throw new AxiosError('option ' + opt + ' must be ' + result, AxiosError.ERR_BAD_OPTION_VALUE);\n      }\n      continue;\n    }\n    if (allowUnknown !== true) {\n      throw new AxiosError('Unknown option ' + opt, AxiosError.ERR_BAD_OPTION);\n    }\n  }\n}\n\nexport default {\n  assertOptions,\n  validators\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,SAAQA,OAAO,QAAO,gBAAgB;AACtC,OAAOC,UAAU,MAAM,uBAAuB;AAE9C,MAAMC,UAAU,GAAG,CAAC,CAAC;;AAErB;AACA,CAAC,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAACC,OAAO,CAAC,CAACC,IAAI,EAAEC,CAAC,KAAK;EACnFH,UAAU,CAACE,IAAI,CAAC,GAAG,SAASE,SAASA,CAACC,KAAK,EAAE;IAC3C,OAAO,OAAOA,KAAK,KAAKH,IAAI,IAAI,GAAG,IAAIC,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC,GAAGD,IAAI;EACnE,CAAC;AACH,CAAC,CAAC;AAEF,MAAMI,kBAAkB,GAAG,CAAC,CAAC;;AAE7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAN,UAAU,CAACO,YAAY,GAAG,SAASA,YAAYA,CAACH,SAAS,EAAEI,OAAO,EAAEC,OAAO,EAAE;EAC3E,SAASC,aAAaA,CAACC,GAAG,EAAEC,IAAI,EAAE;IAChC,OAAO,UAAU,GAAGd,OAAO,GAAG,0BAA0B,GAAGa,GAAG,GAAG,IAAI,GAAGC,IAAI,IAAIH,OAAO,GAAG,IAAI,GAAGA,OAAO,GAAG,EAAE,CAAC;EAChH;;EAEA;EACA,OAAO,CAACI,KAAK,EAAEF,GAAG,EAAEG,IAAI,KAAK;IAC3B,IAAIV,SAAS,KAAK,KAAK,EAAE;MACvB,MAAM,IAAIL,UAAU,CAClBW,aAAa,CAACC,GAAG,EAAE,mBAAmB,IAAIH,OAAO,GAAG,MAAM,GAAGA,OAAO,GAAG,EAAE,CAAC,CAAC,EAC3ET,UAAU,CAACgB,cACb,CAAC;IACH;IAEA,IAAIP,OAAO,IAAI,CAACF,kBAAkB,CAACK,GAAG,CAAC,EAAE;MACvCL,kBAAkB,CAACK,GAAG,CAAC,GAAG,IAAI;MAC9B;MACAK,OAAO,CAACC,IAAI,CACVP,aAAa,CACXC,GAAG,EACH,8BAA8B,GAAGH,OAAO,GAAG,yCAC7C,CACF,CAAC;IACH;IAEA,OAAOJ,SAAS,GAAGA,SAAS,CAACS,KAAK,EAAEF,GAAG,EAAEG,IAAI,CAAC,GAAG,IAAI;EACvD,CAAC;AACH,CAAC;AAEDd,UAAU,CAACkB,QAAQ,GAAG,SAASA,QAAQA,CAACC,eAAe,EAAE;EACvD,OAAO,CAACN,KAAK,EAAEF,GAAG,KAAK;IACrB;IACAK,OAAO,CAACC,IAAI,CAAC,GAAGN,GAAG,+BAA+BQ,eAAe,EAAE,CAAC;IACpE,OAAO,IAAI;EACb,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,aAAaA,CAACC,OAAO,EAAEC,MAAM,EAAEC,YAAY,EAAE;EACpD,IAAI,OAAOF,OAAO,KAAK,QAAQ,EAAE;IAC/B,MAAM,IAAItB,UAAU,CAAC,2BAA2B,EAAEA,UAAU,CAACyB,oBAAoB,CAAC;EACpF;EACA,MAAMC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACJ,OAAO,CAAC;EACjC,IAAIlB,CAAC,GAAGsB,IAAI,CAACE,MAAM;EACnB,OAAOxB,CAAC,EAAE,GAAG,CAAC,EAAE;IACd,MAAMQ,GAAG,GAAGc,IAAI,CAACtB,CAAC,CAAC;IACnB,MAAMC,SAAS,GAAGkB,MAAM,CAACX,GAAG,CAAC;IAC7B,IAAIP,SAAS,EAAE;MACb,MAAMS,KAAK,GAAGQ,OAAO,CAACV,GAAG,CAAC;MAC1B,MAAMiB,MAAM,GAAGf,KAAK,KAAKgB,SAAS,IAAIzB,SAAS,CAACS,KAAK,EAAEF,GAAG,EAAEU,OAAO,CAAC;MACpE,IAAIO,MAAM,KAAK,IAAI,EAAE;QACnB,MAAM,IAAI7B,UAAU,CAAC,SAAS,GAAGY,GAAG,GAAG,WAAW,GAAGiB,MAAM,EAAE7B,UAAU,CAACyB,oBAAoB,CAAC;MAC/F;MACA;IACF;IACA,IAAID,YAAY,KAAK,IAAI,EAAE;MACzB,MAAM,IAAIxB,UAAU,CAAC,iBAAiB,GAAGY,GAAG,EAAEZ,UAAU,CAAC+B,cAAc,CAAC;IAC1E;EACF;AACF;AAEA,eAAe;EACbV,aAAa;EACbpB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}