"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendValidationError = exports.sendForbidden = exports.sendUnauthorized = exports.sendNotFound = exports.sendCreated = exports.sendError = exports.sendSuccess = void 0;
const sendSuccess = (res, message, data, statusCode = 200) => {
    const response = {
        success: true,
        message,
        data
    };
    return res.status(statusCode).json(response);
};
exports.sendSuccess = sendSuccess;
const sendError = (res, message, statusCode = 400, error) => {
    const response = {
        success: false,
        message,
        error
    };
    return res.status(statusCode).json(response);
};
exports.sendError = sendError;
const sendCreated = (res, message, data) => {
    return (0, exports.sendSuccess)(res, message, data, 201);
};
exports.sendCreated = sendCreated;
const sendNotFound = (res, message = 'ไม่พบข้อมูลที่ต้องการ') => {
    return (0, exports.sendError)(res, message, 404);
};
exports.sendNotFound = sendNotFound;
const sendUnauthorized = (res, message = 'ไม่ได้รับการยืนยันตัวตน') => {
    return (0, exports.sendError)(res, message, 401);
};
exports.sendUnauthorized = sendUnauthorized;
const sendForbidden = (res, message = 'ไม่มีสิทธิ์เข้าถึง') => {
    return (0, exports.sendError)(res, message, 403);
};
exports.sendForbidden = sendForbidden;
const sendValidationError = (res, message = 'ข้อมูลไม่ถูกต้อง', errors) => {
    const response = {
        success: false,
        message,
        error: errors
    };
    return res.status(400).json(response);
};
exports.sendValidationError = sendValidationError;
//# sourceMappingURL=response.js.map