{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\coop\\\\frontend\\\\src\\\\pages\\\\ProductsPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { apiService } from '../services/api';\nimport SweetAlertUtils from '../utils/sweetAlert';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProductsPage = () => {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  useEffect(() => {\n    fetchProducts();\n  }, []);\n  const fetchProducts = async () => {\n    try {\n      setLoading(true);\n      const response = await apiService.getProducts();\n      if (response.success) {\n        const data = response.data;\n        setProducts(data.data);\n      } else {\n        SweetAlertUtils.error('เกิดข้อผิดพลาด', 'ไม่สามารถโหลดข้อมูลสินค้าได้');\n      }\n    } catch (error) {\n      console.error('Error fetching products:', error);\n      SweetAlertUtils.error('เกิดข้อผิดพลาด', 'ไม่สามารถโหลดข้อมูลสินค้าได้');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('th-TH', {\n      style: 'currency',\n      currency: 'THB'\n    }).format(amount);\n  };\n  const calculateProfitMargin = (costPrice, sellingPrice) => {\n    if (costPrice === 0) return 0;\n    return (sellingPrice - costPrice) / costPrice * 100;\n  };\n  const getStockStatus = (stock, minStock) => {\n    if (stock === 0) return {\n      class: 'danger',\n      text: 'หมด'\n    };\n    if (stock <= minStock) return {\n      class: 'warning',\n      text: 'ใกล้หมด'\n    };\n    return {\n      class: 'success',\n      text: 'เพียงพอ'\n    };\n  };\n  const filteredProducts = products.filter(product => product.name.toLowerCase().includes(searchTerm.toLowerCase()) || product.productCode.toLowerCase().includes(searchTerm.toLowerCase()) || product.category.toLowerCase().includes(searchTerm.toLowerCase()));\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"content-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container-fluid\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row mb-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-sm-6\",\n            children: /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"m-0\",\n              children: \"\\u0E08\\u0E31\\u0E14\\u0E01\\u0E32\\u0E23\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"content\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container-fluid\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner-border text-primary\",\n              role: \"status\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sr-only\",\n                children: \"\\u0E01\\u0E33\\u0E25\\u0E31\\u0E07\\u0E42\\u0E2B\\u0E25\\u0E14...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"content-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container-fluid\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row mb-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-sm-6\",\n            children: /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"m-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-box mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 17\n              }, this), \"\\u0E08\\u0E31\\u0E14\\u0E01\\u0E32\\u0E23\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-sm-6\",\n            children: /*#__PURE__*/_jsxDEV(\"ol\", {\n              className: \"breadcrumb float-sm-right\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"breadcrumb-item\",\n                children: /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/\",\n                  children: \"\\u0E2B\\u0E19\\u0E49\\u0E32\\u0E41\\u0E23\\u0E01\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 95,\n                  columnNumber: 49\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"breadcrumb-item active\",\n                children: \"\\u0E08\\u0E31\\u0E14\\u0E01\\u0E32\\u0E23\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container-fluid\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"card-title\",\n                  children: \"\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 110,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-tools\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"input-group input-group-sm\",\n                    style: {\n                      width: '250px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      className: \"form-control float-right\",\n                      placeholder: \"\\u0E04\\u0E49\\u0E19\\u0E2B\\u0E32\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32...\",\n                      value: searchTerm,\n                      onChange: e => setSearchTerm(e.target.value)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 113,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"input-group-append\",\n                      children: /*#__PURE__*/_jsxDEV(\"button\", {\n                        type: \"submit\",\n                        className: \"btn btn-default\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-search\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 122,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 121,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 120,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 112,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 111,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-body table-responsive p-0\",\n                children: [/*#__PURE__*/_jsxDEV(\"table\", {\n                  className: \"table table-hover text-nowrap\",\n                  children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                    children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 132,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 133,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"\\u0E2B\\u0E21\\u0E27\\u0E14\\u0E2B\\u0E21\\u0E39\\u0E48\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 134,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"\\u0E23\\u0E32\\u0E04\\u0E32\\u0E17\\u0E38\\u0E19\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 135,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"\\u0E23\\u0E32\\u0E04\\u0E32\\u0E02\\u0E32\\u0E22\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 136,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"\\u0E01\\u0E33\\u0E44\\u0E23\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 137,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"\\u0E2A\\u0E15\\u0E4A\\u0E2D\\u0E01\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 138,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"\\u0E2A\\u0E16\\u0E32\\u0E19\\u0E30\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 139,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"\\u0E08\\u0E31\\u0E14\\u0E01\\u0E32\\u0E23\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 140,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 131,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 130,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                    children: filteredProducts.map(product => {\n                      const stockStatus = getStockStatus(product.stock, product.minStock);\n                      const profitMargin = calculateProfitMargin(product.costPrice, product.sellingPrice);\n                      return /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                          children: /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"badge badge-primary\",\n                            children: product.productCode\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 151,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 150,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: product.name\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 154,\n                            columnNumber: 31\n                          }, this), product.description && /*#__PURE__*/_jsxDEV(_Fragment, {\n                            children: [/*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 157,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                              className: \"text-muted\",\n                              children: product.description\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 158,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 153,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"badge badge-secondary\",\n                            children: product.category\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 163,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 162,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: formatCurrency(product.costPrice)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 165,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: formatCurrency(product.sellingPrice)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 166,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-success\",\n                            children: formatCurrency(product.sellingPrice - product.costPrice)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 168,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(_Fragment, {\n                            children: [/*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 172,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                              className: \"text-muted\",\n                              children: [\"(\", profitMargin.toFixed(1), \"%)\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 173,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 167,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: product.stock\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 179,\n                            columnNumber: 31\n                          }, this), \" \", product.unit, /*#__PURE__*/_jsxDEV(_Fragment, {\n                            children: [/*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 181,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                              className: \"text-muted\",\n                              children: [\"\\u0E02\\u0E31\\u0E49\\u0E19\\u0E15\\u0E48\\u0E33: \", product.minStock, \" \", product.unit]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 182,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 178,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: `badge badge-${stockStatus.class}`,\n                            children: stockStatus.text\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 188,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(_Fragment, {\n                            children: [/*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 192,\n                              columnNumber: 33\n                            }, this), product.isActive ? /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"badge badge-success\",\n                              children: \"\\u0E43\\u0E0A\\u0E49\\u0E07\\u0E32\\u0E19\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 194,\n                              columnNumber: 35\n                            }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"badge badge-danger\",\n                              children: \"\\u0E23\\u0E30\\u0E07\\u0E31\\u0E1A\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 196,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 187,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"btn-group\",\n                            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                              className: \"btn btn-sm btn-info\",\n                              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                                className: \"fas fa-eye\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 203,\n                                columnNumber: 35\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 202,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                              className: \"btn btn-sm btn-warning\",\n                              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                                className: \"fas fa-edit\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 206,\n                                columnNumber: 35\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 205,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                              className: \"btn btn-sm btn-success\",\n                              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                                className: \"fas fa-plus\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 209,\n                                columnNumber: 35\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 208,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                              className: \"btn btn-sm btn-danger\",\n                              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                                className: \"fas fa-trash\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 212,\n                                columnNumber: 35\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 211,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 201,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 200,\n                          columnNumber: 29\n                        }, this)]\n                      }, product._id, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 149,\n                        columnNumber: 27\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 143,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 129,\n                  columnNumber: 19\n                }, this), filteredProducts.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center p-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-box fa-3x text-muted mb-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"text-muted\",\n                    children: \"\\u0E44\\u0E21\\u0E48\\u0E1E\\u0E1A\\u0E02\\u0E49\\u0E2D\\u0E21\\u0E39\\u0E25\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-muted\",\n                    children: searchTerm ? 'ไม่พบสินค้าที่ตรงกับคำค้นหา' : 'ยังไม่มีสินค้าในระบบ'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-footer\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"col-sm-6\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-muted\",\n                      children: [\"\\u0E41\\u0E2A\\u0E14\\u0E07 \", filteredProducts.length, \" \\u0E08\\u0E32\\u0E01 \", products.length, \" \\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 235,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 234,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"col-sm-6\",\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"btn btn-primary float-right\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-plus mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 241,\n                        columnNumber: 25\n                      }, this), \"\\u0E40\\u0E1E\\u0E34\\u0E48\\u0E21\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\\u0E43\\u0E2B\\u0E21\\u0E48\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 240,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 239,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(ProductsPage, \"x+1/PUpwgeCYBRqMDvNLJbxzKDY=\");\n_c = ProductsPage;\nexport default ProductsPage;\nvar _c;\n$RefreshReg$(_c, \"ProductsPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "apiService", "Sweet<PERSON>lertUtils", "Link", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProductsPage", "_s", "products", "setProducts", "loading", "setLoading", "searchTerm", "setSearchTerm", "fetchProducts", "response", "getProducts", "success", "data", "error", "console", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "calculateProfitMargin", "costPrice", "sellingPrice", "getStockStatus", "stock", "minStock", "class", "text", "filteredProducts", "filter", "product", "name", "toLowerCase", "includes", "productCode", "category", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "role", "to", "width", "type", "placeholder", "value", "onChange", "e", "target", "map", "stockStatus", "profitMargin", "description", "toFixed", "unit", "isActive", "_id", "length", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/coop/frontend/src/pages/ProductsPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Product, PaginatedResponse } from '../types';\nimport { apiService } from '../services/api';\nimport SweetAlertUtils from '../utils/sweetAlert';\nimport { Link } from 'react-router-dom';\n\nconst ProductsPage: React.FC = () => {\n  const [products, setProducts] = useState<Product[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n\n  useEffect(() => {\n    fetchProducts();\n  }, []);\n\n  const fetchProducts = async () => {\n    try {\n      setLoading(true);\n      const response = await apiService.getProducts();\n      if (response.success) {\n        const data = response.data as PaginatedResponse<Product>;\n        setProducts(data.data);\n      } else {\n        SweetAlertUtils.error('เกิดข้อผิดพลาด', 'ไม่สามารถโหลดข้อมูลสินค้าได้');\n      }\n    } catch (error) {\n      console.error('Error fetching products:', error);\n      SweetAlertUtils.error('เกิดข้อผิดพลาด', 'ไม่สามารถโหลดข้อมูลสินค้าได้');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('th-TH', {\n      style: 'currency',\n      currency: 'THB'\n    }).format(amount);\n  };\n\n  const calculateProfitMargin = (costPrice: number, sellingPrice: number) => {\n    if (costPrice === 0) return 0;\n    return ((sellingPrice - costPrice) / costPrice) * 100;\n  };\n\n  const getStockStatus = (stock: number, minStock: number) => {\n    if (stock === 0) return { class: 'danger', text: 'หมด' };\n    if (stock <= minStock) return { class: 'warning', text: 'ใกล้หมด' };\n    return { class: 'success', text: 'เพียงพอ' };\n  };\n\n  const filteredProducts = products.filter(product =>\n    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    product.productCode.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    product.category.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  if (loading) {\n    return (\n      <div className=\"content-header\">\n        <div className=\"container-fluid\">\n          <div className=\"row mb-2\">\n            <div className=\"col-sm-6\">\n              <h1 className=\"m-0\">จัดการสินค้า</h1>\n            </div>\n          </div>\n        </div>\n        <section className=\"content\">\n          <div className=\"container-fluid\">\n            <div className=\"d-flex justify-content-center\">\n              <div className=\"spinner-border text-primary\" role=\"status\">\n                <span className=\"sr-only\">กำลังโหลด...</span>\n              </div>\n            </div>\n          </div>\n        </section>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      {/* Content Header */}\n      <div className=\"content-header\">\n        <div className=\"container-fluid\">\n          <div className=\"row mb-2\">\n            <div className=\"col-sm-6\">\n              <h1 className=\"m-0\">\n                <i className=\"fas fa-box mr-2\"></i>\n                จัดการสินค้า\n              </h1>\n            </div>\n            <div className=\"col-sm-6\">\n              <ol className=\"breadcrumb float-sm-right\">\n                <li className=\"breadcrumb-item\"><Link to=\"/\">หน้าแรก</Link></li>\n                <li className=\"breadcrumb-item active\">จัดการสินค้า</li>\n              </ol>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <section className=\"content\">\n        <div className=\"container-fluid\">\n          <div className=\"row\">\n            <div className=\"col-12\">\n              <div className=\"card\">\n                <div className=\"card-header\">\n                  <h3 className=\"card-title\">รายการสินค้า</h3>\n                  <div className=\"card-tools\">\n                    <div className=\"input-group input-group-sm\" style={{width: '250px'}}>\n                      <input\n                        type=\"text\"\n                        className=\"form-control float-right\"\n                        placeholder=\"ค้นหาสินค้า...\"\n                        value={searchTerm}\n                        onChange={(e) => setSearchTerm(e.target.value)}\n                      />\n                      <div className=\"input-group-append\">\n                        <button type=\"submit\" className=\"btn btn-default\">\n                          <i className=\"fas fa-search\"></i>\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                <div className=\"card-body table-responsive p-0\">\n                  <table className=\"table table-hover text-nowrap\">\n                    <thead>\n                      <tr>\n                        <th>รหัสสินค้า</th>\n                        <th>ชื่อสินค้า</th>\n                        <th>หมวดหมู่</th>\n                        <th>ราคาทุน</th>\n                        <th>ราคาขาย</th>\n                        <th>กำไร</th>\n                        <th>สต๊อก</th>\n                        <th>สถานะ</th>\n                        <th>จัดการ</th>\n                      </tr>\n                    </thead>\n                    <tbody>\n                      {filteredProducts.map((product) => {\n                        const stockStatus = getStockStatus(product.stock, product.minStock);\n                        const profitMargin = calculateProfitMargin(product.costPrice, product.sellingPrice);\n                        \n                        return (\n                          <tr key={product._id}>\n                            <td>\n                              <span className=\"badge badge-primary\">{product.productCode}</span>\n                            </td>\n                            <td>\n                              <strong>{product.name}</strong>\n                              {product.description && (\n                                <>\n                                  <br />\n                                  <small className=\"text-muted\">{product.description}</small>\n                                </>\n                              )}\n                            </td>\n                            <td>\n                              <span className=\"badge badge-secondary\">{product.category}</span>\n                            </td>\n                            <td>{formatCurrency(product.costPrice)}</td>\n                            <td>{formatCurrency(product.sellingPrice)}</td>\n                            <td>\n                              <span className=\"text-success\">\n                                {formatCurrency(product.sellingPrice - product.costPrice)}\n                              </span>\n                              <>\n                                <br />\n                                <small className=\"text-muted\">\n                                  ({profitMargin.toFixed(1)}%)\n                                </small>\n                              </>\n                            </td>\n                            <td>\n                              <strong>{product.stock}</strong> {product.unit}\n                              <>\n                                <br />\n                                <small className=\"text-muted\">\n                                  ขั้นต่ำ: {product.minStock} {product.unit}\n                                </small>\n                              </>\n                            </td>\n                            <td>\n                              <span className={`badge badge-${stockStatus.class}`}>\n                                {stockStatus.text}\n                              </span>\n                              <>\n                                <br />\n                                {product.isActive ? (\n                                  <span className=\"badge badge-success\">ใช้งาน</span>\n                                ) : (\n                                  <span className=\"badge badge-danger\">ระงับ</span>\n                                )}\n                              </>\n                            </td>\n                            <td>\n                              <div className=\"btn-group\">\n                                <button className=\"btn btn-sm btn-info\">\n                                  <i className=\"fas fa-eye\"></i>\n                                </button>\n                                <button className=\"btn btn-sm btn-warning\">\n                                  <i className=\"fas fa-edit\"></i>\n                                </button>\n                                <button className=\"btn btn-sm btn-success\">\n                                  <i className=\"fas fa-plus\"></i>\n                                </button>\n                                <button className=\"btn btn-sm btn-danger\">\n                                  <i className=\"fas fa-trash\"></i>\n                                </button>\n                              </div>\n                            </td>\n                          </tr>\n                        );\n                      })}\n                    </tbody>\n                  </table>\n                  \n                  {filteredProducts.length === 0 && (\n                    <div className=\"text-center p-4\">\n                      <i className=\"fas fa-box fa-3x text-muted mb-3\"></i>\n                      <h5 className=\"text-muted\">ไม่พบข้อมูลสินค้า</h5>\n                      <p className=\"text-muted\">\n                        {searchTerm ? 'ไม่พบสินค้าที่ตรงกับคำค้นหา' : 'ยังไม่มีสินค้าในระบบ'}\n                      </p>\n                    </div>\n                  )}\n                </div>\n                <div className=\"card-footer\">\n                  <div className=\"row\">\n                    <div className=\"col-sm-6\">\n                      <span className=\"text-muted\">\n                        แสดง {filteredProducts.length} จาก {products.length} รายการ\n                      </span>\n                    </div>\n                    <div className=\"col-sm-6\">\n                      <button className=\"btn btn-primary float-right\">\n                        <i className=\"fas fa-plus mr-1\"></i>\n                        เพิ่มสินค้าใหม่\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n    </>\n  );\n};\n\nexport default ProductsPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAElD,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,OAAOC,eAAe,MAAM,qBAAqB;AACjD,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExC,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAEhDC,SAAS,CAAC,MAAM;IACdgB,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMI,QAAQ,GAAG,MAAMhB,UAAU,CAACiB,WAAW,CAAC,CAAC;MAC/C,IAAID,QAAQ,CAACE,OAAO,EAAE;QACpB,MAAMC,IAAI,GAAGH,QAAQ,CAACG,IAAkC;QACxDT,WAAW,CAACS,IAAI,CAACA,IAAI,CAAC;MACxB,CAAC,MAAM;QACLlB,eAAe,CAACmB,KAAK,CAAC,gBAAgB,EAAE,8BAA8B,CAAC;MACzE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDnB,eAAe,CAACmB,KAAK,CAAC,gBAAgB,EAAE,8BAA8B,CAAC;IACzE,CAAC,SAAS;MACRR,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMU,cAAc,GAAIC,MAAc,IAAK;IACzC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,MAAMM,qBAAqB,GAAGA,CAACC,SAAiB,EAAEC,YAAoB,KAAK;IACzE,IAAID,SAAS,KAAK,CAAC,EAAE,OAAO,CAAC;IAC7B,OAAQ,CAACC,YAAY,GAAGD,SAAS,IAAIA,SAAS,GAAI,GAAG;EACvD,CAAC;EAED,MAAME,cAAc,GAAGA,CAACC,KAAa,EAAEC,QAAgB,KAAK;IAC1D,IAAID,KAAK,KAAK,CAAC,EAAE,OAAO;MAAEE,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAM,CAAC;IACxD,IAAIH,KAAK,IAAIC,QAAQ,EAAE,OAAO;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAU,CAAC;IACnE,OAAO;MAAED,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAU,CAAC;EAC9C,CAAC;EAED,MAAMC,gBAAgB,GAAG5B,QAAQ,CAAC6B,MAAM,CAACC,OAAO,IAC9CA,OAAO,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7B,UAAU,CAAC4B,WAAW,CAAC,CAAC,CAAC,IAC7DF,OAAO,CAACI,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7B,UAAU,CAAC4B,WAAW,CAAC,CAAC,CAAC,IACpEF,OAAO,CAACK,QAAQ,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7B,UAAU,CAAC4B,WAAW,CAAC,CAAC,CAClE,CAAC;EAED,IAAI9B,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKyC,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B1C,OAAA;QAAKyC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9B1C,OAAA;UAAKyC,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvB1C,OAAA;YAAKyC,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvB1C,OAAA;cAAIyC,SAAS,EAAC,KAAK;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN9C,OAAA;QAASyC,SAAS,EAAC,SAAS;QAAAC,QAAA,eAC1B1C,OAAA;UAAKyC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B1C,OAAA;YAAKyC,SAAS,EAAC,+BAA+B;YAAAC,QAAA,eAC5C1C,OAAA;cAAKyC,SAAS,EAAC,6BAA6B;cAACM,IAAI,EAAC,QAAQ;cAAAL,QAAA,eACxD1C,OAAA;gBAAMyC,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAEV;EAEA,oBACE9C,OAAA,CAAAE,SAAA;IAAAwC,QAAA,gBAEE1C,OAAA;MAAKyC,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7B1C,OAAA;QAAKyC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9B1C,OAAA;UAAKyC,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB1C,OAAA;YAAKyC,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvB1C,OAAA;cAAIyC,SAAS,EAAC,KAAK;cAAAC,QAAA,gBACjB1C,OAAA;gBAAGyC,SAAS,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,4EAErC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACN9C,OAAA;YAAKyC,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvB1C,OAAA;cAAIyC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACvC1C,OAAA;gBAAIyC,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,eAAC1C,OAAA,CAACF,IAAI;kBAACkD,EAAE,EAAC,GAAG;kBAAAN,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChE9C,OAAA;gBAAIyC,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9C,OAAA;MAASyC,SAAS,EAAC,SAAS;MAAAC,QAAA,eAC1B1C,OAAA;QAAKyC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9B1C,OAAA;UAAKyC,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClB1C,OAAA;YAAKyC,SAAS,EAAC,QAAQ;YAAAC,QAAA,eACrB1C,OAAA;cAAKyC,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB1C,OAAA;gBAAKyC,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B1C,OAAA;kBAAIyC,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5C9C,OAAA;kBAAKyC,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACzB1C,OAAA;oBAAKyC,SAAS,EAAC,4BAA4B;oBAACnB,KAAK,EAAE;sBAAC2B,KAAK,EAAE;oBAAO,CAAE;oBAAAP,QAAA,gBAClE1C,OAAA;sBACEkD,IAAI,EAAC,MAAM;sBACXT,SAAS,EAAC,0BAA0B;sBACpCU,WAAW,EAAC,uEAAgB;sBAC5BC,KAAK,EAAE3C,UAAW;sBAClB4C,QAAQ,EAAGC,CAAC,IAAK5C,aAAa,CAAC4C,CAAC,CAACC,MAAM,CAACH,KAAK;oBAAE;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD,CAAC,eACF9C,OAAA;sBAAKyC,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,eACjC1C,OAAA;wBAAQkD,IAAI,EAAC,QAAQ;wBAACT,SAAS,EAAC,iBAAiB;wBAAAC,QAAA,eAC/C1C,OAAA;0BAAGyC,SAAS,EAAC;wBAAe;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3B;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN9C,OAAA;gBAAKyC,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,gBAC7C1C,OAAA;kBAAOyC,SAAS,EAAC,+BAA+B;kBAAAC,QAAA,gBAC9C1C,OAAA;oBAAA0C,QAAA,eACE1C,OAAA;sBAAA0C,QAAA,gBACE1C,OAAA;wBAAA0C,QAAA,EAAI;sBAAU;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACnB9C,OAAA;wBAAA0C,QAAA,EAAI;sBAAU;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACnB9C,OAAA;wBAAA0C,QAAA,EAAI;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACjB9C,OAAA;wBAAA0C,QAAA,EAAI;sBAAO;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAChB9C,OAAA;wBAAA0C,QAAA,EAAI;sBAAO;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAChB9C,OAAA;wBAAA0C,QAAA,EAAI;sBAAI;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACb9C,OAAA;wBAAA0C,QAAA,EAAI;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACd9C,OAAA;wBAAA0C,QAAA,EAAI;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACd9C,OAAA;wBAAA0C,QAAA,EAAI;sBAAM;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eACR9C,OAAA;oBAAA0C,QAAA,EACGT,gBAAgB,CAACuB,GAAG,CAAErB,OAAO,IAAK;sBACjC,MAAMsB,WAAW,GAAG7B,cAAc,CAACO,OAAO,CAACN,KAAK,EAAEM,OAAO,CAACL,QAAQ,CAAC;sBACnE,MAAM4B,YAAY,GAAGjC,qBAAqB,CAACU,OAAO,CAACT,SAAS,EAAES,OAAO,CAACR,YAAY,CAAC;sBAEnF,oBACE3B,OAAA;wBAAA0C,QAAA,gBACE1C,OAAA;0BAAA0C,QAAA,eACE1C,OAAA;4BAAMyC,SAAS,EAAC,qBAAqB;4BAAAC,QAAA,EAAEP,OAAO,CAACI;0BAAW;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChE,CAAC,eACL9C,OAAA;0BAAA0C,QAAA,gBACE1C,OAAA;4BAAA0C,QAAA,EAASP,OAAO,CAACC;0BAAI;4BAAAO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAS,CAAC,EAC9BX,OAAO,CAACwB,WAAW,iBAClB3D,OAAA,CAAAE,SAAA;4BAAAwC,QAAA,gBACE1C,OAAA;8BAAA2C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACN9C,OAAA;8BAAOyC,SAAS,EAAC,YAAY;8BAAAC,QAAA,EAAEP,OAAO,CAACwB;4BAAW;8BAAAhB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC;0BAAA,eAC3D,CACH;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC,eACL9C,OAAA;0BAAA0C,QAAA,eACE1C,OAAA;4BAAMyC,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,EAAEP,OAAO,CAACK;0BAAQ;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/D,CAAC,eACL9C,OAAA;0BAAA0C,QAAA,EAAKxB,cAAc,CAACiB,OAAO,CAACT,SAAS;wBAAC;0BAAAiB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAC5C9C,OAAA;0BAAA0C,QAAA,EAAKxB,cAAc,CAACiB,OAAO,CAACR,YAAY;wBAAC;0BAAAgB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAC/C9C,OAAA;0BAAA0C,QAAA,gBACE1C,OAAA;4BAAMyC,SAAS,EAAC,cAAc;4BAAAC,QAAA,EAC3BxB,cAAc,CAACiB,OAAO,CAACR,YAAY,GAAGQ,OAAO,CAACT,SAAS;0BAAC;4BAAAiB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrD,CAAC,eACP9C,OAAA,CAAAE,SAAA;4BAAAwC,QAAA,gBACE1C,OAAA;8BAAA2C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACN9C,OAAA;8BAAOyC,SAAS,EAAC,YAAY;8BAAAC,QAAA,GAAC,GAC3B,EAACgB,YAAY,CAACE,OAAO,CAAC,CAAC,CAAC,EAAC,IAC5B;4BAAA;8BAAAjB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA,eACR,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC,eACL9C,OAAA;0BAAA0C,QAAA,gBACE1C,OAAA;4BAAA0C,QAAA,EAASP,OAAO,CAACN;0BAAK;4BAAAc,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAS,CAAC,KAAC,EAACX,OAAO,CAAC0B,IAAI,eAC9C7D,OAAA,CAAAE,SAAA;4BAAAwC,QAAA,gBACE1C,OAAA;8BAAA2C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACN9C,OAAA;8BAAOyC,SAAS,EAAC,YAAY;8BAAAC,QAAA,GAAC,8CACnB,EAACP,OAAO,CAACL,QAAQ,EAAC,GAAC,EAACK,OAAO,CAAC0B,IAAI;4BAAA;8BAAAlB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACpC,CAAC;0BAAA,eACR,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC,eACL9C,OAAA;0BAAA0C,QAAA,gBACE1C,OAAA;4BAAMyC,SAAS,EAAE,eAAegB,WAAW,CAAC1B,KAAK,EAAG;4BAAAW,QAAA,EACjDe,WAAW,CAACzB;0BAAI;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACb,CAAC,eACP9C,OAAA,CAAAE,SAAA;4BAAAwC,QAAA,gBACE1C,OAAA;8BAAA2C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,EACLX,OAAO,CAAC2B,QAAQ,gBACf9D,OAAA;8BAAMyC,SAAS,EAAC,qBAAqB;8BAAAC,QAAA,EAAC;4BAAM;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,gBAEnD9C,OAAA;8BAAMyC,SAAS,EAAC,oBAAoB;8BAAAC,QAAA,EAAC;4BAAK;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CACjD;0BAAA,eACD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC,eACL9C,OAAA;0BAAA0C,QAAA,eACE1C,OAAA;4BAAKyC,SAAS,EAAC,WAAW;4BAAAC,QAAA,gBACxB1C,OAAA;8BAAQyC,SAAS,EAAC,qBAAqB;8BAAAC,QAAA,eACrC1C,OAAA;gCAAGyC,SAAS,EAAC;8BAAY;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAI;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACxB,CAAC,eACT9C,OAAA;8BAAQyC,SAAS,EAAC,wBAAwB;8BAAAC,QAAA,eACxC1C,OAAA;gCAAGyC,SAAS,EAAC;8BAAa;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAI;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACzB,CAAC,eACT9C,OAAA;8BAAQyC,SAAS,EAAC,wBAAwB;8BAAAC,QAAA,eACxC1C,OAAA;gCAAGyC,SAAS,EAAC;8BAAa;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAI;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACzB,CAAC,eACT9C,OAAA;8BAAQyC,SAAS,EAAC,uBAAuB;8BAAAC,QAAA,eACvC1C,OAAA;gCAAGyC,SAAS,EAAC;8BAAc;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAI;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC1B,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACN;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC;sBAAA,GAlEEX,OAAO,CAAC4B,GAAG;wBAAApB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAmEhB,CAAC;oBAET,CAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAEPb,gBAAgB,CAAC+B,MAAM,KAAK,CAAC,iBAC5BhE,OAAA;kBAAKyC,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAC9B1C,OAAA;oBAAGyC,SAAS,EAAC;kBAAkC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpD9C,OAAA;oBAAIyC,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjD9C,OAAA;oBAAGyC,SAAS,EAAC,YAAY;oBAAAC,QAAA,EACtBjC,UAAU,GAAG,6BAA6B,GAAG;kBAAsB;oBAAAkC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACN9C,OAAA;gBAAKyC,SAAS,EAAC,aAAa;gBAAAC,QAAA,eAC1B1C,OAAA;kBAAKyC,SAAS,EAAC,KAAK;kBAAAC,QAAA,gBAClB1C,OAAA;oBAAKyC,SAAS,EAAC,UAAU;oBAAAC,QAAA,eACvB1C,OAAA;sBAAMyC,SAAS,EAAC,YAAY;sBAAAC,QAAA,GAAC,2BACtB,EAACT,gBAAgB,CAAC+B,MAAM,EAAC,sBAAK,EAAC3D,QAAQ,CAAC2D,MAAM,EAAC,uCACtD;oBAAA;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN9C,OAAA;oBAAKyC,SAAS,EAAC,UAAU;oBAAAC,QAAA,eACvB1C,OAAA;sBAAQyC,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,gBAC7C1C,OAAA;wBAAGyC,SAAS,EAAC;sBAAkB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,8FAEtC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA,eACV,CAAC;AAEP,CAAC;AAAC1C,EAAA,CAvPID,YAAsB;AAAA8D,EAAA,GAAtB9D,YAAsB;AAyP5B,eAAeA,YAAY;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}