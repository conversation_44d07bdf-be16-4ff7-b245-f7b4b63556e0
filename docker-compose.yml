version: '3.8'

services:
  mongodb:
    image: mongo:8.0
    container_name: student_coop_mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: admin123
      MONGO_INITDB_DATABASE: student_coop
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - student_coop_network

  backend:
    build: ./backend
    container_name: student_coop_backend
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=development
      - MONGODB_URI=********************************************************************
      - JWT_SECRET=student_coop_secret_key_2024_very_secure
      - JWT_EXPIRES_IN=7d
      - CORS_ORIGIN=http://localhost:3000
      - ADMIN_EMAIL=<EMAIL>
      - ADMIN_PASSWORD=admin123
      - ADMIN_NAME=ผู้ดูแลระบบ
    depends_on:
      - mongodb
    networks:
      - student_coop_network

volumes:
  mongodb_data:

networks:
  student_coop_network:
    driver: bridge
