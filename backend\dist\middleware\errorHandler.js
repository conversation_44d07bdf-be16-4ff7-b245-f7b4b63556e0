"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.asyncHandler = exports.notFound = exports.errorHandler = void 0;
const errorHandler = (err, req, res, next) => {
    let error = { ...err };
    error.message = err.message;
    console.error('Error:', err);
    if (err.name === 'CastError') {
        const message = 'ไม่พบข้อมูลที่ต้องการ';
        error = { name: 'CastError', message, statusCode: 404 };
    }
    if (err.code === 11000) {
        const field = Object.keys(err.keyValue || {})[0];
        const message = `ข้อมูล ${field} นี้มีอยู่ในระบบแล้ว`;
        error = { name: 'DuplicateError', message, statusCode: 400 };
    }
    if (err.name === 'ValidationError') {
        const mongooseErr = err;
        const message = Object.values(mongooseErr.errors).map(val => val.message).join(', ');
        error = { name: 'ValidationError', message, statusCode: 400 };
    }
    if (err.name === 'JsonWebTokenError') {
        const message = 'Token ไม่ถูกต้อง';
        error = { name: 'JsonWebTokenError', message, statusCode: 401 };
    }
    if (err.name === 'TokenExpiredError') {
        const message = 'Token หมดอายุ';
        error = { name: 'TokenExpiredError', message, statusCode: 401 };
    }
    res.status(error.statusCode || 500).json({
        success: false,
        message: error.message || 'เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์',
        ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
    });
};
exports.errorHandler = errorHandler;
const notFound = (req, res, next) => {
    const error = new Error(`ไม่พบเส้นทาง ${req.originalUrl}`);
    error.statusCode = 404;
    next(error);
};
exports.notFound = notFound;
const asyncHandler = (fn) => (req, res, next) => Promise.resolve(fn(req, res, next)).catch(next);
exports.asyncHandler = asyncHandler;
//# sourceMappingURL=errorHandler.js.map