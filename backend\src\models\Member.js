const mongoose = require('mongoose');

const memberSchema = new mongoose.Schema({
  memberCode: {
    type: String,
    required: [true, 'รหัสสมาชิกจำเป็นต้องระบุ'],
    unique: true,
    trim: true,
    uppercase: true,
    match: [/^M\d{6}$/, 'รหัสสมาชิกต้องเป็นรูปแบบ M000001']
  },
  name: {
    type: String,
    required: [true, 'ชื่อจำเป็นต้องระบุ'],
    trim: true,
    maxlength: [100, 'ชื่อต้องไม่เกิน 100 ตัวอักษร']
  },
  email: {
    type: String,
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'รูปแบบอีเมลไม่ถูกต้อง']
  },
  phone: {
    type: String,
    trim: true,
    match: [/^[0-9]{10}$/, 'เบอร์โทรศัพท์ต้องเป็นตัวเลข 10 หลัก']
  },
  studentId: {
    type: String,
    trim: true,
    maxlength: [20, 'รหัสนักเรียนต้องไม่เกิน 20 ตัวอักษร']
  },
  class: {
    type: String,
    trim: true,
    maxlength: [50, 'ชั้นเรียนต้องไม่เกิน 50 ตัวอักษร']
  },
  grade: {
    type: String,
    enum: ['ม.1', 'ม.2', 'ม.3', 'ม.4', 'ม.5', 'ม.6', 'ป.1', 'ป.2', 'ป.3', 'ป.4', 'ป.5', 'ป.6', 'อื่นๆ'],
    default: 'อื่นๆ'
  },
  shares: {
    type: Number,
    required: [true, 'จำนวนหุ้นจำเป็นต้องระบุ'],
    min: [1, 'จำนวนหุ้นต้องมากกว่า 0'],
    max: [1000, 'จำนวนหุ้นต้องไม่เกิน 1000'],
    default: 1
  },
  sharePrice: {
    type: Number,
    required: [true, 'ราคาหุ้นจำเป็นต้องระบุ'],
    min: [1, 'ราคาหุ้นต้องมากกว่า 0'],
    default: 100
  },
  totalPurchase: {
    type: Number,
    default: 0,
    min: [0, 'ยอดซื้อสะสมต้องไม่ติดลบ']
  },
  totalDividend: {
    type: Number,
    default: 0,
    min: [0, 'ปันผลสะสมต้องไม่ติดลบ']
  },
  joinDate: {
    type: Date,
    default: Date.now,
    required: [true, 'วันที่สมัครจำเป็นต้องระบุ']
  },
  isActive: {
    type: Boolean,
    default: true
  },
  address: {
    type: String,
    trim: true,
    maxlength: [500, 'ที่อยู่ต้องไม่เกิน 500 ตัวอักษร']
  },
  dateOfBirth: {
    type: Date
  },
  parentContact: {
    fatherName: String,
    fatherPhone: String,
    motherName: String,
    motherPhone: String,
    guardianName: String,
    guardianPhone: String
  },
  notes: {
    type: String,
    trim: true,
    maxlength: [1000, 'หมายเหตุต้องไม่เกิน 1000 ตัวอักษร']
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended', 'graduated'],
    default: 'active'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
memberSchema.index({ memberCode: 1 });
memberSchema.index({ studentId: 1 });
memberSchema.index({ name: 1 });
memberSchema.index({ email: 1 });
memberSchema.index({ isActive: 1 });
memberSchema.index({ status: 1 });
memberSchema.index({ joinDate: -1 });
memberSchema.index({ grade: 1 });

// Virtual for total investment
memberSchema.virtual('totalInvestment').get(function() {
  return this.shares * this.sharePrice;
});

// Virtual for dividend rate
memberSchema.virtual('dividendRate').get(function() {
  if (this.totalInvestment === 0) return 0;
  return (this.totalDividend / this.totalInvestment) * 100;
});

// Virtual for purchase rate
memberSchema.virtual('purchaseRate').get(function() {
  if (this.totalInvestment === 0) return 0;
  return (this.totalPurchase / this.totalInvestment) * 100;
});

// Pre-save middleware to generate member code
memberSchema.pre('save', async function(next) {
  if (!this.isNew || this.memberCode) return next();
  
  try {
    // Find the last member code
    const lastMember = await this.constructor.findOne(
      { memberCode: { $regex: /^M\d{6}$/ } },
      { memberCode: 1 },
      { sort: { memberCode: -1 } }
    );
    
    let nextNumber = 1;
    if (lastMember && lastMember.memberCode) {
      const lastNumber = parseInt(lastMember.memberCode.substring(1));
      nextNumber = lastNumber + 1;
    }
    
    this.memberCode = `M${nextNumber.toString().padStart(6, '0')}`;
    next();
  } catch (error) {
    next(error);
  }
});

// Instance method to add purchase
memberSchema.methods.addPurchase = function(amount) {
  this.totalPurchase += amount;
  return this.save();
};

// Instance method to add dividend
memberSchema.methods.addDividend = function(amount) {
  this.totalDividend += amount;
  return this.save();
};

// Instance method to update shares
memberSchema.methods.updateShares = function(newShares, newSharePrice = null) {
  this.shares = newShares;
  if (newSharePrice !== null) {
    this.sharePrice = newSharePrice;
  }
  return this.save();
};

// Static method to find active members
memberSchema.statics.findActive = function() {
  return this.find({ isActive: true, status: 'active' });
};

// Static method to find by grade
memberSchema.statics.findByGrade = function(grade) {
  return this.find({ grade, isActive: true });
};

// Static method to get statistics
memberSchema.statics.getStatistics = async function() {
  const stats = await this.aggregate([
    {
      $match: { isActive: true }
    },
    {
      $group: {
        _id: null,
        totalMembers: { $sum: 1 },
        totalShares: { $sum: '$shares' },
        totalInvestment: { $sum: { $multiply: ['$shares', '$sharePrice'] } },
        totalPurchase: { $sum: '$totalPurchase' },
        totalDividend: { $sum: '$totalDividend' },
        avgShares: { $avg: '$shares' },
        avgPurchase: { $avg: '$totalPurchase' }
      }
    }
  ]);
  
  return stats[0] || {
    totalMembers: 0,
    totalShares: 0,
    totalInvestment: 0,
    totalPurchase: 0,
    totalDividend: 0,
    avgShares: 0,
    avgPurchase: 0
  };
};

module.exports = mongoose.model('Member', memberSchema);
