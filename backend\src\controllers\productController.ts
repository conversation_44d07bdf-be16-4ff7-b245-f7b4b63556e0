import { Request, Response } from 'express';
import { Product } from '../models/Product';
import { sendSuccess, sendError, sendCreated, sendNotFound } from '../utils/response';
import { asyncHandler } from '../middleware/errorHandler';
import { getPaginationParams, createPaginatedResponse, getSortOptions } from '../utils/pagination';

// @desc    Get all products
// @route   GET /api/products
// @access  Private
export const getProducts = asyncHandler(async (req: Request, res: Response) => {
  const { page, limit, skip } = getPaginationParams(req.query);
  const { search, sortBy, sortOrder } = req.query;

  // Build search query
  const searchQuery: any = {};
  if (search) {
    searchQuery.$or = [
      { name: { $regex: search, $options: 'i' } },
      { productCode: { $regex: search, $options: 'i' } },
      { category: { $regex: search, $options: 'i' } }
    ];
  }

  // Get sort options
  const sortOptions = getSortOptions(sortBy as string, sortOrder as 'asc' | 'desc') || { createdAt: -1 };

  // Get products with pagination
  const [products, total] = await Promise.all([
    Product.find(searchQuery)
      .sort(sortOptions as any)
      .skip(skip)
      .limit(limit),
    Product.countDocuments(searchQuery)
  ]);

  const paginatedResponse = createPaginatedResponse(products, { page, limit, total });

  sendSuccess(res, 'ดึงข้อมูลสินค้าสำเร็จ', paginatedResponse);
});

// @desc    Get product by ID
// @route   GET /api/products/:id
// @access  Private
export const getProduct = asyncHandler(async (req: Request, res: Response) => {
  const product = await Product.findById(req.params.id);

  if (!product) {
    return sendNotFound(res, 'ไม่พบข้อมูลสินค้า');
  }

  sendSuccess(res, 'ดึงข้อมูลสินค้าสำเร็จ', product);
});

// @desc    Create new product
// @route   POST /api/products
// @access  Private (Admin)
export const createProduct = asyncHandler(async (req: Request, res: Response) => {
  const productData = req.body;

  // Check if product code already exists (if provided)
  if (productData.productCode) {
    const existingProduct = await Product.findOne({ productCode: productData.productCode.toUpperCase() });
    if (existingProduct) {
      return sendError(res, 'รหัสสินค้านี้มีอยู่ในระบบแล้ว', 400);
    }
  }

  const product = await Product.create(productData);

  sendCreated(res, 'สร้างข้อมูลสินค้าสำเร็จ', product);
});

// @desc    Update product
// @route   PUT /api/products/:id
// @access  Private (Admin)
export const updateProduct = asyncHandler(async (req: Request, res: Response) => {
  const productData = req.body;

  // Check if product exists
  const existingProduct = await Product.findById(req.params.id);
  if (!existingProduct) {
    return sendNotFound(res, 'ไม่พบข้อมูลสินค้า');
  }

  // Check if product code already exists (if changed)
  if (productData.productCode && productData.productCode !== existingProduct.productCode) {
    const duplicateProduct = await Product.findOne({ 
      productCode: productData.productCode.toUpperCase(),
      _id: { $ne: req.params.id }
    });
    if (duplicateProduct) {
      return sendError(res, 'รหัสสินค้านี้มีอยู่ในระบบแล้ว', 400);
    }
  }

  const product = await Product.findByIdAndUpdate(
    req.params.id,
    productData,
    { new: true, runValidators: true }
  );

  sendSuccess(res, 'อัปเดตข้อมูลสินค้าสำเร็จ', product);
});

// @desc    Delete product
// @route   DELETE /api/products/:id
// @access  Private (Admin)
export const deleteProduct = asyncHandler(async (req: Request, res: Response) => {
  const product = await Product.findById(req.params.id);

  if (!product) {
    return sendNotFound(res, 'ไม่พบข้อมูลสินค้า');
  }

  await Product.findByIdAndDelete(req.params.id);

  sendSuccess(res, 'ลบข้อมูลสินค้าสำเร็จ');
});

// @desc    Get active products
// @route   GET /api/products/active
// @access  Private
export const getActiveProducts = asyncHandler(async (req: Request, res: Response) => {
  const products = await Product.find({ isActive: true }).sort({ name: 1 });

  sendSuccess(res, 'ดึงข้อมูลสินค้าที่ใช้งานสำเร็จ', products);
});

// @desc    Get low stock products
// @route   GET /api/products/low-stock
// @access  Private
export const getLowStockProducts = asyncHandler(async (req: Request, res: Response) => {
  const products = await Product.find({
    isActive: true,
    $expr: { $lte: ['$stock', '$minStock'] }
  }).sort({ stock: 1 });

  sendSuccess(res, 'ดึงข้อมูลสินค้าที่เหลือน้อยสำเร็จ', products);
});
