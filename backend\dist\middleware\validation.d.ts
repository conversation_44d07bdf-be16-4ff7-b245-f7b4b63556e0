import { Request, Response, NextFunction } from 'express';
export declare const handleValidationErrors: (req: Request, res: Response, next: NextFunction) => Response | void;
export declare const validateUserRegistration: (((req: Request, res: Response, next: NextFunction) => Response | void) | import("express-validator").ValidationChain)[];
export declare const validateUserLogin: (((req: Request, res: Response, next: NextFunction) => Response | void) | import("express-validator").ValidationChain)[];
export declare const validateMember: (((req: Request, res: Response, next: NextFunction) => Response | void) | import("express-validator").ValidationChain)[];
export declare const validateProduct: (((req: Request, res: Response, next: NextFunction) => Response | void) | import("express-validator").ValidationChain)[];
export declare const validateSale: (((req: Request, res: Response, next: NextFunction) => Response | void) | import("express-validator").ValidationChain)[];
export declare const validatePagination: (((req: Request, res: Response, next: NextFunction) => Response | void) | import("express-validator").ValidationChain)[];
export declare const validateDateRange: (((req: Request, res: Response, next: NextFunction) => Response | void) | import("express-validator").ValidationChain)[];
//# sourceMappingURL=validation.d.ts.map