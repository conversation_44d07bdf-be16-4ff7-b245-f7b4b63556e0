"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSortOptions = exports.createPaginatedResponse = exports.getPaginationParams = void 0;
const getPaginationParams = (query) => {
    const page = Math.max(1, parseInt(query.page?.toString() || '1'));
    const limit = Math.min(100, Math.max(1, parseInt(query.limit?.toString() || '10')));
    const skip = (page - 1) * limit;
    return { page, limit, skip };
};
exports.getPaginationParams = getPaginationParams;
const createPaginatedResponse = (data, options) => {
    const { page, limit, total } = options;
    const pages = Math.ceil(total / limit);
    const hasNext = page < pages;
    const hasPrev = page > 1;
    return {
        data,
        pagination: {
            page,
            limit,
            total,
            pages,
            hasNext,
            hasPrev
        }
    };
};
exports.createPaginatedResponse = createPaginatedResponse;
const getSortOptions = (sortBy, sortOrder) => {
    if (!sortBy)
        return {};
    const order = sortOrder === 'desc' ? -1 : 1;
    return { [sortBy]: order };
};
exports.getSortOptions = getSortOptions;
//# sourceMappingURL=pagination.js.map