"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateDateRange = exports.validatePagination = exports.validateSale = exports.validateProduct = exports.validateMember = exports.validateUserLogin = exports.validateUserRegistration = exports.handleValidationErrors = void 0;
const express_validator_1 = require("express-validator");
const handleValidationErrors = (req, res, next) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            message: 'ข้อมูลไม่ถูกต้อง',
            errors: errors.array()
        });
    }
    next();
};
exports.handleValidationErrors = handleValidationErrors;
exports.validateUserRegistration = [
    (0, express_validator_1.body)('email')
        .isEmail()
        .withMessage('รูปแบบอีเมลไม่ถูกต้อง')
        .normalizeEmail(),
    (0, express_validator_1.body)('password')
        .isLength({ min: 6 })
        .withMessage('รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร'),
    (0, express_validator_1.body)('name')
        .trim()
        .isLength({ min: 1, max: 100 })
        .withMessage('ชื่อต้องมี 1-100 ตัวอักษร'),
    (0, express_validator_1.body)('role')
        .optional()
        .isIn(['admin', 'member'])
        .withMessage('บทบาทไม่ถูกต้อง'),
    exports.handleValidationErrors
];
exports.validateUserLogin = [
    (0, express_validator_1.body)('email')
        .isEmail()
        .withMessage('รูปแบบอีเมลไม่ถูกต้อง')
        .normalizeEmail(),
    (0, express_validator_1.body)('password')
        .notEmpty()
        .withMessage('รหัสผ่านจำเป็นต้องระบุ'),
    exports.handleValidationErrors
];
exports.validateMember = [
    (0, express_validator_1.body)('name')
        .trim()
        .isLength({ min: 1, max: 100 })
        .withMessage('ชื่อสมาชิกต้องมี 1-100 ตัวอักษร'),
    (0, express_validator_1.body)('email')
        .optional()
        .isEmail()
        .withMessage('รูปแบบอีเมลไม่ถูกต้อง')
        .normalizeEmail(),
    (0, express_validator_1.body)('phone')
        .optional()
        .matches(/^[0-9]{10}$/)
        .withMessage('เบอร์โทรศัพท์ต้องเป็นตัวเลข 10 หลัก'),
    (0, express_validator_1.body)('address')
        .optional()
        .isLength({ max: 500 })
        .withMessage('ที่อยู่ต้องไม่เกิน 500 ตัวอักษร'),
    (0, express_validator_1.body)('studentId')
        .optional()
        .isLength({ max: 20 })
        .withMessage('รหัสนักเรียนต้องไม่เกิน 20 ตัวอักษร'),
    (0, express_validator_1.body)('class')
        .optional()
        .isLength({ max: 50 })
        .withMessage('ชั้นเรียนต้องไม่เกิน 50 ตัวอักษร'),
    (0, express_validator_1.body)('shares')
        .optional()
        .isInt({ min: 0 })
        .withMessage('จำนวนหุ้นต้องเป็นจำนวนเต็มไม่น้อยกว่า 0'),
    exports.handleValidationErrors
];
exports.validateProduct = [
    (0, express_validator_1.body)('name')
        .trim()
        .isLength({ min: 1, max: 200 })
        .withMessage('ชื่อสินค้าต้องมี 1-200 ตัวอักษร'),
    (0, express_validator_1.body)('description')
        .optional()
        .isLength({ max: 1000 })
        .withMessage('รายละเอียดต้องไม่เกิน 1000 ตัวอักษร'),
    (0, express_validator_1.body)('category')
        .trim()
        .isLength({ min: 1, max: 100 })
        .withMessage('หมวดหมู่ต้องมี 1-100 ตัวอักษร'),
    (0, express_validator_1.body)('costPrice')
        .isFloat({ min: 0 })
        .withMessage('ราคาทุนต้องเป็นตัวเลขไม่น้อยกว่า 0'),
    (0, express_validator_1.body)('sellingPrice')
        .isFloat({ min: 0 })
        .withMessage('ราคาขายต้องเป็นตัวเลขไม่น้อยกว่า 0'),
    (0, express_validator_1.body)('stock')
        .isInt({ min: 0 })
        .withMessage('จำนวนสต๊อกต้องเป็นจำนวนเต็มไม่น้อยกว่า 0'),
    (0, express_validator_1.body)('minStock')
        .optional()
        .isInt({ min: 0 })
        .withMessage('สต๊อกขั้นต่ำต้องเป็นจำนวนเต็มไม่น้อยกว่า 0'),
    (0, express_validator_1.body)('unit')
        .trim()
        .isLength({ min: 1, max: 20 })
        .withMessage('หน่วยนับต้องมี 1-20 ตัวอักษร'),
    exports.handleValidationErrors
];
exports.validateSale = [
    (0, express_validator_1.body)('memberId')
        .optional()
        .trim()
        .notEmpty()
        .withMessage('รหัสสมาชิกไม่ถูกต้อง'),
    (0, express_validator_1.body)('items')
        .isArray({ min: 1 })
        .withMessage('ต้องมีรายการสินค้าอย่างน้อย 1 รายการ'),
    (0, express_validator_1.body)('items.*.productId')
        .notEmpty()
        .withMessage('รหัสสินค้าจำเป็นต้องระบุ'),
    (0, express_validator_1.body)('items.*.quantity')
        .isInt({ min: 1 })
        .withMessage('จำนวนต้องเป็นจำนวนเต็มมากกว่า 0'),
    (0, express_validator_1.body)('paymentMethod')
        .optional()
        .isIn(['cash', 'transfer'])
        .withMessage('วิธีการชำระเงินไม่ถูกต้อง'),
    exports.handleValidationErrors
];
exports.validatePagination = [
    (0, express_validator_1.query)('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('หน้าต้องเป็นจำนวนเต็มมากกว่า 0'),
    (0, express_validator_1.query)('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('จำนวนรายการต่อหน้าต้องอยู่ระหว่าง 1-100'),
    (0, express_validator_1.query)('sortOrder')
        .optional()
        .isIn(['asc', 'desc'])
        .withMessage('ลำดับการเรียงต้องเป็น asc หรือ desc'),
    exports.handleValidationErrors
];
exports.validateDateRange = [
    (0, express_validator_1.query)('startDate')
        .optional()
        .isISO8601()
        .withMessage('วันที่เริ่มต้นไม่ถูกต้อง'),
    (0, express_validator_1.query)('endDate')
        .optional()
        .isISO8601()
        .withMessage('วันที่สิ้นสุดไม่ถูกต้อง'),
    exports.handleValidationErrors
];
//# sourceMappingURL=validation.js.map