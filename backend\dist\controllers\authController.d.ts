import { Request, Response } from 'express';
export declare const register: (req: Request, res: Response, next: import("express").NextFunction) => Promise<any>;
export declare const login: (req: Request, res: Response, next: import("express").NextFunction) => Promise<any>;
export declare const getMe: (req: Request, res: Response, next: import("express").NextFunction) => Promise<any>;
export declare const updateProfile: (req: Request, res: Response, next: import("express").NextFunction) => Promise<any>;
export declare const changePassword: (req: Request, res: Response, next: import("express").NextFunction) => Promise<any>;
export declare const logout: (req: Request, res: Response, next: import("express").NextFunction) => Promise<any>;
//# sourceMappingURL=authController.d.ts.map