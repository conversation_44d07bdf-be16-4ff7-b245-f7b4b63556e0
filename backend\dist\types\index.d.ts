import { Document } from 'mongoose';
export interface IUser extends Document {
    _id: string;
    email: string;
    password: string;
    name: string;
    role: 'admin' | 'member';
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
}
export interface IMember extends Document {
    _id: string;
    memberCode: string;
    name: string;
    email?: string;
    phone?: string;
    address?: string;
    studentId?: string;
    class?: string;
    shares: number;
    totalPurchase: number;
    totalDividend: number;
    isActive: boolean;
    joinDate: Date;
    createdAt: Date;
    updatedAt: Date;
}
export interface IProduct extends Document {
    _id: string;
    productCode: string;
    name: string;
    description?: string;
    category: string;
    costPrice: number;
    sellingPrice: number;
    stock: number;
    minStock: number;
    unit: string;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
}
export interface ISale extends Document {
    _id: string;
    saleCode: string;
    memberId?: string;
    memberName?: string;
    items: ISaleItem[];
    totalAmount: number;
    totalCost: number;
    profit: number;
    paymentMethod: 'cash' | 'transfer';
    saleDate: Date;
    createdBy: string;
    createdAt: Date;
    updatedAt: Date;
}
export interface ISaleItem {
    productId: string;
    productName: string;
    quantity: number;
    costPrice: number;
    sellingPrice: number;
    totalCost: number;
    totalAmount: number;
    profit: number;
}
export interface IShare extends Document {
    _id: string;
    memberId: string;
    sharePrice: number;
    quantity: number;
    totalAmount: number;
    transactionType: 'buy' | 'sell';
    transactionDate: Date;
    createdBy: string;
    createdAt: Date;
    updatedAt: Date;
}
export interface IDividend extends Document {
    _id: string;
    year: number;
    period: string;
    totalProfit: number;
    dividendRate: number;
    profitSharingRate: number;
    totalDividend: number;
    totalProfitSharing: number;
    distributions: IDividendDistribution[];
    status: 'calculated' | 'paid' | 'cancelled';
    calculatedDate: Date;
    paidDate?: Date;
    createdBy: string;
    createdAt: Date;
    updatedAt: Date;
}
export interface IDividendDistribution {
    memberId: string;
    memberName: string;
    shares: number;
    totalPurchase: number;
    dividendAmount: number;
    profitSharingAmount: number;
    totalAmount: number;
}
export interface ISettings extends Document {
    _id: string;
    key: string;
    value: any;
    description?: string;
    updatedBy: string;
    updatedAt: Date;
}
export interface ApiResponse<T = any> {
    success: boolean;
    message: string;
    data?: T;
    error?: string;
}
export interface PaginationQuery {
    page?: number;
    limit?: number;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
export interface PaginatedResponse<T> {
    data: T[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        pages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };
}
export interface DashboardStats {
    totalMembers: number;
    totalProducts: number;
    totalSales: number;
    totalProfit: number;
    todaySales: number;
    todayProfit: number;
    lowStockProducts: number;
    recentSales: ISale[];
}
export interface SalesReport {
    period: string;
    totalSales: number;
    totalProfit: number;
    totalCost: number;
    salesCount: number;
    topProducts: {
        productName: string;
        quantity: number;
        totalAmount: number;
    }[];
}
export interface MemberReport {
    memberId: string;
    memberName: string;
    shares: number;
    totalPurchase: number;
    totalDividend: number;
    lastPurchaseDate?: Date;
}
export interface JWTPayload {
    userId: string;
    email: string;
    role: string;
    iat?: number;
    exp?: number;
}
//# sourceMappingURL=index.d.ts.map