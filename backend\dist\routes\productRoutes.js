"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const productController_1 = require("../controllers/productController");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const router = (0, express_1.Router)();
router.use(auth_1.authenticate);
router.get('/active', productController_1.getActiveProducts);
router.get('/low-stock', productController_1.getLowStockProducts);
router.get('/:id', productController_1.getProduct);
router.get('/', validation_1.validatePagination, productController_1.getProducts);
router.post('/', (0, auth_1.authorize)('admin'), validation_1.validateProduct, productController_1.createProduct);
router.put('/:id', (0, auth_1.authorize)('admin'), validation_1.validateProduct, productController_1.updateProduct);
router.delete('/:id', (0, auth_1.authorize)('admin'), productController_1.deleteProduct);
exports.default = router;
//# sourceMappingURL=productRoutes.js.map