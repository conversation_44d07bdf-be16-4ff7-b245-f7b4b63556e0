import React, { useState, useEffect } from 'react';
import { DashboardStats, Sale } from '../types';
import { apiService } from '../services/api';
import SweetAlertUtils from '../utils/sweetAlert';
import { useAuth } from '../context/AuthContext';
import { Link } from 'react-router-dom';

const DashboardPage: React.FC = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  // const [recentSales, setRecentSales] = useState<Sale[]>([]);
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    fetchDashboardStats();

    // Update time every second
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const fetchDashboardStats = async () => {
    try {
      setLoading(true);
      const response = await apiService.getDashboardStats();
      if (response.success) {
        setStats(response.data as DashboardStats);
      } else {
        SweetAlertUtils.error('เกิดข้อผิดพลาด', 'ไม่สามารถโหลดข้อมูลแดชบอร์ดได้');
      }
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      // Set mock data for demo
      setStats({
        totalMembers: 150,
        totalProducts: 85,
        totalSales: 125000,
        totalProfit: 25000,
        todaySales: 5500,
        todayProfit: 1100,
        lowStockProducts: 8,
        recentSales: []
      });
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('th-TH', {
      style: 'currency',
      currency: 'THB'
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('th-TH').format(num);
  };

  const formatDateTime = (date: Date) => {
    return new Intl.DateTimeFormat('th-TH', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).format(date);
  };

  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return 'สวัสดีตอนเช้า';
    if (hour < 17) return 'สวัสดีตอนบ่าย';
    return 'สวัสดีตอนเย็น';
  };

  const refreshData = () => {
    fetchDashboardStats();
    SweetAlertUtils.toast('success', 'รีเฟรชข้อมูลแล้ว');
  };

  if (loading) {
    return (
      <div className="content-header">
        <div className="container-fluid">
          <div className="row mb-2">
            <div className="col-sm-6">
              <h1 className="m-0">แดชบอร์ด</h1>
            </div>
          </div>
        </div>
        <section className="content">
          <div className="container-fluid">
            <div className="d-flex justify-content-center">
              <div className="spinner-border text-primary" role="status">
                <span className="sr-only">กำลังโหลด...</span>
              </div>
            </div>
          </div>
        </section>
      </div>
    );
  }

  return (
    <>
      {/* Content Header */}
      <div className="content-header">
        <div className="container-fluid">
          <div className="row mb-2">
            <div className="col-sm-6">
              <h1 className="m-0">
                <i className="fas fa-tachometer-alt mr-2"></i>
                แดชบอร์ด
              </h1>
              <p className="text-muted mb-0">
                {getGreeting()}, {user?.name} | {formatDateTime(currentTime)}
              </p>
            </div>
            <div className="col-sm-6">
              <div className="float-sm-right">
                <button
                  className="btn btn-primary btn-sm mr-2"
                  onClick={refreshData}
                >
                  <i className="fas fa-sync-alt mr-1"></i>
                  รีเฟรช
                </button>
                <ol className="breadcrumb mb-0">
                  <li className="breadcrumb-item"><Link to="/">หน้าแรก</Link></li>
                  <li className="breadcrumb-item active">แดชบอร์ด</li>
                </ol>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <section className="content">
        <div className="container-fluid">
          {/* Alert Section */}
          {stats && stats.lowStockProducts > 0 && (
            <div className="row">
              <div className="col-12">
                <div className="alert alert-warning alert-dismissible">
                  <button type="button" className="close" data-dismiss="alert" aria-hidden="true">×</button>
                  <h5><i className="icon fas fa-exclamation-triangle"></i> แจ้งเตือน!</h5>
                  มีสินค้าที่เหลือน้อย {stats.lowStockProducts} รายการ กรุณาเติมสต๊อก
                  <a href="/products?filter=low-stock" className="btn btn-sm btn-warning ml-2">
                    ดูรายการสินค้า
                  </a>
                </div>
              </div>
            </div>
          )}

          {/* Small boxes (Stat box) */}
          <div className="row">
            <div className="col-lg-3 col-6">
              <div className="small-box bg-info">
                <div className="inner">
                  <h3>{stats ? formatNumber(stats.totalMembers) : '0'}</h3>
                  <p>สมาชิกทั้งหมด</p>
                </div>
                <div className="icon">
                  <i className="fas fa-users"></i>
                </div>
                <a href="/members" className="small-box-footer">
                  ดูรายละเอียด <i className="fas fa-arrow-circle-right"></i>
                </a>
              </div>
            </div>

            <div className="col-lg-3 col-6">
              <div className="small-box bg-success">
                <div className="inner">
                  <h3>{stats ? formatNumber(stats.totalProducts) : '0'}</h3>
                  <p>สินค้าทั้งหมด</p>
                </div>
                <div className="icon">
                  <i className="fas fa-box"></i>
                </div>
                <a href="/products" className="small-box-footer">
                  ดูรายละเอียด <i className="fas fa-arrow-circle-right"></i>
                </a>
              </div>
            </div>

            <div className="col-lg-3 col-6">
              <div className="small-box bg-warning">
                <div className="inner">
                  <h3>{stats ? formatCurrency(stats.todaySales) : '฿0'}</h3>
                  <p>ยอดขายวันนี้</p>
                </div>
                <div className="icon">
                  <i className="fas fa-shopping-cart"></i>
                </div>
                <a href="/sales" className="small-box-footer">
                  ดูรายละเอียด <i className="fas fa-arrow-circle-right"></i>
                </a>
              </div>
            </div>

            <div className="col-lg-3 col-6">
              <div className="small-box bg-danger">
                <div className="inner">
                  <h3>{stats ? formatCurrency(stats.todayProfit) : '฿0'}</h3>
                  <p>กำไรวันนี้</p>
                </div>
                <div className="icon">
                  <i className="fas fa-chart-line"></i>
                </div>
                <a href="/reports/sales" className="small-box-footer">
                  ดูรายละเอียด <i className="fas fa-arrow-circle-right"></i>
                </a>
              </div>
            </div>
          </div>

          {/* Charts and Analytics */}
          <div className="row">
            <div className="col-md-8">
              <div className="card">
                <div className="card-header">
                  <h3 className="card-title">
                    <i className="fas fa-chart-line mr-1"></i>
                    สรุปยอดขายและกำไร
                  </h3>
                  <div className="card-tools">
                    <button type="button" className="btn btn-tool" data-card-widget="collapse">
                      <i className="fas fa-minus"></i>
                    </button>
                  </div>
                </div>
                <div className="card-body">
                  <div className="row">
                    <div className="col-md-6">
                      <div className="info-box bg-gradient-primary">
                        <span className="info-box-icon">
                          <i className="fas fa-money-bill-wave"></i>
                        </span>
                        <div className="info-box-content">
                          <span className="info-box-text">ยอดขายรวม</span>
                          <span className="info-box-number">
                            {stats ? formatCurrency(stats.totalSales) : '฿0'}
                          </span>
                          <div className="progress">
                            <div className="progress-bar" style={{width: '70%'}}></div>
                          </div>
                          <span className="progress-description">
                            เพิ่มขึ้น 20% จากเดือนที่แล้ว
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="col-md-6">
                      <div className="info-box bg-gradient-success">
                        <span className="info-box-icon">
                          <i className="fas fa-chart-pie"></i>
                        </span>
                        <div className="info-box-content">
                          <span className="info-box-text">กำไรรวม</span>
                          <span className="info-box-number">
                            {stats ? formatCurrency(stats.totalProfit) : '฿0'}
                          </span>
                          <div className="progress">
                            <div className="progress-bar" style={{width: '85%'}}></div>
                          </div>
                          <span className="progress-description">
                            เพิ่มขึ้น 15% จากเดือนที่แล้ว
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Performance Metrics */}
                  <div className="row mt-3">
                    <div className="col-md-3 col-sm-6 col-12">
                      <div className="info-box">
                        <span className="info-box-icon bg-info"><i className="fas fa-percentage"></i></span>
                        <div className="info-box-content">
                          <span className="info-box-text">อัตรากำไร</span>
                          <span className="info-box-number">
                            {stats ? ((stats.totalProfit / stats.totalSales) * 100).toFixed(1) : '0'}%
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="col-md-3 col-sm-6 col-12">
                      <div className="info-box">
                        <span className="info-box-icon bg-warning"><i className="fas fa-shopping-cart"></i></span>
                        <div className="info-box-content">
                          <span className="info-box-text">ยอดขายเฉลี่ย/วัน</span>
                          <span className="info-box-number">
                            {stats ? formatCurrency(stats.todaySales) : '฿0'}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="col-md-3 col-sm-6 col-12">
                      <div className="info-box">
                        <span className="info-box-icon bg-danger"><i className="fas fa-exclamation-triangle"></i></span>
                        <div className="info-box-content">
                          <span className="info-box-text">สินค้าใกล้หมด</span>
                          <span className="info-box-number">
                            {stats ? stats.lowStockProducts : '0'}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="col-md-3 col-sm-6 col-12">
                      <div className="info-box">
                        <span className="info-box-icon bg-secondary"><i className="fas fa-boxes"></i></span>
                        <div className="info-box-content">
                          <span className="info-box-text">สินค้าทั้งหมด</span>
                          <span className="info-box-number">
                            {stats ? stats.totalProducts : '0'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="col-md-4">
              <div className="card">
                <div className="card-header">
                  <h3 className="card-title">
                    <i className="fas fa-bell mr-1"></i>
                    การแจ้งเตือนและกิจกรรม
                  </h3>
                </div>
                <div className="card-body">
                  {/* System Status */}
                  <div className="callout callout-info">
                    <h5><i className="fas fa-info"></i> สถานะระบบ</h5>
                    <p>ระบบทำงานปกติ | เซิร์ฟเวอร์ออนไลน์</p>
                    <small className="text-muted">อัปเดตล่าสุด: {formatDateTime(currentTime)}</small>
                  </div>

                  {/* Stock Alert */}
                  {stats && stats.lowStockProducts > 0 ? (
                    <div className="callout callout-warning">
                      <h5><i className="fas fa-exclamation-triangle"></i> แจ้งเตือนสต๊อก</h5>
                      <p>มีสินค้าที่เหลือน้อย {stats.lowStockProducts} รายการ</p>
                      <a href="/products?filter=low-stock" className="btn btn-sm btn-warning">
                        <i className="fas fa-eye mr-1"></i>
                        ดูรายการสินค้า
                      </a>
                    </div>
                  ) : (
                    <div className="callout callout-success">
                      <h5><i className="fas fa-check"></i> สต๊อกเพียงพอ</h5>
                      <p>สินค้าทุกรายการมีสต๊อกเพียงพอ</p>
                    </div>
                  )}

                  {/* Quick Stats */}
                  <div className="callout callout-primary">
                    <h5><i className="fas fa-chart-bar"></i> สถิติด่วน</h5>
                    <ul className="list-unstyled">
                      <li><strong>สมาชิกใหม่วันนี้:</strong> 2 คน</li>
                      <li><strong>ยอดขายวันนี้:</strong> {stats ? formatCurrency(stats.todaySales) : '฿0'}</li>
                      <li><strong>กำไรวันนี้:</strong> {stats ? formatCurrency(stats.todayProfit) : '฿0'}</li>
                    </ul>
                  </div>

                  {/* Recent Activity */}
                  <div className="timeline">
                    <div className="time-label">
                      <span className="bg-primary">กิจกรรมล่าสุด</span>
                    </div>
                    <div>
                      <i className="fas fa-shopping-cart bg-success"></i>
                      <div className="timeline-item">
                        <span className="time"><i className="fas fa-clock"></i> 5 นาทีที่แล้ว</span>
                        <h3 className="timeline-header">การขายใหม่</h3>
                        <div className="timeline-body">
                          ขายสินค้า 3 รายการ มูลค่า ฿150
                        </div>
                      </div>
                    </div>
                    <div>
                      <i className="fas fa-user bg-info"></i>
                      <div className="timeline-item">
                        <span className="time"><i className="fas fa-clock"></i> 1 ชั่วโมงที่แล้ว</span>
                        <h3 className="timeline-header">สมาชิกใหม่</h3>
                        <div className="timeline-body">
                          นักเรียนใหม่สมัครเป็นสมาชิก
                        </div>
                      </div>
                    </div>
                    <div>
                      <i className="fas fa-clock bg-gray"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="row">
            <div className="col-12">
              <div className="card card-primary card-outline">
                <div className="card-header">
                  <h3 className="card-title">
                    <i className="fas fa-bolt mr-1"></i>
                    การดำเนินการด่วน
                  </h3>
                  <div className="card-tools">
                    <span className="badge badge-primary">4 รายการ</span>
                  </div>
                </div>
                <div className="card-body">
                  <div className="row">
                    <div className="col-lg-3 col-md-6 col-sm-6 col-12 mb-3">
                      <div className="info-box bg-gradient-primary">
                        <span className="info-box-icon">
                          <i className="fas fa-cash-register"></i>
                        </span>
                        <div className="info-box-content">
                          <span className="info-box-text">ขายสินค้า</span>
                          <span className="info-box-number">POS</span>
                          <Link to="/sales/new" className="btn btn-sm btn-light mt-1">
                            เริ่มขาย <i className="fas fa-arrow-right ml-1"></i>
                          </Link>
                        </div>
                      </div>
                    </div>
                    <div className="col-lg-3 col-md-6 col-sm-6 col-12 mb-3">
                      <div className="info-box bg-gradient-success">
                        <span className="info-box-icon">
                          <i className="fas fa-user-plus"></i>
                        </span>
                        <div className="info-box-content">
                          <span className="info-box-text">เพิ่มสมาชิก</span>
                          <span className="info-box-number">สมัคร</span>
                          <Link to="/members/new" className="btn btn-sm btn-light mt-1">
                            สมัครใหม่ <i className="fas fa-arrow-right ml-1"></i>
                          </Link>
                        </div>
                      </div>
                    </div>
                    <div className="col-lg-3 col-md-6 col-sm-6 col-12 mb-3">
                      <div className="info-box bg-gradient-info">
                        <span className="info-box-icon">
                          <i className="fas fa-box-open"></i>
                        </span>
                        <div className="info-box-content">
                          <span className="info-box-text">เพิ่มสินค้า</span>
                          <span className="info-box-number">สต๊อก</span>
                          <Link to="/products/new" className="btn btn-sm btn-light mt-1">
                            เพิ่มสินค้า <i className="fas fa-arrow-right ml-1"></i>
                          </Link>
                        </div>
                      </div>
                    </div>
                    <div className="col-lg-3 col-md-6 col-sm-6 col-12 mb-3">
                      <div className="info-box bg-gradient-warning">
                        <span className="info-box-icon">
                          <i className="fas fa-chart-line"></i>
                        </span>
                        <div className="info-box-content">
                          <span className="info-box-text">ดูรายงาน</span>
                          <span className="info-box-number">สถิติ</span>
                          <Link to="/reports" className="btn btn-sm btn-light mt-1">
                            ดูรายงาน <i className="fas fa-arrow-right ml-1"></i>
                          </Link>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Additional Quick Actions */}
                  <div className="row">
                    <div className="col-md-4">
                      <div className="card card-outline card-secondary">
                        <div className="card-body text-center">
                          <i className="fas fa-money-bill-wave fa-2x text-success mb-2"></i>
                          <h5>จัดการปันผล</h5>
                          <p className="text-muted">คำนวณและจ่ายปันผลให้สมาชิก</p>
                          <Link to="/dividends" className="btn btn-outline-success">
                            <i className="fas fa-calculator mr-1"></i>
                            คำนวณปันผล
                          </Link>
                        </div>
                      </div>
                    </div>
                    <div className="col-md-4">
                      <div className="card card-outline card-secondary">
                        <div className="card-body text-center">
                          <i className="fas fa-chart-pie fa-2x text-info mb-2"></i>
                          <h5>จัดการหุ้น</h5>
                          <p className="text-muted">ตั้งค่าและจัดการหุ้นของสมาชิก</p>
                          <Link to="/shares" className="btn btn-outline-info">
                            <i className="fas fa-cog mr-1"></i>
                            จัดการหุ้น
                          </Link>
                        </div>
                      </div>
                    </div>
                    <div className="col-md-4">
                      <div className="card card-outline card-secondary">
                        <div className="card-body text-center">
                          <i className="fas fa-cogs fa-2x text-warning mb-2"></i>
                          <h5>ตั้งค่าระบบ</h5>
                          <p className="text-muted">กำหนดค่าต่างๆ ของระบบ</p>
                          <Link to="/settings" className="btn btn-outline-warning">
                            <i className="fas fa-tools mr-1"></i>
                            ตั้งค่า
                          </Link>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default DashboardPage;
