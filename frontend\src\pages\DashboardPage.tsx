import React, { useState, useEffect } from 'react';
import { DashboardStats } from '../types';
import { apiService } from '../services/api';
import SweetAlertUtils from '../utils/sweetAlert';

const DashboardPage: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      setLoading(true);
      const response = await apiService.getDashboardStats();
      if (response.success) {
        setStats(response.data as DashboardStats);
      } else {
        SweetAlertUtils.error('เกิดข้อผิดพลาด', 'ไม่สามารถโหลดข้อมูลแดชบอร์ดได้');
      }
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      // Set mock data for demo
      setStats({
        totalMembers: 150,
        totalProducts: 85,
        totalSales: 125000,
        totalProfit: 25000,
        todaySales: 5500,
        todayProfit: 1100,
        lowStockProducts: 8,
        recentSales: []
      });
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('th-TH', {
      style: 'currency',
      currency: 'THB'
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('th-TH').format(num);
  };

  if (loading) {
    return (
      <div className="content-header">
        <div className="container-fluid">
          <div className="row mb-2">
            <div className="col-sm-6">
              <h1 className="m-0">แดชบอร์ด</h1>
            </div>
          </div>
        </div>
        <section className="content">
          <div className="container-fluid">
            <div className="d-flex justify-content-center">
              <div className="spinner-border text-primary" role="status">
                <span className="sr-only">กำลังโหลด...</span>
              </div>
            </div>
          </div>
        </section>
      </div>
    );
  }

  return (
    <>
      {/* Content Header */}
      <div className="content-header">
        <div className="container-fluid">
          <div className="row mb-2">
            <div className="col-sm-6">
              <h1 className="m-0">แดชบอร์ด</h1>
            </div>
            <div className="col-sm-6">
              <ol className="breadcrumb float-sm-right">
                <li className="breadcrumb-item"><a href="/">หน้าแรก</a></li>
                <li className="breadcrumb-item active">แดชบอร์ด</li>
              </ol>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <section className="content">
        <div className="container-fluid">
          {/* Small boxes (Stat box) */}
          <div className="row">
            <div className="col-lg-3 col-6">
              <div className="small-box bg-info">
                <div className="inner">
                  <h3>{stats ? formatNumber(stats.totalMembers) : '0'}</h3>
                  <p>สมาชิกทั้งหมด</p>
                </div>
                <div className="icon">
                  <i className="fas fa-users"></i>
                </div>
                <a href="/members" className="small-box-footer">
                  ดูรายละเอียด <i className="fas fa-arrow-circle-right"></i>
                </a>
              </div>
            </div>

            <div className="col-lg-3 col-6">
              <div className="small-box bg-success">
                <div className="inner">
                  <h3>{stats ? formatNumber(stats.totalProducts) : '0'}</h3>
                  <p>สินค้าทั้งหมด</p>
                </div>
                <div className="icon">
                  <i className="fas fa-box"></i>
                </div>
                <a href="/products" className="small-box-footer">
                  ดูรายละเอียด <i className="fas fa-arrow-circle-right"></i>
                </a>
              </div>
            </div>

            <div className="col-lg-3 col-6">
              <div className="small-box bg-warning">
                <div className="inner">
                  <h3>{stats ? formatCurrency(stats.todaySales) : '฿0'}</h3>
                  <p>ยอดขายวันนี้</p>
                </div>
                <div className="icon">
                  <i className="fas fa-shopping-cart"></i>
                </div>
                <a href="/sales" className="small-box-footer">
                  ดูรายละเอียด <i className="fas fa-arrow-circle-right"></i>
                </a>
              </div>
            </div>

            <div className="col-lg-3 col-6">
              <div className="small-box bg-danger">
                <div className="inner">
                  <h3>{stats ? formatCurrency(stats.todayProfit) : '฿0'}</h3>
                  <p>กำไรวันนี้</p>
                </div>
                <div className="icon">
                  <i className="fas fa-chart-line"></i>
                </div>
                <a href="/reports/sales" className="small-box-footer">
                  ดูรายละเอียด <i className="fas fa-arrow-circle-right"></i>
                </a>
              </div>
            </div>
          </div>

          {/* Second row */}
          <div className="row">
            <div className="col-md-6">
              <div className="card">
                <div className="card-header">
                  <h3 className="card-title">สรุปยอดขายและกำไร</h3>
                </div>
                <div className="card-body">
                  <div className="row">
                    <div className="col-md-6">
                      <div className="info-box">
                        <span className="info-box-icon bg-primary">
                          <i className="fas fa-money-bill-wave"></i>
                        </span>
                        <div className="info-box-content">
                          <span className="info-box-text">ยอดขายรวม</span>
                          <span className="info-box-number">
                            {stats ? formatCurrency(stats.totalSales) : '฿0'}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="col-md-6">
                      <div className="info-box">
                        <span className="info-box-icon bg-success">
                          <i className="fas fa-chart-pie"></i>
                        </span>
                        <div className="info-box-content">
                          <span className="info-box-text">กำไรรวม</span>
                          <span className="info-box-number">
                            {stats ? formatCurrency(stats.totalProfit) : '฿0'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="col-md-6">
              <div className="card">
                <div className="card-header">
                  <h3 className="card-title">การแจ้งเตือน</h3>
                </div>
                <div className="card-body">
                  {stats && stats.lowStockProducts > 0 ? (
                    <div className="alert alert-warning">
                      <h5><i className="icon fas fa-exclamation-triangle"></i> แจ้งเตือน!</h5>
                      มีสินค้าที่เหลือน้อย {stats.lowStockProducts} รายการ
                      <br />
                      <a href="/products?filter=low-stock" className="btn btn-sm btn-warning mt-2">
                        ดูรายการสินค้า
                      </a>
                    </div>
                  ) : (
                    <div className="alert alert-success">
                      <h5><i className="icon fas fa-check"></i> ดีเยี่ยม!</h5>
                      สินค้าทุกรายการมีสต๊อกเพียงพอ
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="row">
            <div className="col-12">
              <div className="card">
                <div className="card-header">
                  <h3 className="card-title">การดำเนินการด่วน</h3>
                </div>
                <div className="card-body">
                  <div className="row">
                    <div className="col-md-3">
                      <a href="/sales/new" className="btn btn-primary btn-block">
                        <i className="fas fa-plus mr-2"></i>
                        ขายสินค้า
                      </a>
                    </div>
                    <div className="col-md-3">
                      <a href="/members/new" className="btn btn-success btn-block">
                        <i className="fas fa-user-plus mr-2"></i>
                        เพิ่มสมาชิก
                      </a>
                    </div>
                    <div className="col-md-3">
                      <a href="/products/new" className="btn btn-info btn-block">
                        <i className="fas fa-box mr-2"></i>
                        เพิ่มสินค้า
                      </a>
                    </div>
                    <div className="col-md-3">
                      <a href="/reports" className="btn btn-warning btn-block">
                        <i className="fas fa-chart-bar mr-2"></i>
                        ดูรายงาน
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default DashboardPage;
