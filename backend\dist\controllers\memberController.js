"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.searchMembers = exports.getActiveMembers = exports.deleteMember = exports.updateMember = exports.createMember = exports.getMember = exports.getMembers = void 0;
const Member_1 = require("../models/Member");
const response_1 = require("../utils/response");
const errorHandler_1 = require("../middleware/errorHandler");
const pagination_1 = require("../utils/pagination");
exports.getMembers = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { page, limit, skip } = (0, pagination_1.getPaginationParams)(req.query);
    const { search, sortBy, sortOrder } = req.query;
    const searchQuery = {};
    if (search) {
        searchQuery.$or = [
            { name: { $regex: search, $options: 'i' } },
            { memberCode: { $regex: search, $options: 'i' } },
            { email: { $regex: search, $options: 'i' } },
            { studentId: { $regex: search, $options: 'i' } }
        ];
    }
    const sortOptions = (0, pagination_1.getSortOptions)(sortBy, sortOrder) || { createdAt: -1 };
    const [members, total] = await Promise.all([
        Member_1.Member.find(searchQuery)
            .sort(sortOptions)
            .skip(skip)
            .limit(limit),
        Member_1.Member.countDocuments(searchQuery)
    ]);
    const paginatedResponse = (0, pagination_1.createPaginatedResponse)(members, { page, limit, total });
    (0, response_1.sendSuccess)(res, 'ดึงข้อมูลสมาชิกสำเร็จ', paginatedResponse);
});
exports.getMember = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const member = await Member_1.Member.findById(req.params.id);
    if (!member) {
        return (0, response_1.sendNotFound)(res, 'ไม่พบข้อมูลสมาชิก');
    }
    (0, response_1.sendSuccess)(res, 'ดึงข้อมูลสมาชิกสำเร็จ', member);
});
exports.createMember = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const memberData = req.body;
    if (memberData.memberCode) {
        const existingMember = await Member_1.Member.findOne({ memberCode: memberData.memberCode.toUpperCase() });
        if (existingMember) {
            return (0, response_1.sendError)(res, 'รหัสสมาชิกนี้มีอยู่ในระบบแล้ว', 400);
        }
    }
    if (memberData.email) {
        const existingEmail = await Member_1.Member.findOne({ email: memberData.email });
        if (existingEmail) {
            return (0, response_1.sendError)(res, 'อีเมลนี้มีอยู่ในระบบแล้ว', 400);
        }
    }
    const member = await Member_1.Member.create(memberData);
    (0, response_1.sendCreated)(res, 'สร้างข้อมูลสมาชิกสำเร็จ', member);
});
exports.updateMember = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const memberData = req.body;
    const existingMember = await Member_1.Member.findById(req.params.id);
    if (!existingMember) {
        return (0, response_1.sendNotFound)(res, 'ไม่พบข้อมูลสมาชิก');
    }
    if (memberData.memberCode && memberData.memberCode !== existingMember.memberCode) {
        const duplicateMember = await Member_1.Member.findOne({
            memberCode: memberData.memberCode.toUpperCase(),
            _id: { $ne: req.params.id }
        });
        if (duplicateMember) {
            return (0, response_1.sendError)(res, 'รหัสสมาชิกนี้มีอยู่ในระบบแล้ว', 400);
        }
    }
    if (memberData.email && memberData.email !== existingMember.email) {
        const duplicateEmail = await Member_1.Member.findOne({
            email: memberData.email,
            _id: { $ne: req.params.id }
        });
        if (duplicateEmail) {
            return (0, response_1.sendError)(res, 'อีเมลนี้มีอยู่ในระบบแล้ว', 400);
        }
    }
    const member = await Member_1.Member.findByIdAndUpdate(req.params.id, memberData, { new: true, runValidators: true });
    (0, response_1.sendSuccess)(res, 'อัปเดตข้อมูลสมาชิกสำเร็จ', member);
});
exports.deleteMember = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const member = await Member_1.Member.findById(req.params.id);
    if (!member) {
        return (0, response_1.sendNotFound)(res, 'ไม่พบข้อมูลสมาชิก');
    }
    await Member_1.Member.findByIdAndDelete(req.params.id);
    (0, response_1.sendSuccess)(res, 'ลบข้อมูลสมาชิกสำเร็จ');
});
exports.getActiveMembers = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const members = await Member_1.Member.findActive().sort({ name: 1 });
    (0, response_1.sendSuccess)(res, 'ดึงข้อมูลสมาชิกที่ใช้งานสำเร็จ', members);
});
exports.searchMembers = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { q } = req.query;
    if (!q) {
        return (0, response_1.sendError)(res, 'กรุณาระบุคำค้นหา', 400);
    }
    const members = await Member_1.Member.find({
        isActive: true,
        $or: [
            { name: { $regex: q, $options: 'i' } },
            { memberCode: { $regex: q, $options: 'i' } }
        ]
    }).limit(10);
    (0, response_1.sendSuccess)(res, 'ค้นหาสมาชิกสำเร็จ', members);
});
//# sourceMappingURL=memberController.js.map