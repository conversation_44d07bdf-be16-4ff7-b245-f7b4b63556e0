{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\coop\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\";\nimport React, { Component } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass ErrorBoundary extends Component {\n  constructor(...args) {\n    super(...args);\n    this.state = {\n      hasError: false\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      hasError: true,\n      error\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    console.error('Uncaught error:', error, errorInfo);\n  }\n  render() {\n    if (this.state.hasError) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-page\",\n        style: {\n          minHeight: '100vh',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          backgroundColor: '#f4f4f4'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-content text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card\",\n            style: {\n              maxWidth: '500px',\n              margin: 'auto'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-header bg-danger text-white\",\n              children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"card-title mb-0\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-exclamation-triangle mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 39,\n                  columnNumber: 19\n                }, this), \"\\u0E40\\u0E01\\u0E34\\u0E14\\u0E02\\u0E49\\u0E2D\\u0E1C\\u0E34\\u0E14\\u0E1E\\u0E25\\u0E32\\u0E14\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 38,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-body\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"\\u0E02\\u0E2D\\u0E2D\\u0E20\\u0E31\\u0E22 \\u0E40\\u0E01\\u0E34\\u0E14\\u0E02\\u0E49\\u0E2D\\u0E1C\\u0E34\\u0E14\\u0E1E\\u0E25\\u0E32\\u0E14\\u0E43\\u0E19\\u0E23\\u0E30\\u0E1A\\u0E1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 44,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-muted\",\n                children: \"\\u0E01\\u0E23\\u0E38\\u0E13\\u0E32\\u0E23\\u0E35\\u0E40\\u0E1F\\u0E23\\u0E0A\\u0E2B\\u0E19\\u0E49\\u0E32\\u0E40\\u0E27\\u0E47\\u0E1A\\u0E2B\\u0E23\\u0E37\\u0E2D\\u0E15\\u0E34\\u0E14\\u0E15\\u0E48\\u0E2D\\u0E1C\\u0E39\\u0E49\\u0E14\\u0E39\\u0E41\\u0E25\\u0E23\\u0E30\\u0E1A\\u0E1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 17\n              }, this), this.state.error && /*#__PURE__*/_jsxDEV(\"details\", {\n                className: \"mt-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n                  className: \"btn btn-sm btn-outline-secondary\",\n                  children: \"\\u0E14\\u0E39\\u0E23\\u0E32\\u0E22\\u0E25\\u0E30\\u0E40\\u0E2D\\u0E35\\u0E22\\u0E14\\u0E02\\u0E49\\u0E2D\\u0E1C\\u0E34\\u0E14\\u0E1E\\u0E25\\u0E32\\u0E14\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 50,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n                  className: \"mt-2 p-2 bg-light text-left\",\n                  style: {\n                    fontSize: '12px'\n                  },\n                  children: this.state.error.toString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 53,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 49,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-footer\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn btn-primary mr-2\",\n                onClick: () => window.location.reload(),\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-sync-alt mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 64,\n                  columnNumber: 19\n                }, this), \"\\u0E23\\u0E35\\u0E40\\u0E1F\\u0E23\\u0E0A\\u0E2B\\u0E19\\u0E49\\u0E32\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn btn-secondary\",\n                onClick: () => window.location.href = '/',\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-home mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 71,\n                  columnNumber: 19\n                }, this), \"\\u0E01\\u0E25\\u0E31\\u0E1A\\u0E2B\\u0E19\\u0E49\\u0E32\\u0E41\\u0E23\\u0E01\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this);\n    }\n    return this.props.children;\n  }\n}\nexport default ErrorBoundary;", "map": {"version": 3, "names": ["React", "Component", "jsxDEV", "_jsxDEV", "Error<PERSON>ou<PERSON><PERSON>", "constructor", "args", "state", "<PERSON><PERSON><PERSON><PERSON>", "getDerivedStateFromError", "error", "componentDidCatch", "errorInfo", "console", "render", "className", "style", "minHeight", "display", "alignItems", "justifyContent", "backgroundColor", "children", "max<PERSON><PERSON><PERSON>", "margin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "toString", "onClick", "window", "location", "reload", "href", "props"], "sources": ["C:/Users/<USER>/Documents/augment-projects/coop/frontend/src/components/ErrorBoundary.tsx"], "sourcesContent": ["import React, { Component, ErrorInfo, ReactNode } from 'react';\n\ninterface Props {\n  children: ReactNode;\n}\n\ninterface State {\n  hasError: boolean;\n  error?: Error;\n}\n\nclass ErrorBoundary extends Component<Props, State> {\n  public state: State = {\n    hasError: false\n  };\n\n  public static getDerivedStateFromError(error: Error): State {\n    return { hasError: true, error };\n  }\n\n  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {\n    console.error('Uncaught error:', error, errorInfo);\n  }\n\n  public render() {\n    if (this.state.hasError) {\n      return (\n        <div className=\"error-page\" style={{ \n          minHeight: '100vh', \n          display: 'flex', \n          alignItems: 'center', \n          justifyContent: 'center',\n          backgroundColor: '#f4f4f4'\n        }}>\n          <div className=\"error-content text-center\">\n            <div className=\"card\" style={{ maxWidth: '500px', margin: 'auto' }}>\n              <div className=\"card-header bg-danger text-white\">\n                <h3 className=\"card-title mb-0\">\n                  <i className=\"fas fa-exclamation-triangle mr-2\"></i>\n                  เกิดข้อผิดพลาด\n                </h3>\n              </div>\n              <div className=\"card-body\">\n                <h4>ขออภัย เกิดข้อผิดพลาดในระบบ</h4>\n                <p className=\"text-muted\">\n                  กรุณารีเฟรชหน้าเว็บหรือติดต่อผู้ดูแลระบบ\n                </p>\n                {this.state.error && (\n                  <details className=\"mt-3\">\n                    <summary className=\"btn btn-sm btn-outline-secondary\">\n                      ดูรายละเอียดข้อผิดพลาด\n                    </summary>\n                    <pre className=\"mt-2 p-2 bg-light text-left\" style={{ fontSize: '12px' }}>\n                      {this.state.error.toString()}\n                    </pre>\n                  </details>\n                )}\n              </div>\n              <div className=\"card-footer\">\n                <button \n                  className=\"btn btn-primary mr-2\"\n                  onClick={() => window.location.reload()}\n                >\n                  <i className=\"fas fa-sync-alt mr-1\"></i>\n                  รีเฟรชหน้า\n                </button>\n                <button \n                  className=\"btn btn-secondary\"\n                  onClick={() => window.location.href = '/'}\n                >\n                  <i className=\"fas fa-home mr-1\"></i>\n                  กลับหน้าแรก\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nexport default ErrorBoundary;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,QAA8B,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAW/D,MAAMC,aAAa,SAASH,SAAS,CAAe;EAAAI,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAA,KAC3CC,KAAK,GAAU;MACpBC,QAAQ,EAAE;IACZ,CAAC;EAAA;EAED,OAAcC,wBAAwBA,CAACC,KAAY,EAAS;IAC1D,OAAO;MAAEF,QAAQ,EAAE,IAAI;MAAEE;IAAM,CAAC;EAClC;EAEOC,iBAAiBA,CAACD,KAAY,EAAEE,SAAoB,EAAE;IAC3DC,OAAO,CAACH,KAAK,CAAC,iBAAiB,EAAEA,KAAK,EAAEE,SAAS,CAAC;EACpD;EAEOE,MAAMA,CAAA,EAAG;IACd,IAAI,IAAI,CAACP,KAAK,CAACC,QAAQ,EAAE;MACvB,oBACEL,OAAA;QAAKY,SAAS,EAAC,YAAY;QAACC,KAAK,EAAE;UACjCC,SAAS,EAAE,OAAO;UAClBC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBC,eAAe,EAAE;QACnB,CAAE;QAAAC,QAAA,eACAnB,OAAA;UAAKY,SAAS,EAAC,2BAA2B;UAAAO,QAAA,eACxCnB,OAAA;YAAKY,SAAS,EAAC,MAAM;YAACC,KAAK,EAAE;cAAEO,QAAQ,EAAE,OAAO;cAAEC,MAAM,EAAE;YAAO,CAAE;YAAAF,QAAA,gBACjEnB,OAAA;cAAKY,SAAS,EAAC,kCAAkC;cAAAO,QAAA,eAC/CnB,OAAA;gBAAIY,SAAS,EAAC,iBAAiB;gBAAAO,QAAA,gBAC7BnB,OAAA;kBAAGY,SAAS,EAAC;gBAAkC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,wFAEtD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACNzB,OAAA;cAAKY,SAAS,EAAC,WAAW;cAAAO,QAAA,gBACxBnB,OAAA;gBAAAmB,QAAA,EAAI;cAA2B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpCzB,OAAA;gBAAGY,SAAS,EAAC,YAAY;gBAAAO,QAAA,EAAC;cAE1B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EACH,IAAI,CAACrB,KAAK,CAACG,KAAK,iBACfP,OAAA;gBAASY,SAAS,EAAC,MAAM;gBAAAO,QAAA,gBACvBnB,OAAA;kBAASY,SAAS,EAAC,kCAAkC;kBAAAO,QAAA,EAAC;gBAEtD;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,eACVzB,OAAA;kBAAKY,SAAS,EAAC,6BAA6B;kBAACC,KAAK,EAAE;oBAAEa,QAAQ,EAAE;kBAAO,CAAE;kBAAAP,QAAA,EACtE,IAAI,CAACf,KAAK,CAACG,KAAK,CAACoB,QAAQ,CAAC;gBAAC;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACV;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNzB,OAAA;cAAKY,SAAS,EAAC,aAAa;cAAAO,QAAA,gBAC1BnB,OAAA;gBACEY,SAAS,EAAC,sBAAsB;gBAChCgB,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;gBAAAZ,QAAA,gBAExCnB,OAAA;kBAAGY,SAAS,EAAC;gBAAsB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,gEAE1C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTzB,OAAA;gBACEY,SAAS,EAAC,mBAAmB;gBAC7BgB,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACE,IAAI,GAAG,GAAI;gBAAAb,QAAA,gBAE1CnB,OAAA;kBAAGY,SAAS,EAAC;gBAAkB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,sEAEtC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV;IAEA,OAAO,IAAI,CAACQ,KAAK,CAACd,QAAQ;EAC5B;AACF;AAEA,eAAelB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}