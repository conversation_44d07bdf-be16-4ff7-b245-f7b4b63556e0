"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getLowStockProducts = exports.getActiveProducts = exports.deleteProduct = exports.updateProduct = exports.createProduct = exports.getProduct = exports.getProducts = void 0;
const Product_1 = require("../models/Product");
const response_1 = require("../utils/response");
const errorHandler_1 = require("../middleware/errorHandler");
const pagination_1 = require("../utils/pagination");
exports.getProducts = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { page, limit, skip } = (0, pagination_1.getPaginationParams)(req.query);
    const { search, sortBy, sortOrder } = req.query;
    const searchQuery = {};
    if (search) {
        searchQuery.$or = [
            { name: { $regex: search, $options: 'i' } },
            { productCode: { $regex: search, $options: 'i' } },
            { category: { $regex: search, $options: 'i' } }
        ];
    }
    const sortOptions = (0, pagination_1.getSortOptions)(sortBy, sortOrder) || { createdAt: -1 };
    const [products, total] = await Promise.all([
        Product_1.Product.find(searchQuery)
            .sort(sortOptions)
            .skip(skip)
            .limit(limit),
        Product_1.Product.countDocuments(searchQuery)
    ]);
    const paginatedResponse = (0, pagination_1.createPaginatedResponse)(products, { page, limit, total });
    (0, response_1.sendSuccess)(res, 'ดึงข้อมูลสินค้าสำเร็จ', paginatedResponse);
});
exports.getProduct = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const product = await Product_1.Product.findById(req.params.id);
    if (!product) {
        return (0, response_1.sendNotFound)(res, 'ไม่พบข้อมูลสินค้า');
    }
    (0, response_1.sendSuccess)(res, 'ดึงข้อมูลสินค้าสำเร็จ', product);
});
exports.createProduct = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const productData = req.body;
    if (productData.productCode) {
        const existingProduct = await Product_1.Product.findOne({ productCode: productData.productCode.toUpperCase() });
        if (existingProduct) {
            return (0, response_1.sendError)(res, 'รหัสสินค้านี้มีอยู่ในระบบแล้ว', 400);
        }
    }
    const product = await Product_1.Product.create(productData);
    (0, response_1.sendCreated)(res, 'สร้างข้อมูลสินค้าสำเร็จ', product);
});
exports.updateProduct = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const productData = req.body;
    const existingProduct = await Product_1.Product.findById(req.params.id);
    if (!existingProduct) {
        return (0, response_1.sendNotFound)(res, 'ไม่พบข้อมูลสินค้า');
    }
    if (productData.productCode && productData.productCode !== existingProduct.productCode) {
        const duplicateProduct = await Product_1.Product.findOne({
            productCode: productData.productCode.toUpperCase(),
            _id: { $ne: req.params.id }
        });
        if (duplicateProduct) {
            return (0, response_1.sendError)(res, 'รหัสสินค้านี้มีอยู่ในระบบแล้ว', 400);
        }
    }
    const product = await Product_1.Product.findByIdAndUpdate(req.params.id, productData, { new: true, runValidators: true });
    (0, response_1.sendSuccess)(res, 'อัปเดตข้อมูลสินค้าสำเร็จ', product);
});
exports.deleteProduct = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const product = await Product_1.Product.findById(req.params.id);
    if (!product) {
        return (0, response_1.sendNotFound)(res, 'ไม่พบข้อมูลสินค้า');
    }
    await Product_1.Product.findByIdAndDelete(req.params.id);
    (0, response_1.sendSuccess)(res, 'ลบข้อมูลสินค้าสำเร็จ');
});
exports.getActiveProducts = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const products = await Product_1.Product.find({ isActive: true }).sort({ name: 1 });
    (0, response_1.sendSuccess)(res, 'ดึงข้อมูลสินค้าที่ใช้งานสำเร็จ', products);
});
exports.getLowStockProducts = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const products = await Product_1.Product.find({
        isActive: true,
        $expr: { $lte: ['$stock', '$minStock'] }
    }).sort({ stock: 1 });
    (0, response_1.sendSuccess)(res, 'ดึงข้อมูลสินค้าที่เหลือน้อยสำเร็จ', products);
});
//# sourceMappingURL=productController.js.map