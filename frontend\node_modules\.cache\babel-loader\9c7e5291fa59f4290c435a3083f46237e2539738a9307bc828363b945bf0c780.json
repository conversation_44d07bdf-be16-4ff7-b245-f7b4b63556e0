{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\coop\\\\frontend\\\\src\\\\components\\\\Loading.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Loading = ({\n  message = 'กำลังโหลด...',\n  fullScreen = false\n}) => {\n  const containerClass = fullScreen ? 'position-fixed w-100 h-100 d-flex align-items-center justify-content-center bg-white' : 'd-flex align-items-center justify-content-center p-4';\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: containerClass,\n    style: {\n      zIndex: fullScreen ? 9999 : 'auto'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-border text-primary mb-3\",\n        role: \"status\",\n        style: {\n          width: '3rem',\n          height: '3rem'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"sr-only\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h5 text-muted\",\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n_c = Loading;\nexport default Loading;\nvar _c;\n$RefreshReg$(_c, \"Loading\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Loading", "message", "fullScreen", "containerClass", "className", "style", "zIndex", "children", "role", "width", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/coop/frontend/src/components/Loading.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface LoadingProps {\n  message?: string;\n  fullScreen?: boolean;\n}\n\nconst Loading: React.FC<LoadingProps> = ({ \n  message = 'กำลังโหลด...', \n  fullScreen = false \n}) => {\n  const containerClass = fullScreen \n    ? 'position-fixed w-100 h-100 d-flex align-items-center justify-content-center bg-white'\n    : 'd-flex align-items-center justify-content-center p-4';\n\n  return (\n    <div className={containerClass} style={{ zIndex: fullScreen ? 9999 : 'auto' }}>\n      <div className=\"text-center\">\n        <div className=\"spinner-border text-primary mb-3\" role=\"status\" style={{ width: '3rem', height: '3rem' }}>\n          <span className=\"sr-only\">Loading...</span>\n        </div>\n        <div className=\"h5 text-muted\">{message}</div>\n      </div>\n    </div>\n  );\n};\n\nexport default Loading;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAO1B,MAAMC,OAA+B,GAAGA,CAAC;EACvCC,OAAO,GAAG,cAAc;EACxBC,UAAU,GAAG;AACf,CAAC,KAAK;EACJ,MAAMC,cAAc,GAAGD,UAAU,GAC7B,sFAAsF,GACtF,sDAAsD;EAE1D,oBACEH,OAAA;IAAKK,SAAS,EAAED,cAAe;IAACE,KAAK,EAAE;MAAEC,MAAM,EAAEJ,UAAU,GAAG,IAAI,GAAG;IAAO,CAAE;IAAAK,QAAA,eAC5ER,OAAA;MAAKK,SAAS,EAAC,aAAa;MAAAG,QAAA,gBAC1BR,OAAA;QAAKK,SAAS,EAAC,kCAAkC;QAACI,IAAI,EAAC,QAAQ;QAACH,KAAK,EAAE;UAAEI,KAAK,EAAE,MAAM;UAAEC,MAAM,EAAE;QAAO,CAAE;QAAAH,QAAA,eACvGR,OAAA;UAAMK,SAAS,EAAC,SAAS;UAAAG,QAAA,EAAC;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eACNf,OAAA;QAAKK,SAAS,EAAC,eAAe;QAAAG,QAAA,EAAEN;MAAO;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACC,EAAA,GAlBIf,OAA+B;AAoBrC,eAAeA,OAAO;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}