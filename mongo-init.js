// MongoDB Initialization Script
// This script runs when MongoDB container starts for the first time

// Switch to student_coop database
db = db.getSiblingDB('student_coop');

// Create collections with validation
db.createCollection('users', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['email', 'password', 'name', 'role'],
      properties: {
        email: {
          bsonType: 'string',
          pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$',
          description: 'must be a valid email address'
        },
        password: {
          bsonType: 'string',
          minLength: 6,
          description: 'must be a string with at least 6 characters'
        },
        name: {
          bsonType: 'string',
          minLength: 1,
          maxLength: 100,
          description: 'must be a string between 1-100 characters'
        },
        role: {
          bsonType: 'string',
          enum: ['admin', 'staff', 'member'],
          description: 'must be one of admin, staff, or member'
        },
        isActive: {
          bsonType: 'bool',
          description: 'must be a boolean'
        }
      }
    }
  }
});

db.createCollection('members', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['memberCode', 'name', 'shares', 'sharePrice'],
      properties: {
        memberCode: {
          bsonType: 'string',
          pattern: '^M\\d{6}$',
          description: 'must be in format M000001'
        },
        name: {
          bsonType: 'string',
          minLength: 1,
          maxLength: 100,
          description: 'must be a string between 1-100 characters'
        },
        shares: {
          bsonType: 'int',
          minimum: 1,
          maximum: 1000,
          description: 'must be an integer between 1-1000'
        },
        sharePrice: {
          bsonType: 'number',
          minimum: 1,
          description: 'must be a positive number'
        }
      }
    }
  }
});

db.createCollection('products', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['productCode', 'name', 'category', 'costPrice', 'sellingPrice'],
      properties: {
        productCode: {
          bsonType: 'string',
          pattern: '^P\\d{6}$',
          description: 'must be in format P000001'
        },
        name: {
          bsonType: 'string',
          minLength: 1,
          maxLength: 200,
          description: 'must be a string between 1-200 characters'
        },
        costPrice: {
          bsonType: 'number',
          minimum: 0,
          description: 'must be a non-negative number'
        },
        sellingPrice: {
          bsonType: 'number',
          minimum: 0,
          description: 'must be a non-negative number'
        }
      }
    }
  }
});

// Create indexes
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ role: 1 });
db.users.createIndex({ isActive: 1 });

db.members.createIndex({ memberCode: 1 }, { unique: true });
db.members.createIndex({ name: 1 });
db.members.createIndex({ isActive: 1 });
db.members.createIndex({ joinDate: -1 });

db.products.createIndex({ productCode: 1 }, { unique: true });
db.products.createIndex({ name: 1 });
db.products.createIndex({ category: 1 });
db.products.createIndex({ isActive: 1 });
db.products.createIndex({ stock: 1 });

print('✅ MongoDB initialization completed');
print('📊 Database: student_coop');
print('📋 Collections created: users, members, products');
print('🔍 Indexes created successfully');
