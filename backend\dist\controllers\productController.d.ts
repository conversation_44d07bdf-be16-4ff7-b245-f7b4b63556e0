import { Request, Response } from 'express';
export declare const getProducts: (req: Request, res: Response, next: import("express").NextFunction) => Promise<any>;
export declare const getProduct: (req: Request, res: Response, next: import("express").NextFunction) => Promise<any>;
export declare const createProduct: (req: Request, res: Response, next: import("express").NextFunction) => Promise<any>;
export declare const updateProduct: (req: Request, res: Response, next: import("express").NextFunction) => Promise<any>;
export declare const deleteProduct: (req: Request, res: Response, next: import("express").NextFunction) => Promise<any>;
export declare const getActiveProducts: (req: Request, res: Response, next: import("express").NextFunction) => Promise<any>;
export declare const getLowStockProducts: (req: Request, res: Response, next: import("express").NextFunction) => Promise<any>;
//# sourceMappingURL=productController.d.ts.map