{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\coop\\\\frontend\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider, useAuth } from './context/AuthContext';\nimport AdminLayout from './layouts/AdminLayout';\nimport LoginPage from './pages/LoginPage';\nimport DashboardPage from './pages/DashboardPage';\nimport MembersPage from './pages/MembersPage';\nimport ProductsPage from './pages/ProductsPage';\n\n// Import AdminLTE CSS\nimport 'admin-lte/dist/css/adminlte.min.css';\nimport '@fortawesome/fontawesome-free/css/all.min.css';\n\n// Protected Route Component\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children\n}) => {\n  _s();\n  const {\n    isAuthenticated,\n    isLoading\n  } = useAuth();\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-center align-items-center\",\n      style: {\n        height: '100vh'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-border text-primary\",\n        role: \"status\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"sr-only\",\n          children: \"\\u0E01\\u0E33\\u0E25\\u0E31\\u0E07\\u0E42\\u0E2B\\u0E25\\u0E14...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this);\n  }\n  return isAuthenticated ? /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false) : /*#__PURE__*/_jsxDEV(Navigate, {\n    to: \"/login\",\n    replace: true\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 46\n  }, this);\n};\n\n// Public Route Component (redirect to dashboard if authenticated)\n_s(ProtectedRoute, \"yb/FJYAIXt7wZoU4a4YvGQ4Nlsc=\", false, function () {\n  return [useAuth];\n});\n_c = ProtectedRoute;\nconst PublicRoute = ({\n  children\n}) => {\n  _s2();\n  const {\n    isAuthenticated,\n    isLoading\n  } = useAuth();\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-center align-items-center\",\n      style: {\n        height: '100vh'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-border text-primary\",\n        role: \"status\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"sr-only\",\n          children: \"\\u0E01\\u0E33\\u0E25\\u0E31\\u0E07\\u0E42\\u0E2B\\u0E25\\u0E14...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this);\n  }\n  return !isAuthenticated ? /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false) : /*#__PURE__*/_jsxDEV(Navigate, {\n    to: \"/\",\n    replace: true\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 47\n  }, this);\n};\n_s2(PublicRoute, \"yb/FJYAIXt7wZoU4a4YvGQ4Nlsc=\", false, function () {\n  return [useAuth];\n});\n_c2 = PublicRoute;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/login\",\n          element: /*#__PURE__*/_jsxDEV(PublicRoute, {\n            children: /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n            children: /*#__PURE__*/_jsxDEV(AdminLayout, {\n              children: /*#__PURE__*/_jsxDEV(DashboardPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/members\",\n          element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n            children: /*#__PURE__*/_jsxDEV(AdminLayout, {\n              children: /*#__PURE__*/_jsxDEV(MembersPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 80,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/products\",\n          element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n            children: /*#__PURE__*/_jsxDEV(AdminLayout, {\n              children: /*#__PURE__*/_jsxDEV(ProductsPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"*\",\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n}\n_c3 = App;\nexport default App;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ProtectedRoute\");\n$RefreshReg$(_c2, \"PublicRoute\");\n$RefreshReg$(_c3, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "AdminLayout", "LoginPage", "DashboardPage", "MembersPage", "ProductsPage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProtectedRoute", "children", "_s", "isAuthenticated", "isLoading", "className", "style", "height", "role", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "_c", "PublicRoute", "_s2", "_c2", "App", "path", "element", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/coop/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider, useAuth } from './context/AuthContext';\nimport AdminLayout from './layouts/AdminLayout';\nimport LoginPage from './pages/LoginPage';\nimport DashboardPage from './pages/DashboardPage';\nimport MembersPage from './pages/MembersPage';\nimport ProductsPage from './pages/ProductsPage';\n\n// Import AdminLTE CSS\nimport 'admin-lte/dist/css/adminlte.min.css';\nimport '@fortawesome/fontawesome-free/css/all.min.css';\n\n// Protected Route Component\nconst ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const { isAuthenticated, isLoading } = useAuth();\n\n  if (isLoading) {\n    return (\n      <div className=\"d-flex justify-content-center align-items-center\" style={{ height: '100vh' }}>\n        <div className=\"spinner-border text-primary\" role=\"status\">\n          <span className=\"sr-only\">กำลังโหลด...</span>\n        </div>\n      </div>\n    );\n  }\n\n  return isAuthenticated ? <>{children}</> : <Navigate to=\"/login\" replace />;\n};\n\n// Public Route Component (redirect to dashboard if authenticated)\nconst PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const { isAuthenticated, isLoading } = useAuth();\n\n  if (isLoading) {\n    return (\n      <div className=\"d-flex justify-content-center align-items-center\" style={{ height: '100vh' }}>\n        <div className=\"spinner-border text-primary\" role=\"status\">\n          <span className=\"sr-only\">กำลังโหลด...</span>\n        </div>\n      </div>\n    );\n  }\n\n  return !isAuthenticated ? <>{children}</> : <Navigate to=\"/\" replace />;\n};\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <Router>\n        <Routes>\n          {/* Public Routes */}\n          <Route\n            path=\"/login\"\n            element={\n              <PublicRoute>\n                <LoginPage />\n              </PublicRoute>\n            }\n          />\n\n          {/* Protected Routes */}\n          <Route\n            path=\"/\"\n            element={\n              <ProtectedRoute>\n                <AdminLayout>\n                  <DashboardPage />\n                </AdminLayout>\n              </ProtectedRoute>\n            }\n          />\n\n          <Route\n            path=\"/members\"\n            element={\n              <ProtectedRoute>\n                <AdminLayout>\n                  <MembersPage />\n                </AdminLayout>\n              </ProtectedRoute>\n            }\n          />\n\n          <Route\n            path=\"/products\"\n            element={\n              <ProtectedRoute>\n                <AdminLayout>\n                  <ProductsPage />\n                </AdminLayout>\n              </ProtectedRoute>\n            }\n          />\n\n          {/* Catch all route */}\n          <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\n        </Routes>\n      </Router>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,YAAY,EAAEC,OAAO,QAAQ,uBAAuB;AAC7D,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,YAAY,MAAM,sBAAsB;;AAE/C;AACA,OAAO,qCAAqC;AAC5C,OAAO,+CAA+C;;AAEtD;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,cAAuD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAChF,MAAM;IAAEC,eAAe;IAAEC;EAAU,CAAC,GAAGd,OAAO,CAAC,CAAC;EAEhD,IAAIc,SAAS,EAAE;IACb,oBACEP,OAAA;MAAKQ,SAAS,EAAC,kDAAkD;MAACC,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAQ,CAAE;MAAAN,QAAA,eAC3FJ,OAAA;QAAKQ,SAAS,EAAC,6BAA6B;QAACG,IAAI,EAAC,QAAQ;QAAAP,QAAA,eACxDJ,OAAA;UAAMQ,SAAS,EAAC,SAAS;UAAAJ,QAAA,EAAC;QAAY;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,OAAOT,eAAe,gBAAGN,OAAA,CAAAE,SAAA;IAAAE,QAAA,EAAGA;EAAQ,gBAAG,CAAC,gBAAGJ,OAAA,CAACT,QAAQ;IAACyB,EAAE,EAAC,QAAQ;IAACC,OAAO;EAAA;IAAAL,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC7E,CAAC;;AAED;AAAAV,EAAA,CAhBMF,cAAuD;EAAA,QACpBV,OAAO;AAAA;AAAAyB,EAAA,GAD1Cf,cAAuD;AAiB7D,MAAMgB,WAAoD,GAAGA,CAAC;EAAEf;AAAS,CAAC,KAAK;EAAAgB,GAAA;EAC7E,MAAM;IAAEd,eAAe;IAAEC;EAAU,CAAC,GAAGd,OAAO,CAAC,CAAC;EAEhD,IAAIc,SAAS,EAAE;IACb,oBACEP,OAAA;MAAKQ,SAAS,EAAC,kDAAkD;MAACC,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAQ,CAAE;MAAAN,QAAA,eAC3FJ,OAAA;QAAKQ,SAAS,EAAC,6BAA6B;QAACG,IAAI,EAAC,QAAQ;QAAAP,QAAA,eACxDJ,OAAA;UAAMQ,SAAS,EAAC,SAAS;UAAAJ,QAAA,EAAC;QAAY;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,OAAO,CAACT,eAAe,gBAAGN,OAAA,CAAAE,SAAA;IAAAE,QAAA,EAAGA;EAAQ,gBAAG,CAAC,gBAAGJ,OAAA,CAACT,QAAQ;IAACyB,EAAE,EAAC,GAAG;IAACC,OAAO;EAAA;IAAAL,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AACzE,CAAC;AAACK,GAAA,CAdID,WAAoD;EAAA,QACjB1B,OAAO;AAAA;AAAA4B,GAAA,GAD1CF,WAAoD;AAgB1D,SAASG,GAAGA,CAAA,EAAG;EACb,oBACEtB,OAAA,CAACR,YAAY;IAAAY,QAAA,eACXJ,OAAA,CAACZ,MAAM;MAAAgB,QAAA,eACLJ,OAAA,CAACX,MAAM;QAAAe,QAAA,gBAELJ,OAAA,CAACV,KAAK;UACJiC,IAAI,EAAC,QAAQ;UACbC,OAAO,eACLxB,OAAA,CAACmB,WAAW;YAAAf,QAAA,eACVJ,OAAA,CAACL,SAAS;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QACd;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGFf,OAAA,CAACV,KAAK;UACJiC,IAAI,EAAC,GAAG;UACRC,OAAO,eACLxB,OAAA,CAACG,cAAc;YAAAC,QAAA,eACbJ,OAAA,CAACN,WAAW;cAAAU,QAAA,eACVJ,OAAA,CAACJ,aAAa;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QACjB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEFf,OAAA,CAACV,KAAK;UACJiC,IAAI,EAAC,UAAU;UACfC,OAAO,eACLxB,OAAA,CAACG,cAAc;YAAAC,QAAA,eACbJ,OAAA,CAACN,WAAW;cAAAU,QAAA,eACVJ,OAAA,CAACH,WAAW;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QACjB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEFf,OAAA,CAACV,KAAK;UACJiC,IAAI,EAAC,WAAW;UAChBC,OAAO,eACLxB,OAAA,CAACG,cAAc;YAAAC,QAAA,eACbJ,OAAA,CAACN,WAAW;cAAAU,QAAA,eACVJ,OAAA,CAACF,YAAY;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QACjB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGFf,OAAA,CAACV,KAAK;UAACiC,IAAI,EAAC,GAAG;UAACC,OAAO,eAAExB,OAAA,CAACT,QAAQ;YAACyB,EAAE,EAAC,GAAG;YAACC,OAAO;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEnB;AAACU,GAAA,GAvDQH,GAAG;AAyDZ,eAAeA,GAAG;AAAC,IAAAJ,EAAA,EAAAG,GAAA,EAAAI,GAAA;AAAAC,YAAA,CAAAR,EAAA;AAAAQ,YAAA,CAAAL,GAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}