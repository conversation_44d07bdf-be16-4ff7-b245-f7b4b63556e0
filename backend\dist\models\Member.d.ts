import mongoose from 'mongoose';
import { IMember } from '../types';
interface IMemberMethods {
    addPurchase(amount: number): Promise<IMember>;
    addDividend(amount: number): Promise<IMember>;
    updateShares(shares: number): Promise<IMember>;
}
interface IMemberModel extends mongoose.Model<IMember, {}, IMemberMethods> {
    findActive(): mongoose.Query<IMember[], IMember>;
    findByCode(code: string): mongoose.Query<IMember | null, IMember>;
}
export declare const Member: IMemberModel;
export default Member;
//# sourceMappingURL=Member.d.ts.map