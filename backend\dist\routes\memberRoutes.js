"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const memberController_1 = require("../controllers/memberController");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const router = (0, express_1.Router)();
router.use(auth_1.authenticate);
router.get('/active', memberController_1.getActiveMembers);
router.get('/search', memberController_1.searchMembers);
router.get('/:id', memberController_1.getMember);
router.get('/', validation_1.validatePagination, memberController_1.getMembers);
router.post('/', (0, auth_1.authorize)('admin'), validation_1.validateMember, memberController_1.createMember);
router.put('/:id', (0, auth_1.authorize)('admin'), validation_1.validateMember, memberController_1.updateMember);
router.delete('/:id', (0, auth_1.authorize)('admin'), memberController_1.deleteMember);
exports.default = router;
//# sourceMappingURL=memberRoutes.js.map