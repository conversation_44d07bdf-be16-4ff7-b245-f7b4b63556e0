"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Member = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const memberSchema = new mongoose_1.Schema({
    memberCode: {
        type: String,
        required: [true, 'รหัสสมาชิกจำเป็นต้องระบุ'],
        unique: true,
        trim: true,
        uppercase: true
    },
    name: {
        type: String,
        required: [true, 'ชื่อสมาชิกจำเป็นต้องระบุ'],
        trim: true,
        maxlength: [100, 'ชื่อต้องไม่เกิน 100 ตัวอักษร']
    },
    email: {
        type: String,
        trim: true,
        lowercase: true,
        match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'รูปแบบอีเมลไม่ถูกต้อง']
    },
    phone: {
        type: String,
        trim: true,
        match: [/^[0-9]{10}$/, 'เบอร์โทรศัพท์ต้องเป็นตัวเลข 10 หลัก']
    },
    address: {
        type: String,
        trim: true,
        maxlength: [500, 'ที่อยู่ต้องไม่เกิน 500 ตัวอักษร']
    },
    studentId: {
        type: String,
        trim: true,
        maxlength: [20, 'รหัสนักเรียนต้องไม่เกิน 20 ตัวอักษร']
    },
    class: {
        type: String,
        trim: true,
        maxlength: [50, 'ชั้นเรียนต้องไม่เกิน 50 ตัวอักษร']
    },
    shares: {
        type: Number,
        default: 0,
        min: [0, 'จำนวนหุ้นต้องไม่น้อยกว่า 0']
    },
    totalPurchase: {
        type: Number,
        default: 0,
        min: [0, 'ยอดซื้อสะสมต้องไม่น้อยกว่า 0']
    },
    totalDividend: {
        type: Number,
        default: 0,
        min: [0, 'ปันผลสะสมต้องไม่น้อยกว่า 0']
    },
    isActive: {
        type: Boolean,
        default: true
    },
    joinDate: {
        type: Date,
        default: Date.now
    }
}, {
    timestamps: true
});
memberSchema.index({ memberCode: 1 });
memberSchema.index({ name: 1 });
memberSchema.index({ email: 1 });
memberSchema.index({ studentId: 1 });
memberSchema.index({ isActive: 1 });
memberSchema.index({ joinDate: 1 });
memberSchema.pre('save', async function (next) {
    if (!this.isNew || this.memberCode)
        return next();
    try {
        const count = await mongoose_1.default.model('Member').countDocuments();
        this.memberCode = `M${String(count + 1).padStart(6, '0')}`;
        next();
    }
    catch (error) {
        next(error);
    }
});
memberSchema.virtual('memberInfo').get(function () {
    return `${this.memberCode} - ${this.name}`;
});
memberSchema.statics.findActive = function () {
    return this.find({ isActive: true });
};
memberSchema.statics.findByCode = function (code) {
    return this.findOne({ memberCode: code.toUpperCase() });
};
memberSchema.methods.addPurchase = function (amount) {
    this.totalPurchase += amount;
    return this.save();
};
memberSchema.methods.addDividend = function (amount) {
    this.totalDividend += amount;
    return this.save();
};
memberSchema.methods.updateShares = function (shares) {
    this.shares = shares;
    return this.save();
};
exports.Member = mongoose_1.default.model('Member', memberSchema);
exports.default = exports.Member;
//# sourceMappingURL=Member.js.map