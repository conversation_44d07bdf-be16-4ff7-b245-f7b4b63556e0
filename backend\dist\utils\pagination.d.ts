import { PaginationQuery, PaginatedResponse } from '../types';
export interface PaginationOptions {
    page: number;
    limit: number;
    total: number;
}
export declare const getPaginationParams: (query: PaginationQuery) => {
    page: number;
    limit: number;
    skip: number;
};
export declare const createPaginatedResponse: <T>(data: T[], options: PaginationOptions) => PaginatedResponse<T>;
export declare const getSortOptions: (sortBy?: string, sortOrder?: "asc" | "desc") => {
    [x: string]: number;
};
//# sourceMappingURL=pagination.d.ts.map