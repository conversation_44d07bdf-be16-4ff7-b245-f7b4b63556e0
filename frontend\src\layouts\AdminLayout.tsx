import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { AuthUtils } from '../utils/auth';
import { useLocation, Link } from 'react-router-dom';

interface AdminLayoutProps {
  children: React.ReactNode;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const { user, logout } = useAuth();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const location = useLocation();

  // Initialize AdminLTE
  useEffect(() => {
    // Load AdminLTE scripts
    const script = document.createElement('script');
    script.src = '/adminlte/js/adminlte.min.js';
    script.async = true;
    document.body.appendChild(script);

    return () => {
      document.body.removeChild(script);
    };
  }, []);

  const handleLogout = () => {
    logout();
  };

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const isActiveRoute = (path: string) => {
    return location.pathname === path;
  };

  return (
    <div className={`wrapper ${sidebarCollapsed ? 'sidebar-collapse' : ''}`}>
      {/* Navbar */}
      <nav className="main-header navbar navbar-expand navbar-white navbar-light">
        {/* Left navbar links */}
        <ul className="navbar-nav">
          <li className="nav-item">
            <a 
              className="nav-link" 
              data-widget="pushmenu" 
              href="#" 
              role="button"
              onClick={(e) => {
                e.preventDefault();
                toggleSidebar();
              }}
            >
              <i className="fas fa-bars"></i>
            </a>
          </li>
          <li className="nav-item d-none d-sm-inline-block">
            <a href="/" className="nav-link">หน้าแรก</a>
          </li>
        </ul>

        {/* Right navbar links */}
        <ul className="navbar-nav ml-auto">
          <li className="nav-item dropdown">
            <a className="nav-link" data-toggle="dropdown" href="#">
              <i className="far fa-user"></i>
              <span className="ml-1">{user?.name}</span>
            </a>
            <div className="dropdown-menu dropdown-menu-lg dropdown-menu-right">
              <span className="dropdown-item-text">
                <strong>{user?.name}</strong>
                <br />
                <small className="text-muted">{user?.email}</small>
              </span>
              <div className="dropdown-divider"></div>
              <a href="/profile" className="dropdown-item">
                <i className="fas fa-user mr-2"></i> โปรไฟล์
              </a>
              <a href="/settings" className="dropdown-item">
                <i className="fas fa-cog mr-2"></i> ตั้งค่า
              </a>
              <div className="dropdown-divider"></div>
              <button 
                className="dropdown-item" 
                onClick={handleLogout}
              >
                <i className="fas fa-sign-out-alt mr-2"></i> ออกจากระบบ
              </button>
            </div>
          </li>
        </ul>
      </nav>

      {/* Main Sidebar Container */}
      <aside className="main-sidebar sidebar-dark-primary elevation-4">
        {/* Brand Logo */}
        <a href="/" className="brand-link">
          <img 
            src="/logo192.png" 
            alt="Logo" 
            className="brand-image img-circle elevation-3" 
            style={{ opacity: 0.8 }}
          />
          <span className="brand-text font-weight-light">สหกรณ์ร้านค้า</span>
        </a>

        {/* Sidebar */}
        <div className="sidebar">
          {/* Sidebar user panel */}
          <div className="user-panel mt-3 pb-3 mb-3 d-flex">
            <div className="image">
              <img 
                src="/default-avatar.png" 
                className="img-circle elevation-2" 
                alt="User Image"
                onError={(e) => {
                  e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiM2Yzc1N2QiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTIgMTRDOC42ODYyOSAxNCA2IDE2LjY4NjMgNiAyMFYyMkgxOFYyMEMxOCAxNi42ODYzIDE1LjMxMzcgMTQgMTIgMTRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4KPC9zdmc+';
                }}
              />
            </div>
            <div className="info">
              <a href="/profile" className="d-block">{user?.name}</a>
            </div>
          </div>

          {/* Sidebar Menu */}
          <nav className="mt-2">
            <ul className="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
              <li className="nav-item">
                <Link to="/" className={`nav-link ${isActiveRoute('/') ? 'active' : ''}`}>
                  <i className="nav-icon fas fa-tachometer-alt"></i>
                  <p>แดชบอร์ด</p>
                </Link>
              </li>

              <li className="nav-item">
                <Link to="/members" className={`nav-link ${isActiveRoute('/members') ? 'active' : ''}`}>
                  <i className="nav-icon fas fa-users"></i>
                  <p>จัดการสมาชิก</p>
                </Link>
              </li>

              <li className="nav-item">
                <Link to="/products" className={`nav-link ${isActiveRoute('/products') ? 'active' : ''}`}>
                  <i className="nav-icon fas fa-box"></i>
                  <p>จัดการสินค้า</p>
                </Link>
              </li>

              <li className="nav-item">
                <a href="/sales" className="nav-link">
                  <i className="nav-icon fas fa-shopping-cart"></i>
                  <p>ขายสินค้า</p>
                </a>
              </li>

              <li className="nav-item">
                <a href="/shares" className="nav-link">
                  <i className="nav-icon fas fa-chart-pie"></i>
                  <p>จัดการหุ้น</p>
                </a>
              </li>

              <li className="nav-item">
                <a href="/dividends" className="nav-link">
                  <i className="nav-icon fas fa-money-bill-wave"></i>
                  <p>ปันผล</p>
                </a>
              </li>

              <li className="nav-item has-treeview">
                <a href="#" className="nav-link">
                  <i className="nav-icon fas fa-chart-bar"></i>
                  <p>
                    รายงาน
                    <i className="fas fa-angle-left right"></i>
                  </p>
                </a>
                <ul className="nav nav-treeview">
                  <li className="nav-item">
                    <a href="/reports/sales" className="nav-link">
                      <i className="far fa-circle nav-icon"></i>
                      <p>รายงานยอดขาย</p>
                    </a>
                  </li>
                  <li className="nav-item">
                    <a href="/reports/members" className="nav-link">
                      <i className="far fa-circle nav-icon"></i>
                      <p>รายงานสมาชิก</p>
                    </a>
                  </li>
                  <li className="nav-item">
                    <a href="/reports/dividends" className="nav-link">
                      <i className="far fa-circle nav-icon"></i>
                      <p>รายงานปันผล</p>
                    </a>
                  </li>
                </ul>
              </li>

              {AuthUtils.isAdmin() && (
                <li className="nav-item">
                  <a href="/settings" className="nav-link">
                    <i className="nav-icon fas fa-cog"></i>
                    <p>ตั้งค่าระบบ</p>
                  </a>
                </li>
              )}
            </ul>
          </nav>
        </div>
      </aside>

      {/* Content Wrapper */}
      <div className="content-wrapper">
        {children}
      </div>

      {/* Footer */}
      <footer className="main-footer">
        <strong>Copyright &copy; 2024 <a href="#">ระบบสหกรณ์ร้านค้านักเรียน</a>.</strong>
        All rights reserved.
        <div className="float-right d-none d-sm-inline-block">
          <b>Version</b> 1.0.0
        </div>
      </footer>
    </div>
  );
};

export default AdminLayout;
