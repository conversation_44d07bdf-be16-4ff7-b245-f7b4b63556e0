<!DOCTYPE html>
<html lang="th">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="ระบบสหกรณ์ร้านค้านักเรียน"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />

    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- AdminLTE CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/css/adminlte.min.css">

    <title>ระบบสหกรณ์ร้านค้านักเรียน</title>

    <style>
      body {
        font-family: 'Source Sans Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      }

      .wrapper {
        min-height: 100vh;
      }

      .content-wrapper {
        min-height: calc(100vh - 57px);
      }

      .main-sidebar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        z-index: 1038;
      }

      .navbar {
        z-index: 1030;
      }

      .sidebar-collapse .main-sidebar {
        margin-left: -250px;
      }

      .sidebar-collapse .content-wrapper {
        margin-left: 0;
      }

      .content-wrapper {
        margin-left: 250px;
        transition: margin-left 0.3s ease-in-out;
      }

      .main-footer {
        margin-left: 250px;
        transition: margin-left 0.3s ease-in-out;
      }

      .sidebar-collapse .main-footer {
        margin-left: 0;
      }

      /* Custom styles for Thai text */
      .nav-link, .dropdown-item, .card-title, .info-box-text {
        font-weight: 400;
      }

      /* Dashboard cards */
      .small-box {
        border-radius: 10px;
        box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
      }

      .small-box .icon {
        font-size: 70px;
      }

      /* Info boxes */
      .info-box {
        border-radius: 10px;
        box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
      }

      /* Cards */
      .card {
        border-radius: 10px;
        box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
      }

      /* Buttons */
      .btn {
        border-radius: 5px;
      }

      /* Sidebar */
      .nav-sidebar .nav-link {
        border-radius: 5px;
        margin: 1px 8px;
      }

      .nav-sidebar .nav-link.active {
        background-color: #007bff;
        color: white;
      }

      /* Loading spinner */
      .spinner-border {
        width: 3rem;
        height: 3rem;
      }

      /* Responsive */
      @media (max-width: 768px) {
        .content-wrapper {
          margin-left: 0;
        }

        .main-footer {
          margin-left: 0;
        }

        .main-sidebar {
          margin-left: -250px;
        }

        .sidebar-open .main-sidebar {
          margin-left: 0;
        }
      }
    </style>
  </head>
  <body class="hold-transition sidebar-mini layout-fixed">
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>

    <!-- Scripts will be loaded by React -->
  </body>
</html>
